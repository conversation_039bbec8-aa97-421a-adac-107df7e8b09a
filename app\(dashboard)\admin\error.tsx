'use client';

import { FallbackProps } from 'react-error-boundary';

export default function AdminError({ error, resetErrorBoundary }: FallbackProps) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900">
          <PERSON><PERSON><PERSON><PERSON>
        </h2>
        <p className="mt-2 text-gray-600">
          {error.message || '<PERSON>r<PERSON><PERSON> kesalahan yang tidak diketahui'}
        </p>
        <button
          onClick={resetErrorBoundary}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <PERSON>ba Lagi
        </button>
      </div>
    </div>
  );
} 
