import { LoadingSpinner } from "@/app/components/ui/loading-spinner";
import { TableSkeleton } from "@/app/components/ui/table-skeleton";

export function LoadingState() {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <LoadingSpinner size="md" className="text-violet-600" />
    </div>
  );
}

interface TableLoadingStateProps {
  rowCount?: number;
}

export function TableLoadingState({ rowCount = 5 }: TableLoadingStateProps) {
  return (
    <div className="w-full">
      <TableSkeleton rowCount={rowCount} variant="violet" />
    </div>
  );
}
