import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:text-red-100 dark:hover:bg-red-600",
        outline: "text-foreground border-border",
        success:
          "border-transparent bg-green-600 text-white hover:bg-green-700 dark:bg-green-700 dark:text-green-100 dark:hover:bg-green-600",
        warning:
          "border-transparent bg-amber-600 text-white hover:bg-amber-700 dark:bg-amber-700 dark:text-amber-100 dark:hover:bg-amber-600",
        info:
          "border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:text-blue-100 dark:hover:bg-blue-600",
        blue:
          "border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:text-blue-100 dark:hover:bg-blue-600",
        purple:
          "border-transparent bg-purple-600 text-white hover:bg-purple-700 dark:bg-purple-700 dark:text-purple-100 dark:hover:bg-purple-600",
        emerald:
          "border-transparent bg-emerald-600 text-white hover:bg-emerald-700 dark:bg-emerald-700 dark:text-emerald-100 dark:hover:bg-emerald-600",
        slate:
          "border-transparent bg-slate-600 text-white hover:bg-slate-700 dark:bg-slate-700 dark:text-slate-100 dark:hover:bg-slate-600",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof badgeVariants> { }

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
