import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { notFound, redirect } from "next/navigation";
import { PaymentStatus } from "@/app/components/payment/payment-status";
import { PaymentForm } from "@/app/components/payment/payment-form";

interface DepositPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function DepositPage({ params }: DepositPageProps) {
  const session = await getSession();
  if (!session?.user) {
    return notFound();
  }

  const { id } = await params;

  const rental = await prisma.rental.findUnique({
    where: {
      id,
      userId: session.user.id
    },
    include: {
      product: true,
      payment: true
    }
  });

  if (!rental || !rental.payment) {
    return notFound();
  }

  // Redirect if deposit is already paid
  if (rental.payment.status === "DEPOSIT_PAID" || rental.payment.status === "FULLY_PAID") {
    redirect(`/user/rentals/${rental.id}`);
  }

  // Only allow access if payment is pending
  if (rental.payment.status !== "DEPOSIT_PENDING") {
    return notFound();
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Pembayaran Deposit</h1>
        <PaymentStatus
          rental={{
            id: rental.id,
            status: rental.status,
            amount: rental.amount,
            product: {
              name: rental.product.name
            },
            payment: rental.payment
          }}
        />

        <PaymentForm
          rentalId={rental.id}
          amount={rental.payment.deposit}
          type="deposit"
        />
      </div>
    </div>
  );
}