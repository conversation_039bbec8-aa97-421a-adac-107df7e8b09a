import { getProductById } from "@/lib/data/product";
import { notFound } from "next/navigation";
import { ProductEditForm } from "@/app/components/product/product-edit-form";

export const metadata = {
  title: "Detail Produk",
  description: "Kelola detail produk - Admin Rental Genset"
};

export default async function ProductDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const product = await getProductById(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="container mx-auto p-6">
      <ProductEditForm
        product={product}
        onCancel={() => {
          // This will be handled by the router.back() in the component
        }}
      />
    </div>
  );
}
