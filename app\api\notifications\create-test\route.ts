import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";

// Enum values sebagai string literals
const NotificationTypes = {
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  NEW_RENTAL: 'NEW_RENTAL',
  RENTAL_CONFIRMED: 'RENTAL_CONFIRMED',
  OPERATION_STARTED: 'OPERATION_STARTED',
  OPERATION_COMPLETED: 'OPERATION_COMPLETED',
  LOW_STOCK: 'LOW_STOCK',
  OVERTIME_DETECTED: 'OVERTIME_DETECTED',
  NEW_PAYMENT: 'NEW_PAYMENT',
  NEW_INVOICE: 'NEW_INVOICE'
} as const;

type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];

export async function POST(request: Request) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { title, message, type } = await request.json();

    // Validasi tipe notifikasi
    const isValidType = Object.values(NotificationTypes).includes(type as NotificationType);
    if (!isValidType) {
      return NextResponse.json(
        { error: "Tipe notifikasi tidak valid" },
        { status: 400 }
      );
    }

    // Create test notification
    const notification = await prisma.notification.create({
      data: {
        userId: session.user.id,
        title: title || "Test Notification",
        message: message || `Test notification created at ${new Date().toLocaleString()}`,
        type: type as NotificationType,
        isRead: false
      }
    });

    return NextResponse.json({
      success: true,
      notification: {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        isRead: notification.isRead,
        createdAt: notification.createdAt
      }
    });
  } catch (error) {
    console.error("[CREATE_TEST_NOTIFICATION_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal membuat test notification" },
      { status: 500 }
    );
  }
}
