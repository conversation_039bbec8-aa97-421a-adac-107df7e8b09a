import { prisma } from "@/lib/config/prisma";
import { getSession } from '@/lib/auth/server';
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Get session using Better Auth
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    // Ambil semua notifikasi yang belum dibaca
    const unreadNotifications = await prisma.notification.findMany({
      where: {
        userId: userId,
        isRead: false
      }
    });

    // Hitung berdasarkan kategori
    const rentalCount = unreadNotifications.filter(
      n => n.type === "NEW_RENTAL" || n.type === "RENTAL_CONFIRMED"
    ).length;

    const paymentCount = unreadNotifications.filter(
      n => n.type === "NEW_PAYMENT" || n.type === "NEW_INVOICE"
    ).length;

    const stockCount = unreadNotifications.filter(
      n => n.type === "LOW_STOCK"
    ).length;

    const otherCount = unreadNotifications.filter(
      n => !["NEW_RENTAL", "RENTAL_CONFIRMED", "NEW_PAYMENT", "NEW_INVOICE", "LOW_STOCK"].includes(n.type)
    ).length;

    return NextResponse.json({
      total: unreadNotifications.length,
      counts: {
        rental: rentalCount,
        payment: paymentCount,
        stock: stockCount,
        other: otherCount
      }
    });
  } catch (error) {
    console.error("[GET_NOTIFICATION_COUNTS_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal mengambil jumlah notifikasi" },
      { status: 500 }
    );
  }
} 
