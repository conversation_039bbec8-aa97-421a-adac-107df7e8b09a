import { useState, useEffect } from "react";
import { useToast } from "@/lib/hooks/use-toast";
import { type RentalDuration, RENTAL_DURATIONS } from "@/lib/utils/constants";
import { getDurationHours } from "@/lib/utils/duration";
import { addressSchema } from "@/lib/validations/rental/schema";
import { useRouter } from "next/navigation";

interface Product {
  id: string;
  name: string;
  price: number;
  capacity: number | string;
  category: string | null;
  status: string;
  imageUrl?: string;
  image?: string;
  description?: string;
}

interface RentalResponse {
  id: string;
  snapToken?: string;
  payment?: {
    id: string;
    status: string;
  };
}

// Deklarasikan interface untuk Snap secara terpisah
interface SnapInterface {
  pay: (token: string, options: {
    onSuccess: () => void;
    onPending: () => void;
    onError: () => void;
    onClose: () => void;
  }) => void;
}

export function useRentalForm(product: Product | null, isKnownUser: boolean) {
  const router = useRouter();
  const { showError, showWarning } = useToast();
  const [startDate, setStartDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState("");
  const [duration, setDuration] = useState<RentalDuration | "">("");
  const [address, setAddress] = useState("");
  const [notes, setNotes] = useState("");
  const [durationDays, setDurationDays] = useState(1);
  const [totalPrice, setTotalPrice] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [arrivalTime, setArrivalTime] = useState("09:00");
  const [coordinates, setCoordinates] = useState<{ lat: number, lng: number} | null>(null);
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});

  // Validasi tanggal dan update endDate berdasarkan durasi
  useEffect(() => {
    if (!startDate) return;
    
    // Validasi tanggal tidak di masa lalu
    const selectedDate = new Date(startDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (isNaN(selectedDate.getTime()) || selectedDate < today) {
      const currentDate = today.toISOString().split('T')[0];
      
      if (startDate !== currentDate) {
        setStartDate(currentDate);
        showWarning("Tanggal mulai minimal adalah hari ini");
      }
      return;
    }
    
    // Update end date jika durasi dipilih
    if (product && duration) {
      try {
        const rentalDuration = duration as RentalDuration;
        const hours = getDurationHours(rentalDuration);
        const days = Math.ceil(hours / 24);
        setDurationDays(days);
        
        // Hitung berdasarkan jumlah shift 8 jam
        const shifts = Math.ceil(hours / 8);
        // Hitung total harga: harga produk * jumlah shift
        setTotalPrice(product.price * shifts);

        // Hitung tanggal selesai berdasarkan durasi
        const start = new Date(startDate);
        const end = new Date(start);
        end.setHours(end.getHours() + hours);
        setEndDate(end.toISOString().split('T')[0]);
      } catch {
        // Gunakan catch tanpa parameter
        showError("Terjadi kesalahan dalam perhitungan");
      }
    }
  }, [duration, product, startDate, showWarning, showError]);

  // Menambahkan useEffect untuk validasi form secara keseluruhan
  useEffect(() => {
    const errors: {[key: string]: string} = {};
    
    if (!startDate) {
      errors.startDate = "Tanggal mulai harus diisi";
    }
    
    if (!duration) {
      errors.duration = "Durasi harus dipilih";
    }
    
    if (!address) {
      errors.address = "Alamat harus diisi";
    } else if (address.trim().length < 10) {
      errors.address = "Alamat terlalu singkat (minimal 10 karakter)";
    }
    
    setFormErrors(errors);
  }, [startDate, duration, address]);

  // Fungsi untuk memeriksa apakah form valid
  const isFormValid = Object.keys(formErrors).length === 0 && !!duration && !!address && address.trim().length >= 10;

  // Menambahkan fungsi validasi alamat menggunakan schema Zod
  const validateAddress = (addr: string) => {
    try {
      const result = addressSchema.safeParse(addr);
      if (!result.success) {
        const errorMsg = result.error.issues[0]?.message || "Alamat terlalu singkat (minimal 10 karakter)";
        setFormErrors(prev => ({...prev, address: errorMsg}));
        return false;
      }
      // Jika validasi berhasil, hapus error dan kembalikan alamat hasil transformasi
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.address;
        return newErrors;
      });
      return result.data;
    } catch (error) {
      console.error("Error validating address:", error);
      setFormErrors(prev => ({...prev, address: "Gagal memvalidasi alamat"}));
      return false;
    }
  };
  
  // Validasi purpose untuk memastikan minimal 10 karakter
  const validatePurpose = (purposeText: string) => {
    if (!purposeText || purposeText.trim().length < 10) {
      return false;
    }
    return purposeText.trim();
  };

  // Fungsi untuk memeriksa semua input
  const validateAllInputs = (): { valid: boolean; purpose?: string } => {
    // Periksa tanggal
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const start = new Date(startDate);
    
    if (isNaN(start.getTime()) || start < today || !startDate || !endDate) {
      showError("Pastikan tanggal mulai valid dan tidak di masa lalu");
      return { valid: false };
    }
    
    if (!duration) {
      showError("Mohon pilih durasi sewa");
      return { valid: false };
    }
    
    // Validasi alamat
    const validatedAddress = validateAddress(address);
    if (!validatedAddress) {
      showError(formErrors.address || "Alamat terlalu singkat (minimal 10 karakter)");
      return { valid: false };
    }
    
    // Buat deskripsi purpose yang memenuhi validasi minimal 10 karakter
    let rentalPurpose = "Penyewaan genset untuk keperluan operasional";
    
    // Tambahkan detail jika ada notes
    if (notes && notes.length > 0) {
      rentalPurpose += ` dengan catatan: ${notes}`;
    } else {
      // Tambahkan detail lain jika tidak ada notes
      rentalPurpose += ` di lokasi ${validatedAddress} dengan durasi ${RENTAL_DURATIONS[duration as RentalDuration].label}`;
    }
    
    // Validasi purpose
    const validatedPurpose = validatePurpose(rentalPurpose);
    if (!validatedPurpose) {
      showError("Tujuan penyewaan terlalu singkat (minimal 10 karakter)");
      return { valid: false };
    }
    
    return { valid: true, purpose: validatedPurpose };
  };

  // Handler untuk error saat order
  const handleOrderError = async (response: Response) => {
    const errorData = await response.json().catch(() => ({}));
    let errorMessage = 'Gagal membuat pesanan';
    
    if (errorData && errorData.error) {
      errorMessage = errorData.error;
    }
    
    showError(`Gagal membuat pesanan: ${errorMessage}`);
    
    setIsSubmitting(false);
  };
  
  // Redirect berdasarkan status user
  const redirectBasedOnUserStatus = (data: RentalResponse) => {
    if (isKnownUser) {
      router.push(`/user/invoices/${data.id}`);
    } else if (data.snapToken) {
      openSnapPayment(data);
    } else {
      router.push(`/user/rentals/${data.id}`);
    }
  };
  
  // Buka payment gateway
  const openSnapPayment = (data: RentalResponse) => {
    if (window.snap && 'pay' in window.snap) {
      (window.snap as unknown as SnapInterface).pay(data.snapToken!, {
        onSuccess: function() {
          // Don't redirect, let GSAP animation handle the flow
          // Animation will show with manual button
        },
        onPending: function() {
          router.push(`/user/payments/${data.id}`);
        },
        onError: function() {
          alert('Terjadi kesalahan dalam proses pembayaran');
        },
        onClose: function() {
          setIsSubmitting(false);
        }
      });
    } else {
      router.push(`/user/payments/${data.id}`);
    }
  };

  // Handler untuk konfirmasi order
  const handleConfirmOrder = async () => {
    try {
      if (!product) {
        showError("Produk tidak ditemukan");
        return;
      }
      
      // Validasi semua input
      const validationResult = validateAllInputs();
      if (!validationResult.valid) return;
      
      setIsSubmitting(true);
      
      const orderData = {
        productId: product.id,
        startDate,
        endDate,
        arrivalTime,
        deliveryType: duration,
        deliveryAddress: validateAddress(address) || "", 
        notes: notes || "",
        purpose: validationResult.purpose,
        quantity: 1,
        totalAmount: totalPrice,
        downPayment: isKnownUser ? 0 : totalPrice * 0.5
      };
      
      // Kirim data ke API
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
      const response = await fetch(`${baseUrl}/api/rentals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });
      
      if (!response.ok) {
        await handleOrderError(response);
        return;
      }
      
      const data = await response.json() as RentalResponse;
      redirectBasedOnUserStatus(data);
      
    } catch (error) {
      showError(error instanceof Error ? error.message : "Gagal membuat pesanan");
      setIsSubmitting(false);
    }
  };

  return {
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    duration,
    setDuration,
    address,
    setAddress,
    notes,
    setNotes,
    durationDays,
    totalPrice,
    isSubmitting,
    arrivalTime,
    setArrivalTime,
    coordinates,
    setCoordinates,
    formErrors,
    isFormValid,
    validateAddress,
    handleConfirmOrder
  };
} 
