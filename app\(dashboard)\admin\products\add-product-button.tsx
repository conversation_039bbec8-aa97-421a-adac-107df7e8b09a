'use client';

import { useState } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle 
} from "@/app/components/ui/dialog";
import { ProductForm } from "@/app/components/product/product-form";
import { useRouter } from "next/navigation";
import { Package } from "lucide-react";

export function AddProductButton() {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  const handleSuccess = () => {
    setIsOpen(false);
    router.refresh();
  };

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        Tambah Produk
      </Button>

      <Dialog open={isOpen} onOpenChange={(open) => setIsOpen(open)}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto dark:bg-gray-900 dark:border-gray-800">
          <DialogHeader className="sticky top-0 bg-white dark:bg-gray-900 z-10 pb-4">
            <div className="flex flex-col items-center space-y-4">
              <div className="h-16 w-16 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
                <Package className="h-8 w-8 text-primary dark:text-primary-foreground" />
              </div>
              <DialogTitle className="text-xl font-bold text-primary dark:text-primary-foreground">
                Tambah Produk Baru
              </DialogTitle>
            </div>
          </DialogHeader>

          <div className="p-3 border rounded-lg dark:bg-gray-800 dark:border-gray-700">
            <ProductForm onSuccess={handleSuccess} />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
