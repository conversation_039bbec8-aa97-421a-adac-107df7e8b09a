import { redirect } from "next/navigation";
import { getSession } from "@/lib/auth/server";

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getSession();

  if (session?.user) {
    // Redirect ke dashboard sesuai role
    redirect(session.user.role === "ADMIN" ? "/admin/dashboard" : "/user/dashboard");
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-950 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-lg">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden p-8 space-y-8 transition-all duration-300">
          {children}
        </div>
      </div>
    </div>
  );
}
