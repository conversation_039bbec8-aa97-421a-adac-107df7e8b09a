"use client";

import { createContext, useContext, useState, ReactNode } from "react";
import { RentalSuccessAnimation } from "@/app/components/rental/rental-success-animation";

interface RentalData {
  id: string;
  productName: string;
  startDate: string;
  endDate: string;
  totalAmount: number;
  deliveryAddress: string;
}

interface RentalAnimationContextType {
  showSuccess: (rentalData: RentalData) => void;
  hideAnimation: () => void;
}

const RentalAnimationContext = createContext<RentalAnimationContextType | undefined>(undefined);

export function useRentalAnimationContext() {
  const context = useContext(RentalAnimationContext);
  if (!context) {
    throw new Error("useRentalAnimationContext must be used within a RentalAnimationProvider");
  }
  return context;
}

interface RentalAnimationProviderProps {
  children: ReactNode;
}

export function RentalAnimationProvider({ children }: RentalAnimationProviderProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [rentalData, setRentalData] = useState<RentalData | null>(null);

  const showSuccess = (data: RentalData) => {
    setRentalData(data);
    setIsVisible(true);
  };

  const hideAnimation = () => {
    setIsVisible(false);
    setRentalData(null);
  };

  return (
    <RentalAnimationContext.Provider value={{ showSuccess, hideAnimation }}>
      {children}
      {rentalData && (
        <RentalSuccessAnimation
          isVisible={isVisible}
          rentalData={rentalData}
          onClose={hideAnimation}
        />
      )}
    </RentalAnimationContext.Provider>
  );
}
