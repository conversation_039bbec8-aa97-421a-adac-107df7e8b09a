import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const alertVariants = cva(
  "relative w-full rounded-lg border p-4",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground border-border",
        destructive: "border-red-200 text-red-800 bg-red-50 dark:border-red-800/50 dark:text-red-200 dark:bg-red-950/50",
        success: "border-green-200 text-green-800 bg-green-50 dark:border-green-800/50 dark:text-green-200 dark:bg-green-950/50",
        warning: "border-amber-200 text-amber-800 bg-amber-50 dark:border-amber-800/50 dark:text-amber-200 dark:bg-amber-950/50",
        info: "border-blue-200 text-blue-800 bg-blue-50 dark:border-blue-800/50 dark:text-blue-200 dark:bg-blue-950/50",
      }
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

interface AlertProps extends React.HTMLAttributes<HTMLDivElement>,
  VariantProps<typeof alertVariants> {
  title?: string;
}

export function Alert({ className, variant, title, children, ...props }: AlertProps) {
  return (
    <div
      role="alert"
      className={cn(alertVariants({ variant }), className)}
      {...props}
    >
      {title && <h5 className="mb-1 font-medium leading-none tracking-tight">{title}</h5>}
      <div className="text-sm [&_p]:leading-relaxed">
        {children}
      </div>
    </div>
  );
}
