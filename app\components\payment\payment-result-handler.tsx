"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { usePaymentAnimationContext } from "@/app/components/providers/payment-animation-provider";
import { formatCurrency } from "@/lib/utils/format";

export function PaymentResultHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showSuccess, showError } = usePaymentAnimationContext();

  useEffect(() => {
    const paymentSuccess = searchParams.get('payment_success');
    const paymentError = searchParams.get('payment_error');
    const paymentType = searchParams.get('type');

    if (paymentSuccess && paymentType) {
      // Show success animation
      const successTitle = paymentType === "deposit"
        ? "Deposit Berhasil Dibayar!"
        : paymentType === "remaining"
          ? "Pembayaran Lunas!"
          : "Pembayaran Berhasil!";

      const successMessage = paymentType === "deposit"
        ? "Deposit telah berhasil dibayar. Pesanan Anda sedang diproses."
        : paymentType === "remaining"
          ? "Pelunasan telah berhasil dibayar. Terima kasih atas kepercayaan Anda."
          : "Pembayaran telah berhasil dilakukan.";

      showSuccess(successTitle, successMessage);

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('payment_success');
      newUrl.searchParams.delete('type');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });

    } else if (paymentError && paymentType) {
      // Show error animation
      const errorTitle = "Pembayaran Gagal";
      const errorMessage = paymentType === "deposit"
        ? "Pembayaran deposit gagal. Silakan coba lagi."
        : paymentType === "remaining"
          ? "Pelunasan pembayaran gagal. Silakan coba lagi."
          : "Pembayaran gagal. Silakan coba lagi.";

      showError(errorTitle, errorMessage);

      // Clean up URL parameters
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('payment_error');
      newUrl.searchParams.delete('type');
      router.replace(newUrl.pathname + newUrl.search, { scroll: false });
    }
  }, [searchParams, showSuccess, showError, router]);

  return null; // This component doesn't render anything
}
