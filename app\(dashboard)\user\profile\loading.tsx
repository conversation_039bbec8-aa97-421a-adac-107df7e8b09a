import { Skeleton } from "@/app/components/ui/skeleton";

export default function ProfileLoading() {
  return (
    <div className="container mx-auto py-6">
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-gradient-to-r from-violet-200 to-indigo-300 dark:from-violet-800 dark:to-indigo-700"></div>
        <div className="relative">
          <Skeleton className="h-9 w-40 mb-3 animate-pulse" />
          <Skeleton className="h-4 w-80 animate-pulse" />
        </div>
      </div>

      {/* Profile Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Profile Info Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 md:col-span-1">
          {/* Card Header */}
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <Skeleton className="h-6 w-32 animate-pulse" />
          </div>

          {/* Card Content */}
          <div className="p-6 flex flex-col items-center space-y-4">
            {/* Avatar */}
            <Skeleton className="w-32 h-32 rounded-full animate-pulse" />

            {/* User Info */}
            <div className="text-center w-full">
              <Skeleton className="h-6 w-40 mb-2 mx-auto animate-pulse" />
              <Skeleton className="h-4 w-32 mx-auto animate-pulse" />
            </div>

            {/* Contact Details */}
            <div className="w-full pt-4 space-y-3">
              {/* Email */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-48 animate-pulse" />
              </div>

              {/* Phone */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-32 animate-pulse" />
              </div>

              {/* Address */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-56 animate-pulse" />
              </div>

              {/* Role */}
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 animate-pulse" />
                <Skeleton className="h-4 w-28 animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* Edit Profile Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 md:col-span-2">
          {/* Card Header */}
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <Skeleton className="h-6 w-24 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-64 animate-pulse" />
          </div>

          {/* Card Content - Form Fields */}
          <div className="p-6 space-y-6">
            {/* Name Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Phone Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-28 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
              <Skeleton className="h-3 w-80 animate-pulse" />
            </div>

            {/* Address Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-16 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Bio Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-8 animate-pulse" />
              <Skeleton className="h-20 w-full animate-pulse" />
            </div>

            {/* Avatar Upload Field */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
              <div className="mt-2">
                <Skeleton className="h-8 w-36 animate-pulse" />
                <Skeleton className="h-3 w-96 mt-1 animate-pulse" />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-between mt-6">
              <Skeleton className="h-10 w-16 animate-pulse" />
              <Skeleton className="h-10 w-32 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Security Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 md:col-span-3">
          {/* Card Header */}
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-48 animate-pulse" />
          </div>

          {/* Card Content - Password Form */}
          <div className="p-6 space-y-6">
            {/* Current Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-32 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* New Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-24 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-36 animate-pulse" />
              <Skeleton className="h-10 w-full animate-pulse" />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end mt-6">
              <Skeleton className="h-10 w-36 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Logout Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 md:col-span-3 mt-8">
          {/* Card Header */}
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-56 animate-pulse" />
          </div>

          {/* Card Content */}
          <div className="p-6">
            <Skeleton className="h-4 w-80 mb-4 animate-pulse" />
            <Skeleton className="h-10 w-32 animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
}
