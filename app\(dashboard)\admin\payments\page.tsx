import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { Search } from "lucide-react";

import { Input } from "@/app/components/ui/input";
import { PaymentStatus } from "@prisma/client";
import { PaymentsTable } from "@/app/components/admin/payments-table";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';



export default async function AdminPaymentsPage({
  searchParams
}: {
  searchParams: Promise<{ q?: string, status?: string }>
}) {
  const session = await getSession();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil parameter pencarian dan filter status
  const params = await searchParams;
  const searchQuery = params.q || '';
  const statusFilter = params.status || '';

  // Siapkan filter untuk status
  const statusWhere = {
    status: statusFilter ? statusFilter as PaymentStatus : undefined
  };

  // Ambil data pembayaran dari database
  type PaymentWithRelations = {
    id: string;
    amount: number;
    deposit: number;
    remaining: number;
    overtime: number | null;
    status: PaymentStatus;
    transactionId: string | null;
    createdAt: Date;
    updatedAt: Date;
    rental: {
      operationalEnd: Date | null;
      user: {
        name: string;
        phone: string;
      };
      product: {
        name: string;
        capacity: number;
      };
    };
  };

  const payments = await prisma.payment.findMany({
    where: {
      ...statusWhere,
      OR: [
        { rental: { user: { name: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { user: { email: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { user: { phone: { contains: searchQuery, mode: 'insensitive' } } } },
        { rental: { product: { name: { contains: searchQuery, mode: 'insensitive' } } } },
      ]
    },
    include: {
      rental: {
        include: {
          product: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  }) as PaymentWithRelations[];

  // Menghitung jumlah pembayaran berdasarkan status
  const depositPendingCount = payments.filter(p => p.status === 'DEPOSIT_PENDING').length;
  const depositPaidCount = payments.filter(p => p.status === 'DEPOSIT_PAID').length;
  const fullyPaidCount = payments.filter(p => p.status === 'FULLY_PAID').length;
  const failedCount = payments.filter(p => p.status === 'FAILED').length;

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold dark:text-white">Manajemen Pembayaran</h1>
        <div className="flex items-center gap-2">
          <form className="relative" action="/admin/payments">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="q"
              placeholder="Cari pembayaran..."
              className="pl-9 w-[250px]"
              defaultValue={searchQuery}
            />
            {statusFilter && (
              <input type="hidden" name="status" value={statusFilter} />
            )}
          </form>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-6 ">
        <Link href="/admin/payments">
          <Button variant={!statusFilter ? "default" : "outline"} size="sm" className="bg-gray-600 text-white hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 dark:text-white ">
            Semua ({payments.length})
          </Button>
        </Link>
        <Link href="/admin/payments?status=DEPOSIT_PENDING">
          <Button variant={statusFilter === 'DEPOSIT_PENDING' ? "default" : "outline"} size="sm" className="bg-yellow-600 text-white hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 dark:text-white ">
            Menunggu Deposit ({depositPendingCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=DEPOSIT_PAID">
          <Button variant={statusFilter === 'DEPOSIT_PAID' ? "default" : "outline"} size="sm" className="bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 dark:text-white ">
            Deposit Dibayar ({depositPaidCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=FULLY_PAID">
          <Button variant={statusFilter === 'FULLY_PAID' ? "default" : "outline"} size="sm" className="bg-green-600 text-white hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 dark:text-white ">
            Lunas ({fullyPaidCount})
          </Button>
        </Link>
        <Link href="/admin/payments?status=FAILED">
          <Button variant={statusFilter === 'FAILED' ? "default" : "outline"} size="sm" className="bg-red-600 text-white hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 dark:text-white ">
            Gagal ({failedCount})
          </Button>
        </Link>
      </div>

      <PaymentsTable
        payments={payments}
        searchQuery={searchQuery}
        statusFilter={statusFilter}
      />
    </div>
  );
}
