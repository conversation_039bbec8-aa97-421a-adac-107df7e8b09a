"use client";

import { MarketingBottomNavigation } from "@/app/components/shared/marketing-bottom-navigation";

interface MarketingLayoutProps {
  children: React.ReactNode;
}

export default function MarketingLayout({ children }: MarketingLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-950 dark:text-gray-100">
      {/* Main Content */}
      <main className="pb-24 md:pb-0">
        {children}
      </main>
      
      {/* Mobile Bottom Navigation */}
      <MarketingBottomNavigation />
    </div>
  );
} 
