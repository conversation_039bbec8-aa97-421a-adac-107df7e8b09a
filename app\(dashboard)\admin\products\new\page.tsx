"use client";

import { ProductForm } from "@/app/components/product/product-form";
import { useRouter } from "next/navigation";

export default function NewProductPage() {
    const router = useRouter();

    const handleSuccess = () => {
        // Redirect ke halaman admin products setelah berhasil tambah produk
        router.push("/admin/products");
    };

    const handleCancel = () => {
        // Redirect ke halaman admin products jika dibatalkan
        router.push("/admin/products");
    };

    return (
        <div className="container mx-auto p-6">
            <ProductForm
                onSuccess={handleSuccess}
                onCancel={handleCancel}
            />
        </div>
    );
}
