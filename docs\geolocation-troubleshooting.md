# Troubleshooting Geolokasi

## Error yang Diperbaiki

Error `Percobaan ke-1 gagal: {}` telah diperbaiki dengan:

1. **Improved Error Handling**: Error logging yang lebih detail
2. **Fallback Mechanism**: Sistem fallback jika retry gagal
3. **Better Validation**: Validasi koordinat yang lebih robust
4. **Debug Tools**: Tools untuk debugging masalah geolokasi

## Cara Debugging

### 1. Menggunakan Tombol Debug (Development Mode)

Jika aplikasi berjalan dalam mode development, akan ada tombol "Debug" yang menampilkan informasi sistem di console:

```javascript
🔧 Debug Informasi Geolokasi:
- Navigator geolocation: true/false
- Protocol: https:/http:
- Hostname: localhost/domain
- User Agent: browser info
- Platform: OS info
- Permission state: granted/denied/prompt
```

### 2. Console Logging

Sistem sekarang memberikan logging yang detail:

```javascript
// Proses dimulai
"<PERSON><PERSON><PERSON> proses mendapatkan lokasi pengguna..."
"Memanggil getCurrentPosition..."

// Retry mechanism
"Percobaan mendapatkan lokasi ke-1 dari 3"
"Percobaan ke-1 berhasil: {lat, lng, accuracy}"

// Fallback jika retry gagal
"Retry mechanism gagal, mencoba fallback sederhana"
"Fallback berhasil mendapatkan lokasi"

// Hasil akhir
"✅ Koordinat pengguna berhasil ditemukan"
"🔍 Memulai reverse geocoding"
"✅ Alamat berhasil ditemukan"
"🏁 Proses mendapatkan lokasi selesai"
```

### 3. Error Codes

Sistem menangani berbagai error codes:

- **Code 1 (PERMISSION_DENIED)**: User menolak akses lokasi
- **Code 2 (POSITION_UNAVAILABLE)**: GPS tidak tersedia
- **Code 3 (TIMEOUT)**: Timeout mendapatkan lokasi
- **Validation Error**: Koordinat tidak valid untuk Indonesia

## Langkah Troubleshooting

### 1. Periksa Permission

```javascript
// Cek permission di browser console
navigator.permissions.query({name: 'geolocation'}).then(result => {
  console.log('Permission:', result.state);
});
```

### 2. Test Manual Geolocation

```javascript
// Test manual di browser console
navigator.geolocation.getCurrentPosition(
  pos => console.log('Success:', pos.coords),
  err => console.log('Error:', err),
  { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
);
```

### 3. Periksa Browser Support

```javascript
// Cek dukungan browser
console.log('Geolocation supported:', !!navigator.geolocation);
console.log('HTTPS:', location.protocol === 'https:');
```

## Common Issues & Solutions

### 1. Permission Denied
**Gejala**: Error code 1
**Solusi**:
- Klik ikon kunci di address bar
- Pilih "Allow" untuk location
- Refresh halaman

### 2. Position Unavailable
**Gejala**: Error code 2
**Solusi**:
- Aktifkan GPS di perangkat
- Pindah ke area terbuka
- Pastikan sinyal GPS baik

### 3. Timeout
**Gejala**: Error code 3
**Solusi**:
- Tunggu di area dengan sinyal baik
- Coba lagi setelah beberapa saat
- Periksa koneksi internet

### 4. Koordinat Tidak Valid
**Gejala**: "Koordinat tidak valid untuk Indonesia"
**Solusi**:
- Pastikan berada di wilayah Indonesia
- Periksa setting lokasi perangkat
- Coba restart GPS

### 5. HTTP vs HTTPS
**Gejala**: Akurasi rendah atau error
**Solusi**:
- Gunakan HTTPS untuk akurasi optimal
- Localhost tetap berfungsi di HTTP

## Sistem Fallback

Jika retry mechanism gagal, sistem akan:

1. **Coba dengan konfigurasi sederhana**:
   ```javascript
   {
     enableHighAccuracy: false,
     timeout: 10000,
     maximumAge: 60000
   }
   ```

2. **Validasi koordinat tetap dilakukan**
3. **Gunakan alamat fallback jika reverse geocoding gagal**

## Browser Compatibility

### Fully Supported:
- Chrome 50+
- Firefox 55+
- Safari 10+
- Edge 79+

### Limited Support:
- Internet Explorer (tidak disarankan)
- Browser lama (akurasi terbatas)

## Tips Optimasi

### Untuk User:
1. **Aktifkan GPS** di perangkat
2. **Izinkan akses lokasi** di browser
3. **Berada di area terbuka** untuk sinyal optimal
4. **Gunakan HTTPS** jika memungkinkan
5. **Update browser** ke versi terbaru

### Untuk Developer:
1. **Monitor console logs** untuk debugging
2. **Test di berbagai browser** dan perangkat
3. **Implementasi fallback** yang robust
4. **Berikan feedback** yang jelas ke user
5. **Handle semua error cases**

## Testing Checklist

- [ ] Test di Chrome desktop
- [ ] Test di Firefox desktop  
- [ ] Test di Safari mobile
- [ ] Test dengan GPS off
- [ ] Test dengan permission denied
- [ ] Test di area dengan sinyal lemah
- [ ] Test di HTTP vs HTTPS
- [ ] Test dengan network slow
- [ ] Verify console logs
- [ ] Check error messages

## Monitoring

Untuk monitoring production:

```javascript
// Track geolocation success rate
const geoStats = {
  attempts: 0,
  successes: 0,
  failures: 0,
  averageAccuracy: 0
};

// Log ke analytics service
analytics.track('geolocation_attempt', {
  success: true/false,
  accuracy: number,
  error_code: number,
  browser: navigator.userAgent
});
```
