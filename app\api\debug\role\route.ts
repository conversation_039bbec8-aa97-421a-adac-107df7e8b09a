import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await getSession();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "No session" }, { status: 401 });
    }

    return NextResponse.json({
      sessionRole: session.user.role,
      sessionRoleType: typeof session.user.role,
      sessionRoleLowerCase: session.user.role?.toLowerCase(),
      sessionUser: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
      },
      roleTests: {
        isUser: session.user.role?.toLowerCase() === "user",
        isAdmin: session.user.role?.toLowerCase() === "admin",
        isUserUppercase: session.user.role === "USER",
        isAdminUppercase: session.user.role === "ADMIN",
      }
    });
  } catch (error) {
    console.error("Debug role error:", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
