"use client";

import { useState, useTransition } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { toast } from "sonner";
import { Eye, EyeOff, Lock } from "lucide-react";
import { cn } from "@/lib/utils";

interface ValidationError {
  path: string[];
  message: string;
}

interface PasswordFormProps {
  className?: string;
}

export function PasswordForm({ className }: PasswordFormProps) {
  const [isPending, startTransition] = useTransition();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (formData: FormData) => {
    startTransition(async () => {
      try {
        setErrors({});

        // Convert FormData to JSON
        const data = {
          currentPassword: formData.get("currentPassword") as string,
          newPassword: formData.get("newPassword") as string,
          confirmPassword: formData.get("confirmPassword") as string,
        };

        // Call API endpoint
        const response = await fetch('/api/auth/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        const result = await response.json();

        if (result.success) {
          toast.success(result.message);
          // Reset form
          const form = document.getElementById('password-form') as HTMLFormElement;
          if (form) {
            form.reset();
          }
        } else {
          toast.error(result.message);

          // Handle validation errors
          if (result.errors) {
            const errorMap: Record<string, string> = {};
            result.errors.forEach((error: ValidationError) => {
              if (error.path && error.path[0]) {
                errorMap[error.path[0]] = error.message;
              }
            });
            setErrors(errorMap);
          }
        }
      } catch (error) {
        console.error("Error updating password:", error);
        toast.error("Terjadi kesalahan saat memperbarui password");
      }
    });
  };

  return (
    <Card className={cn("md:col-span-3", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5 text-violet-500" />
          Keamanan Akun
        </CardTitle>
        <CardDescription>
          Kelola keamanan akun Anda dengan mengubah password
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form id="password-form" onSubmit={(e) => {
          e.preventDefault();
          const formData = new FormData(e.currentTarget);
          handleSubmit(formData);
        }} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Password Saat Ini</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                name="currentPassword"
                type={showCurrentPassword ? "text" : "password"}
                placeholder="Masukkan password Anda saat ini"
                required
                className={errors.currentPassword ? "border-red-500" : ""}
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
              >
                {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.currentPassword && (
              <p className="text-sm text-red-500">{errors.currentPassword}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="newPassword">Password Baru</Label>
            <div className="relative">
              <Input
                id="newPassword"
                name="newPassword"
                type={showNewPassword ? "text" : "password"}
                placeholder="Masukkan password baru (minimal 6 karakter)"
                required
                className={errors.newPassword ? "border-red-500" : ""}
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
              >
                {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-500">{errors.newPassword}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Konfirmasi password baru Anda"
                required
                className={errors.confirmPassword ? "border-red-500" : ""}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-violet-600 dark:text-gray-500 dark:hover:text-violet-400 transition-colors"
              >
                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">{errors.confirmPassword}</p>
            )}
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              disabled={isPending}
              className="bg-gradient-to-r from-violet-600 to-indigo-500 hover:from-violet-700 hover:to-indigo-600 dark:from-violet-500/90 dark:to-indigo-600/90 dark:hover:from-violet-500 dark:hover:to-indigo-700 text-white dark:text-white shadow-sm min-w-[140px]"
            >
              {isPending ? "Memperbarui..." : "Perbarui Password"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
