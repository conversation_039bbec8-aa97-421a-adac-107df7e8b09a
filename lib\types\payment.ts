import { Product } from "./product";
import { User } from "./user";

export interface PaymentRequest {
    orderId: string;
    amount: number;
    name: string;
    email: string;
    productName: string;
}

export interface MidtransConfig {
    clientKey: string;
    merchantId: string;
    serverKey: string;
}

export interface SnapInterface {
    pay: (
        token: string,
        options: {
            onSuccess: (result: MidtransResult) => void;
            onPending: (result: MidtransResult) => void;
            onError: (result: MidtransError) => void;
            onClose: () => void;
        }
    ) => void;
}

export interface MidtransResult {
    transaction_id: string;
    order_id: string;
    gross_amount: string;
    payment_type: string;
    transaction_status: string;
    transaction_time: string;
}

export interface MidtransError {
    status_code: string;
    status_message: string;
    transaction_id: string;
    order_id: string;
}

export type PaymentStatus = "DEPOSIT_PENDING" | "DEPOSIT_PAID" | "FULLY_PAID" | "FAILED" | "INVOICE_ISSUED";

export interface PaymentStatusUpdate {
    paymentId: string;
    status: PaymentStatus;
}

export interface MidtransNotification {
    transaction_time: string;
    transaction_status: string;
    transaction_id: string;
    status_message: string;
    status_code: string;
    signature_key: string;
    payment_type: string;
    order_id: string;
    merchant_id: string;
    gross_amount: string;
    fraud_status?: string;
    currency: string;
}

export interface PaymentResponse {
    token: string;
}

export interface MidtransPaymentResponse {
    token: string;
}

export interface PaymentStatusProps {
    id: string;
    currentStatus: string;
    rental: {
        id: string;
        status: string;
        amount: number;
        product: {
            name: string;
        };
    };
}

export interface Rental {
    id: string;
    userId: string;
    productId: string;
    startDate: Date;
    endDate: Date;
    status: string;
    amount: number;
    location: string;
    purpose: string;
    arrivalTime: string;
    quantity: number;
    totalPrice: number;
    createdAt: Date;
    updatedAt: Date;
    product: Product & { user: User };
    user: User;
    payment: Payment | null;
}

export interface Payment {
    id: string;
    rentalId: string;
    userId: string | null;
    amount: number;
    deposit: number;
    remaining: number;
    overtime?: number | null;
    status: string;
    transactionId?: string | null;
    snapToken?: string | null;
    createdAt: Date;
    updatedAt: Date;
    rental: {
        user: {
            name: string | null;
            email: string;
        };
        product: {
            name: string;
            id: string;
            image: string | null;
            price: number;
            capacity: number;
        };
    };
}

export interface PaymentResult {
    success: boolean;
    message: string;
    snapToken?: string;
    invoiceId?: string;
    error?: string;
}
