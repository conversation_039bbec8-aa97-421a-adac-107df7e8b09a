import { prisma } from "@/lib/config/prisma";
import { UserTable } from "../admin/user-table";

interface UserTableServerProps {
  currentPage: number;
  searchQuery: string;
}

export async function UserTableServer({ currentPage, searchQuery }: UserTableServerProps) {
  const itemsPerPage = 5;
  const skip = (currentPage - 1) * itemsPerPage;

  // Build where clause for search
  const whereClause = searchQuery ? {
    OR: [
      { name: { contains: searchQuery, mode: 'insensitive' as const } },
      { email: { contains: searchQuery, mode: 'insensitive' as const } },
      { phone: { contains: searchQuery, mode: 'insensitive' as const } },
    ]
  } : {};

  // Fetch users data with pagination
  const [users, totalUsers] = await Promise.all([
    prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        phone: true,
        image: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: itemsPerPage,
    }),
    prisma.user.count({
      where: whereClause,
    })
  ]);

  const totalPages = Math.ceil(totalUsers / itemsPerPage);

  return (
    <UserTable
      searchQuery={searchQuery}
      users={users}
      currentPage={currentPage}
      totalPages={totalPages}
      totalUsers={totalUsers}
    />
  );
}
