import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";

interface Product {
  id: string;
  name: string;
  price: number;
  capacity: number | string;
  category: string | null;
  status: string;
  imageUrl?: string;
  image?: string;
  description?: string;
}

export function useProductData() {
  const params = useParams();
  const router = useRouter();
  const { showError } = useToast();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [isKnownUser, setIsKnownUser] = useState(false);

  // Fungsi untuk mendapatkan data produk
  async function getProductById(id: string) {
    try {
      // Gunakan URL absolut dengan fallback ke localhost jika env tidak tersedia
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
      const response = await fetch(`${baseUrl}/api/products/${id}`, {
        cache: 'no-store',
        next: { revalidate: 0 }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch product: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      throw error; // Re-throw untuk ditangkap di useEffect
    }
  }

  // Load data produk
  useEffect(() => {
    async function loadProduct() {
      if (!params.id) return;
      
      try {
        setLoading(true);
        const data = await getProductById(params.id as string);
        
        if (!data) {
          showError("Produk yang Anda cari tidak tersedia");
          // Produk tidak ditemukan, redirect ke catalog
          router.push('/user/catalog');
          return;
        }
        
        setProduct(data);
      } catch (error) {
        console.error('Error loading product:', error);
        showError("Gagal memuat produk. Silakan coba lagi nanti atau pilih produk lain");
        router.push('/user/catalog');
      } finally {
        setLoading(false);
      }
    }
    
    loadProduct();
  }, [params.id, router, showError]);
  
  // Periksa status pengguna
  useEffect(() => {
    async function checkUserStatus() {
      try {
        // Gunakan URL absolut yang sama
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || window.location.origin;
        // Panggil API untuk cek status pengguna
        const response = await fetch(`${baseUrl}/api/user/status`, {
          cache: 'no-store',
          next: { revalidate: 0 }
        });
        
        if (response.ok) {
          const data = await response.json();
          setIsKnownUser(data.isKnownUser || false);
        }
      } catch (error) {
        console.error('Error checking user status:', error);
      }
    }
    
    checkUserStatus();
  }, []);

  return { product, loading, isKnownUser };
} 
