# Sistem Animasi GSAP untuk Pembayaran

## Overview
Sistem animasi GSAP telah diimplementasikan untuk memberikan feedback visual yang menarik dan profesional pada proses pembayaran aplikasi rental genset.

## Dependencies
```bash
npm install gsap
```

## Komponen Utama

### 1. `PaymentAnimation` Component
Komponen utama untuk menampilkan animasi pembayaran dengan berbagai state.

```tsx
import { PaymentAnimation } from "@/app/components/ui/payment-animation";

<PaymentAnimation
  type="success" // "success" | "error" | "processing"
  title="Pembayaran Berhasil!"
  message="Deposit telah berhasil dibayar"
  amount="Rp 500.000"
  onAnimationComplete={() => console.log("Animation done")}
/>
```

**Props:**
- `type`: Tipe animasi ("success", "error", "processing")
- `title`: Judul utama animasi
- `message`: Pesan detail
- `amount`: <PERSON><PERSON><PERSON> pembayaran (opsional)
- `onAnimationComplete`: Callback setelah animasi selesai

### 2. `usePaymentAnimation()` Hook
Hook untuk mengelola state dan kontrol animasi pembayaran.

```tsx
import { usePaymentAnimation } from "@/app/components/ui/payment-animation-wrapper";

const { showSuccess, showError, showProcessing, hideAnimation } = usePaymentAnimation();

// Tampilkan animasi sukses
showSuccess("Pembayaran Berhasil!", "Deposit telah dibayar", "Rp 500.000");

// Tampilkan animasi error
showError("Pembayaran Gagal", "Silakan coba lagi", "Rp 500.000");

// Tampilkan animasi processing
showProcessing("Memproses...", "Mohon tunggu", "Rp 500.000");

// Sembunyikan animasi
hideAnimation();
```

### 3. `PaymentAnimationWrapper` Component
Wrapper component yang menggabungkan animasi dengan sistem sound.

```tsx
import { PaymentAnimationWrapper } from "@/app/components/ui/payment-animation-wrapper";

<PaymentAnimationWrapper>
  {children}
</PaymentAnimationWrapper>
```

## Jenis Animasi

### 1. Success Animation
- **Entrance**: Scale dan bounce effect dengan elastic easing
- **Icon**: Check icon dengan scale pulse effect
- **Particles**: 8 partikel yang menyebar dengan random trajectory
- **Sound**: Otomatis memainkan success sound
- **Duration**: ~2 detik total
- **Auto-hide**: Ya, setelah 2 detik

### 2. Error Animation
- **Entrance**: Scale dengan back easing
- **Icon**: X icon dengan shake effect (10 derajat, 5 repetisi)
- **Sound**: Otomatis memainkan error sound
- **Duration**: ~1.5 detik total
- **Auto-hide**: Ya, setelah 2 detik

### 3. Processing Animation
- **Entrance**: Scale dengan power easing
- **Icon**: Credit card icon dengan rotasi kontinyu (360°, infinite)
- **Sound**: Otomatis memainkan notification sound
- **Duration**: Kontinyu sampai dihentikan manual
- **Auto-hide**: Tidak, harus manual

## Implementasi di Payment Button

```tsx
// Di payment-button.client.tsx
import { usePaymentAnimation } from "@/app/components/ui/payment-animation-wrapper";

const { showSuccess, showError, showProcessing, hideAnimation } = usePaymentAnimation();

const processPayment = async () => {
  try {
    // Tampilkan processing
    showProcessing(
      "Memproses Pembayaran",
      "Mohon tunggu...",
      formatCurrency(amount)
    );

    // Proses pembayaran...
    const result = await paymentAPI();

    // Sembunyikan processing
    hideAnimation();

    // Tampilkan hasil
    if (result.success) {
      showSuccess(
        "Pembayaran Berhasil!",
        "Deposit telah dibayar",
        formatCurrency(amount)
      );
    } else {
      showError(
        "Pembayaran Gagal",
        result.message,
        formatCurrency(amount)
      );
    }
  } catch (error) {
    hideAnimation();
    showError("Pembayaran Gagal", error.message, formatCurrency(amount));
  }
};
```

## Konfigurasi GSAP Timeline

### Success Timeline
```javascript
const tl = gsap.timeline();

// Container entrance
tl.to(container, { opacity: 1, scale: 1, duration: 0.4, ease: "back.out(1.7)" })
  
// Circle animation
.to(circle, { scale: 1, rotation: 0, duration: 0.6, ease: "elastic.out(1, 0.5)" }, "-=0.2")

// Icon with bounce
.to(icon, { opacity: 1, y: 0, scale: 1, duration: 0.5, ease: "back.out(2)" })
.to(icon, { scale: 1.2, duration: 0.2, yoyo: true, repeat: 1 })

// Content stagger
.to([title, message, amount], { opacity: 1, y: 0, scale: 1, duration: 0.4, stagger: 0.1 })

// Particles explosion
.to(particles, { opacity: 1, scale: 1, y: -50, x: random, duration: 1, stagger: 0.1 })
```

### Error Timeline
```javascript
// Similar entrance, but with shake effect
.to(icon, { rotation: 10, duration: 0.1, yoyo: true, repeat: 5, ease: "power2.inOut" })
```

### Processing Timeline
```javascript
// Continuous rotation
gsap.to(icon, { rotation: 360, duration: 2, repeat: -1, ease: "none" });
```

## Styling & Theming

### Colors
- **Success**: Green theme (`text-green-600`, `bg-green-100`)
- **Error**: Red theme (`text-red-600`, `bg-red-100`)
- **Processing**: Blue theme (`text-blue-600`, `bg-blue-100`)

### Dark Mode Support
Semua komponen mendukung dark mode dengan:
- `dark:bg-gray-800` untuk background
- `dark:text-white` untuk text
- `dark:border-gray-700` untuk borders

### Responsive Design
- Mobile-first approach
- Touch-friendly dengan backdrop blur
- Proper z-index layering (`z-50`)

## Testing

Kunjungi `/user/animation-test` untuk menguji semua jenis animasi:

```tsx
// Test buttons tersedia untuk:
showSuccess("Test Success", "Success message", "Rp 500.000");
showError("Test Error", "Error message", "Rp 500.000");
showProcessing("Test Processing", "Processing message", "Rp 500.000");
```

## Performance Notes

### Optimizations
- Menggunakan `transform` dan `opacity` untuk animasi (GPU accelerated)
- Timeline cleanup dengan `tl.kill()` pada unmount
- Conditional rendering untuk particles
- Efficient stagger animations

### Browser Compatibility
- Chrome/Edge: ✅ Full support
- Firefox: ✅ Full support
- Safari: ✅ Full support
- Mobile browsers: ✅ Full support

## Integration dengan Sound System

Animasi otomatis terintegrasi dengan sistem sound:
- **Success**: `sounds.onPaymentSuccess()`
- **Error**: `sounds.onPaymentError()`
- **Processing**: `sounds.onNotification()`

## Future Enhancements

### Planned Features
1. **Custom Particles**: Berbagai bentuk partikel (stars, confetti)
2. **Lottie Integration**: Animasi vector yang lebih kompleks
3. **Haptic Feedback**: Getaran untuk mobile devices
4. **Custom Easing**: Easing curves yang lebih natural
5. **Animation Presets**: Template animasi untuk berbagai use case

### Configuration Options
```tsx
interface AnimationConfig {
  duration: number;
  particles: boolean;
  sound: boolean;
  autoHide: boolean;
  customEasing: string;
}
```

## Troubleshooting

### Common Issues
1. **Animasi tidak muncul**: Pastikan `PaymentAnimationWrapper` sudah di-wrap di layout
2. **Sound tidak berfungsi**: Cek apakah `useAppSounds` hook tersedia
3. **Performance lag**: Reduce particle count atau disable pada low-end devices
4. **Z-index conflicts**: Pastikan parent container tidak memiliki z-index lebih tinggi

### Debug Mode
```tsx
// Enable GSAP debug untuk development
gsap.config({ trialWarn: false });
```
