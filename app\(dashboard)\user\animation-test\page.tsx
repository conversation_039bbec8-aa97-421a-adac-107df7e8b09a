"use client";

import { SoundButton } from "@/app/components/ui/sound-button";
import { usePaymentAnimationContext } from "@/app/components/providers/payment-animation-provider";
import { formatCurrency } from "@/lib/utils/format";

export default function AnimationTestPage() {
  const { showSuccess, showError, showProcessing, hideAnimation } = usePaymentAnimationContext();

  const handleSuccessTest = () => {
    showSuccess(
      "Pembayaran Berhasil!",
      "Deposit telah berhasil dibayar. Pesanan Anda sedang diproses.",
      formatCurrency(500000)
    );
  };

  const handleErrorTest = () => {
    showError(
      "Pembayaran Gagal",
      "Ter<PERSON><PERSON> kesalahan dalam proses pembayaran. Silakan coba lagi.",
      formatCurrency(500000)
    );
  };

  const handleProcessingTest = () => {
    showProcessing(
      "Memproses Pembayaran",
      "<PERSON>hon tunggu, kami sedang memproses pembayaran Anda...",
      formatCurrency(500000)
    );

    // Auto hide after 3 seconds for demo
    setTimeout(() => {
      hideAnimation();
    }, 3000);
  };

  const handleLunasTest = () => {
    showSuccess(
      "Pembayaran Lunas!",
      "Pelunasan telah berhasil dibayar. Terima kasih atas kepercayaan Anda.",
      formatCurrency(1000000)
    );
  };

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Test Animasi Pembayaran GSAP</h1>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-100 dark:border-gray-700">
          <h2 className="text-xl font-semibold mb-6">Demo Animasi</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SoundButton
              onClick={handleSuccessTest}
              variant="success"
              size="mobile"
              className="w-full"
              soundType="click"
            >
              🎉 Test Success Animation
            </SoundButton>

            <SoundButton
              onClick={handleErrorTest}
              variant="destructive"
              size="mobile"
              className="w-full"
              soundType="click"
            >
              ❌ Test Error Animation
            </SoundButton>

            <SoundButton
              onClick={handleProcessingTest}
              variant="outline"
              size="mobile"
              className="w-full"
              soundType="click"
            >
              ⏳ Test Processing Animation
            </SoundButton>

            <SoundButton
              onClick={handleLunasTest}
              variant="gradient"
              size="mobile"
              className="w-full"
              soundType="click"
            >
              💰 Test Lunas Animation
            </SoundButton>
          </div>

          <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="font-medium mb-2">Fitur Animasi:</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>✨ Animasi entrance dengan GSAP</li>
              <li>🎵 Integrasi dengan sistem sound</li>
              <li>🎨 Partikel animasi untuk success</li>
              <li>🔄 Rotasi kontinyu untuk processing</li>
              <li>📱 Responsive dan mobile-friendly</li>
              <li>🌙 Dark mode support</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
