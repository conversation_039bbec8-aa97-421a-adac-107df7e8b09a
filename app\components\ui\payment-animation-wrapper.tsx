"use client";

import { useEffect } from "react";
import { PaymentAnimation } from "./payment-animation";
import { usePaymentAnimation } from "@/lib/hooks/use-payment-animation";
import { useAppSounds } from "@/lib/hooks/use-app-sounds";

interface PaymentAnimationWrapperProps {
  children: React.ReactNode;
}

export function PaymentAnimationWrapper({ children }: PaymentAnimationWrapperProps) {
  const { animationState, hideAnimation } = usePaymentAnimation();
  const { sounds } = useAppSounds();

  useEffect(() => {
    if (animationState.isVisible) {
      // Play sound based on animation type
      switch (animationState.type) {
        case "success":
          sounds.onPaymentSuccess();
          break;
        case "error":
          sounds.onPaymentError();
          break;
        case "processing":
          sounds.onNotification();
          break;
      }
    }
  }, [animationState.isVisible, animationState.type, sounds]);

  const handleAnimationComplete = () => {
    // Auto-hide after animation completes (except for processing)
    if (animationState.type !== "processing") {
      setTimeout(() => {
        hideAnimation();
      }, 2000); // Show for 2 seconds after animation
    }
  };

  return (
    <>
      {children}
      {animationState.isVisible && (
        <PaymentAnimation
          type={animationState.type}
          title={animationState.title}
          message={animationState.message}
          amount={animationState.amount}
          onAnimationComplete={handleAnimationComplete}
        />
      )}
    </>
  );
}

// Export hook for easy access
export { usePaymentAnimation };
