import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";
import { put } from '@vercel/blob';

export const runtime = 'nodejs';

export async function POST(req: Request) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get('image') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: "No image provided" }, 
        { status: 400 }
      );
    }

    console.log('Uploading file:', file.name);

    // Upload ke Vercel Blob
    const blob = await put(file.name, file, {
      access: 'public',
      token: process.env.BLOB_READ_WRITE_TOKEN
    });

    console.log('Upload result:', blob);
    
    return NextResponse.json({ 
      success: true,
      imageUrl: blob.url 
    });

  } catch (error) {
    console.error("[UPLOAD_ERROR]", error);
    return NextResponse.json(
      { error: "Failed to upload image" },
      { status: 500 }
    );
  }
}

