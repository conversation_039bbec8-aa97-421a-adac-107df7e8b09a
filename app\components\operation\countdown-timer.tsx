"use client";

import { useEffect, useState, useRef } from "react";
import { getBookedHours } from "@/lib/utils/calculate";
import { Button } from "@/app/components/ui/button";
import { Play, Pause } from "lucide-react";
import axios from "axios";
import { useToast } from "@/lib/hooks/use-toast";

// Nonaktifkan console.log di lingkungan produksi
const logger = {
  log: (...args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(...args);
    }
  },
  error: (...args: unknown[]) => console.error(...args),
  warn: (...args: unknown[]) => console.warn(...args)
};

interface CountdownTimerProps {
  operationalStart: Date;
  duration: string;
  rentalId: string;
  showPauseButton?: boolean;
}

export function CountdownTimer({ operationalStart, duration, rentalId, showPauseButton = true }: CountdownTimerProps) {
  const { showError } = useToast();
  // Debug: log nilai rentalId
  logger.log("CountdownTimer - rentalId:", rentalId);

  // Inisialisasi state
  const [timeLeft, setTimeLeft] = useState<{
    hours: number;
    minutes: number;
    seconds: number;
    isOvertime: boolean;
  }>({
    hours: 0,
    minutes: 0,
    seconds: 0,
    isOvertime: false
  });

  const [isPaused, setIsPaused] = useState(false);
  const [pauseOffset, setPauseOffset] = useState(0);
  const [pauseStartTime, setPauseStartTime] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSuccessfulFetchRef = useRef<number>(0);
  const [isToggling, setIsToggling] = useState(false);

  // Efek untuk memuat data status timer dari server
  useEffect(() => {
    // Validasi: jika rentalId kosong atau tidak valid, jangan lakukan fetch
    if (!rentalId) {
      console.error("CountdownTimer - Invalid rentalId:", rentalId);
      setIsLoading(false);
      setHasError(true);
      showError("Error timer. ID rental tidak valid. Timer akan berjalan secara default.");
      return;
    }

    const fetchTimerStatus = async () => {
      try {
        // Jangan melakukan polling terlalu sering jika baru saja berhasil
        const now = Date.now();
        if (now - lastSuccessfulFetchRef.current < 3000) {
          logger.log("Skipping fetch, too soon from last successful fetch");
          return;
        }

        logger.log("Fetching timer status for rentalId:", rentalId);
        setIsLoading(true);
        const response = await axios.get(`/api/operations/pause-timer?rentalId=${rentalId}`);
        logger.log("Timer API response:", response.data);

        if (response.data.success) {
          const { isPaused, pauseOffset, pauseStartTime } = response.data.data;
          setIsPaused(isPaused);
          setPauseOffset(pauseOffset);
          setPauseStartTime(pauseStartTime);
          setHasError(false);
          setRetryCount(0);
          lastSuccessfulFetchRef.current = now;
        }
      } catch (error) {
        console.error("Error fetching timer status:", error);
        setRetryCount(prev => prev + 1);
        // Jangan tampilkan toast di setiap polling jika gagal
        if (isLoading) {
          setHasError(true);
          showError("Gagal memuat status timer. Status timer tidak dapat dimuat. Timer akan berjalan secara default.");
        }
      } finally {
        // Selalu set loading ke false meskipun terjadi error
        setIsLoading(false);
      }
    };

    // Jalankan fetch segera untuk pertama kali
    fetchTimerStatus();

    // Siapkan polling dengan backoff berdasarkan jumlah retry
    const setupPolling = () => {
      // Bersihkan interval sebelumnya jika ada
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      // Hitung interval berdasarkan jumlah retry (eksponensial backoff)
      // Minimal 30 detik, dan maksimal 120 detik untuk mengurangi beban server
      const baseInterval = 999999999 // EMERGENCY DISABLED; // 30 detik
      const backoffFactor = Math.min(Math.pow(1.5, Math.min(retryCount, 5)), 6);
      const pollingInterval = Math.floor(baseInterval * backoffFactor);

      logger.log(`Setting polling interval to ${pollingInterval}ms (retry count: ${retryCount})`);

      pollingIntervalRef.current = setInterval(() => {
        if (!hasError) {
          fetchTimerStatus();
        }
      }, pollingInterval);
    };

    // Setup polling
    setupPolling();

    // Tambahkan timeout untuk fallback jika loading terlalu lama
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        console.warn("Timer loading timeout, showing default timer");
      }
    }, 3000); // Timeout setelah 3 detik

    // Effect cleanup
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
      clearTimeout(loadingTimeout);
    };
  }, [rentalId, showError, isLoading, hasError, retryCount]);

  // Reset polling interval ketika retry count berubah
  useEffect(() => {
    // Hitung interval berdasarkan jumlah retry (eksponensial backoff)
    // Minimal 10 detik, dan maksimal 60 detik
    const baseInterval = 999999999 // EMERGENCY DISABLED; // 10 detik
    const backoffFactor = Math.min(Math.pow(1.5, Math.min(retryCount, 5)), 6);
    const pollingInterval = Math.floor(baseInterval * backoffFactor);

    logger.log(`Changing polling interval to ${pollingInterval}ms (retry count: ${retryCount})`);

    // Bersihkan interval sebelumnya jika ada
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    // Setup polling baru - gunakan interval lebih cepat (3 detik) untuk sinkronisasi yang lebih baik
    pollingIntervalRef.current = setInterval(() => {
      if (!hasError) {
        const fetchTimerStatus = async () => {
          try {
            // Jangan melakukan polling terlalu sering jika baru saja berhasil
            const now = Date.now();
            if (now - lastSuccessfulFetchRef.current < 2000) {
              logger.log("Skipping fetch, too soon from last successful fetch");
              return;
            }

            logger.log("Fetching timer status for rentalId:", rentalId);
            // Tambahkan parameter waktu untuk menghindari cache
            const timestamp = new Date().getTime();
            const response = await axios.get(`/api/operations/pause-timer?rentalId=${rentalId}&t=${timestamp}`);

            if (response.data.success) {
              const { isPaused, pauseOffset, pauseStartTime } = response.data.data;
              setIsPaused(isPaused);
              setPauseOffset(pauseOffset);
              setPauseStartTime(pauseStartTime);
              setHasError(false);
              setRetryCount(0);
              lastSuccessfulFetchRef.current = now;
            }
          } catch (error) {
            console.error("Error in polling fetch:", error);
            setRetryCount(prev => prev + 1);
          }
        };

        fetchTimerStatus();
      }
    }, isPaused ? 10000 : pollingInterval); // Poll setiap 10 detik jika dijeda, mengurangi beban server

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [retryCount, rentalId, hasError, isPaused]);

  // Effect untuk menyinkronkan status jeda dengan server dan komponen timer lain
  useEffect(() => {
    // Handler untuk event 'visibilitychange'
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Ketika tab menjadi visible, segera sinkronkan status timer
        const syncTimerStatus = async () => {
          try {
            logger.log("Tab became visible, syncing timer status");
            const timestamp = new Date().getTime();
            const response = await axios.get(`/api/operations/pause-timer?rentalId=${rentalId}&t=${timestamp}`);

            if (response.data.success) {
              const { isPaused: serverIsPaused, pauseOffset: serverPauseOffset, pauseStartTime: serverPauseStartTime } = response.data.data;

              // Jika status berbeda, update dan kirim event
              if (isPaused !== serverIsPaused || pauseOffset !== serverPauseOffset) {
                logger.log("Timer status changes detected from server, updating local state and broadcasting");
                setIsPaused(serverIsPaused);
                setPauseOffset(serverPauseOffset);
                setPauseStartTime(serverPauseStartTime);

                // Kirim event untuk komponen timer lain
                window.dispatchEvent(new CustomEvent('timerStatusChanged', {
                  detail: {
                    rentalId,
                    isPaused: serverIsPaused,
                    pauseOffset: serverPauseOffset,
                    pauseStartTime: serverPauseStartTime
                  }
                }));
              }
            }
          } catch (error) {
            console.error("Error syncing timer on visibility change:", error);
          }
        };

        syncTimerStatus();
      }
    };

    // Tambahkan event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [rentalId, isPaused, pauseOffset, pauseStartTime]);

  // Effect untuk mengirim update saat status isPaused berubah
  useEffect(() => {
    logger.log("operation/countdown-timer: isPaused changed to:", isPaused);

    // Broadcast perubahan status ke komponen lain
    window.dispatchEvent(new CustomEvent('timerStatusChanged', {
      detail: {
        rentalId,
        isPaused,
        pauseOffset,
        pauseStartTime
      }
    }));
  }, [isPaused, pauseOffset, pauseStartTime, rentalId]);

  useEffect(() => {
    // Hitung total durasi dalam jam
    const bookedHours = getBookedHours(duration);
    const durationMs = bookedHours * 60 * 60 * 1000;

    // Hitung waktu selesai (waktu mulai + durasi)
    const startTime = new Date(operationalStart).getTime();
    const endTime = startTime + durationMs;

    const calculateTimeLeft = () => {
      // Jangan update sama sekali jika status jeda
      if (isPaused) return;

      try {
        const now = new Date().getTime();
        // Tambahkan offset jika ada (akumulasi waktu jeda)
        const adjustedEndTime = endTime + pauseOffset;
        const difference = adjustedEndTime - now;

        if (difference <= 0) {
          // Jika sudah overtime
          const overtimeDiff = Math.abs(difference);
          const overtimeHours = Math.floor((overtimeDiff / (1000 * 60 * 60)));
          const overtimeMinutes = Math.floor((overtimeDiff % (1000 * 60 * 60)) / (1000 * 60));
          const overtimeSeconds = Math.floor((overtimeDiff % (1000 * 60)) / 1000);

          setTimeLeft({
            hours: overtimeHours,
            minutes: overtimeMinutes,
            seconds: overtimeSeconds,
            isOvertime: true
          });
        } else {
          // Masih dalam durasi normal
          const hours = Math.floor((difference / (1000 * 60 * 60)));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((difference % (1000 * 60)) / 1000);

          setTimeLeft({
            hours,
            minutes,
            seconds,
            isOvertime: false
          });
        }
      } catch (error) {
        console.error("Error calculating time left:", error);
      }
    };

    // Jalankan perhitungan pertama kali jika tidak paused
    if (!isPaused) {
      calculateTimeLeft();
    }

    // Set interval untuk memperbarui setiap detik jika tidak paused
    let timer: NodeJS.Timeout | null = null;

    if (!isPaused) {
      timer = setInterval(calculateTimeLeft, 1000);
      logger.log("Timer interval created - isPaused:", isPaused);
    } else {
      logger.log("Timer paused - not creating interval");
    }

    // Clear interval saat komponen unmount atau status paused berubah
    return () => {
      if (timer) {
        logger.log("Clearing timer interval");
        clearInterval(timer);
      }
    };
  }, [operationalStart, duration, isPaused, pauseOffset]);

  const formatTime = (num: number) => {
    return num.toString().padStart(2, '0');
  };

  const togglePause = async () => {
    try {
      setIsToggling(true);
      const now = new Date().getTime();
      const newIsPaused = !isPaused;
      let newPauseOffset = pauseOffset;
      let newPauseStartTime = pauseStartTime;

      if (isPaused) {
        // Lanjutkan timer - tambahkan waktu jeda ke offset
        const pauseDuration = now - pauseStartTime;
        newPauseOffset = pauseOffset + pauseDuration;
        newPauseStartTime = 0;
        logger.log("operation/countdown-timer: Resuming timer, new pauseOffset:", newPauseOffset);
      } else {
        // Jeda timer - simpan waktu mulai jeda
        newPauseStartTime = now;
        logger.log("operation/countdown-timer: Pausing timer, pauseStartTime:", newPauseStartTime);
      }

      // Perbarui state lokal terlebih dahulu
      setIsPaused(newIsPaused);
      setPauseOffset(newPauseOffset);
      setPauseStartTime(newPauseStartTime);

      // Kirim event untuk komponen timer lain segera
      logger.log("operation/countdown-timer: Dispatching timerStatusChanged event");
      window.dispatchEvent(new CustomEvent('timerStatusChanged', {
        detail: {
          rentalId,
          isPaused: newIsPaused,
          pauseOffset: newPauseOffset,
          pauseStartTime: newPauseStartTime
        }
      }));

      // Perbarui status di server
      logger.log("operation/countdown-timer: Sending pause status to server");
      const response = await fetch('/api/operations/pause-timer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rentalId,
          isPaused: newIsPaused,
          pauseOffset: newPauseOffset,
          pauseStartTime: newPauseStartTime
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update timer status: ${response.status}`);
      }

      const responseData = await response.json();
      logger.log('Timer status updated successfully', responseData);
    } catch (error) {
      console.error('Error toggling timer:', error);
      showError('Error. Failed to update timer status. Please try again.');

      // Kembalikan status sebelumnya jika terjadi error
      setIsPaused(!isPaused);

      // Kirim event untuk mengembalikan status komponen lain
      window.dispatchEvent(new CustomEvent('timerStatusChanged', {
        detail: {
          rentalId,
          isPaused: !isPaused,
          pauseOffset,
          pauseStartTime
        }
      }));
    } finally {
      setIsToggling(false);
    }
  };

  // Render timer UI
  return (
    <div className="relative">
      {isLoading ? (
        <div className="animate-pulse flex flex-col items-center p-4 bg-slate-100 dark:bg-slate-800 rounded-md">
          <div className="h-6 w-40 bg-slate-200 dark:bg-slate-700 rounded mb-2"></div>
          <div className="h-8 w-24 bg-slate-200 dark:bg-slate-700 rounded"></div>
        </div>
      ) : hasError ? (
        <div className="bg-red-50 dark:bg-red-900/30 p-3 rounded-md border border-red-200 dark:border-red-800/50 text-center">
          <p className="text-red-700 dark:text-red-300 text-sm">Error loading timer data.</p>
          <button
            className="text-xs text-red-600 dark:text-red-400 mt-1 underline"
            onClick={() => window.location.reload()}
          >
            Refresh page
          </button>
        </div>
      ) : (
        <div>
          <div className={`rounded-lg p-4 relative ${timeLeft.isOvertime ? 'bg-orange-50 dark:bg-orange-950/40 border border-orange-200 dark:border-orange-900/50' : 'bg-violet-50 dark:bg-violet-950/40 border border-violet-200 dark:border-violet-900/50'}`}>
            {isPaused && (
              <div className="absolute -top-3 right-3 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                PAUSED
              </div>
            )}

            <div className="flex flex-col items-center">
              <div className="text-center mb-2">
                <div className={`text-2xl font-bold font-mono ${timeLeft.isOvertime ? 'text-orange-600 dark:text-orange-300' : 'text-violet-700 dark:text-violet-300'}`}>
                  {formatTime(timeLeft.hours)}:{formatTime(timeLeft.minutes)}:{formatTime(timeLeft.seconds)}
                </div>
                <div className={`text-sm font-medium ${timeLeft.isOvertime ? 'text-orange-600 dark:text-orange-300' : 'text-violet-700 dark:text-violet-300'}`}>
                  {timeLeft.isOvertime ? 'OVERTIME' : 'REMAINING'}
                </div>
              </div>

              {showPauseButton && (
                <div className="mt-2">
                  <Button
                    onClick={togglePause}
                    variant={isPaused ? "outline" : "default"}
                    size="sm"
                    className={`gap-1 ${isPaused ? 'bg-violet-50 dark:bg-violet-900/40 hover:bg-violet-100 dark:hover:bg-violet-800/50 text-violet-700 dark:text-violet-400 border-violet-200 dark:border-violet-800/60' : 'bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-700 dark:text-slate-300'}`}
                    disabled={isToggling}
                  >
                    {isToggling ? (
                      <>
                        <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></span>
                        <span>Loading...</span>
                      </>
                    ) : isPaused ? (
                      <>
                        <Play className="h-4 w-4" />
                        <span>Resume</span>
                      </>
                    ) : (
                      <>
                        <Pause className="h-4 w-4" />
                        <span>Pause</span>
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>

            {timeLeft.isOvertime && (
              <div className="mt-2 text-sm text-orange-600 dark:text-orange-300 text-center">
                <p>Biaya overtime berlaku ({Math.floor(timeLeft.hours + timeLeft.minutes / 60)} jam)</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
