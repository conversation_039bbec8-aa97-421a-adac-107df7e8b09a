import { formatCurrency } from "@/lib/utils/format";
import Link from "next/link";
import Image from "next/image";
import { ProductStatus } from "@prisma/client";

interface ProductCardProps {
    product: {
        id: string;
        name: string;
        price: number;
        description: string | null;
        status: ProductStatus;
        imageUrl: string | null;
        image: string | null;
        capacity: number;
        stock: number;
        user: {
            id: string;
            name: string | null;
        } | null;
        category: string | null;
    };
}

export function ProductCard({ product }: ProductCardProps) {
    return (
        <Link href={`/user/catalog/${product.id}`}>
            <div className="bg-card border border-border rounded-lg shadow-md overflow-hidden transition-all duration-200 hover:scale-105 hover:shadow-lg dark:shadow-xl">
                <div className="aspect-[4/3] relative">
                    <Image
                        src={product.imageUrl || '/placeholder.jpg'}
                        alt={product.name}
                        width={400}
                        height={300}
                        priority
                        className="w-full h-48 object-cover rounded-t-lg"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
                    />
                    <div className="absolute top-2 right-2">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full shadow-sm ${product.status === 'AVAILABLE'
                            ? 'bg-green-600 text-white dark:bg-green-700 dark:text-green-100'
                            : 'bg-red-600 text-white dark:bg-red-700 dark:text-red-100'}`}>
                            {product.status === 'AVAILABLE' ? 'Tersedia' : 'Tidak Tersedia'}
                        </span>
                    </div>
                </div>
                <div className="p-4">
                    <h3 className="text-lg font-semibold mb-2 line-clamp-1 text-card-foreground">{product.name}</h3>
                    <p className="text-muted-foreground text-sm mb-2 line-clamp-2">
                        {product.description || "Tidak ada deskripsi"}
                    </p>
                    <div className="flex justify-between items-center mt-4">
                        <span className="text-lg font-bold text-green-600 dark:text-green-400">
                            {formatCurrency(product.price)}/8 jam
                        </span>
                        <span className="text-sm font-medium text-white bg-green-600 dark:bg-green-700 px-2 py-1 rounded-md shadow-sm">
                            {product.capacity} KVA
                        </span>
                    </div>
                </div>
            </div>
        </Link>
    );
}
