"use client";

import { useState } from "react";
import { signOut } from "@/lib/auth/client";
import { But<PERSON> } from "@/app/components/ui/button";
import { LuLogOut, LuLoader } from "react-icons/lu";
import { cn } from "@/lib/utils/cn";
import { enhancedLogout } from "@/lib/utils/logout";

interface LogoutButtonProps {
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "gradient" | "violet" | "success";
  size?: "default" | "sm" | "lg" | "icon" | "mobile";
  showIcon?: boolean;
  children?: React.ReactNode;
}

export function LogoutButton({
  className,
  variant = "destructive",
  size = "mobile",
  showIcon = true,
  children
}: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await enhancedLogout(signOut);
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handleLogout}
      disabled={isLoading}
      variant={variant}
      size={size}
      className={cn(
        "flex items-center justify-center gap-2.5 min-h-[44px] transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
        "shadow-lg hover:shadow-xl",
        "focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
        isLoading && "cursor-not-allowed opacity-75",
        className
      )}
      aria-label={isLoading ? "Sedang keluar..." : "Keluar dari akun"}
    >
      {isLoading ? (
        <LuLoader className="h-5 w-5 animate-spin" />
      ) : (
        showIcon && <LuLogOut className="h-5 w-5" />
      )}
      <span className="font-medium">
        {isLoading ? "Keluar..." : (children || "Keluar")}
      </span>
    </Button>
  );
}
