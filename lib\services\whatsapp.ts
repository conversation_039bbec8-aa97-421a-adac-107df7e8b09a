export interface WhatsAppMessage {
  to: string;
  message: string;
  type: 'invoice' | 'payment_reminder' | 'confirmation' | 'general' | 'deposit_payment';
  metadata?: {
    invoiceNumber?: string;
    customerName?: string;
    amount?: number;
    depositAmount?: number;
    productName?: string;
    orderId?: string;
    oldStatus?: string;
    newStatus?: string;
    paymentTime?: string;
  };
}

export interface WhatsAppResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  method: 'manual' | 'api';
  timestamp: Date;
  whatsappURL?: string;
}



export class WhatsAppService {

  /**
   * Send WhatsApp message via Fonnte API
   */
  static async sendViaFonnteAPI(phoneNumber: string, message: string, orderId: string): Promise<void> {
    const token = process.env.FONNTE_API_TOKEN;

    if (!token) {
      console.log('⚠️ Fonnte API token not configured, skipping auto-send');
      return;
    }

    try {
      // Clean phone number
      const cleanPhone = phoneNumber.replace(/[^\d]/g, '');
      const formattedPhone = cleanPhone.startsWith('62') ? cleanPhone : `62${cleanPhone.startsWith('0') ? cleanPhone.slice(1) : cleanPhone}`;

      console.log(`📱 Sending WhatsApp via Fonnte API to: ${formattedPhone}`);

      const response = await fetch('https://api.fonnte.com/send', {
        method: 'POST',
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          target: formattedPhone,
          message: message,
          countryCode: '62'
        })
      });

      const result = await response.json();

      if (response.ok && result.status) {
        console.log(`✅ WhatsApp sent successfully via Fonnte API! Message ID: ${result.id || 'unknown'}`);
        console.log(`🎉 Admin notification delivered to +${formattedPhone} for order ${orderId}`);
      } else {
        console.error('❌ Fonnte API error:', result);

        // Specific error handling
        if (result.reason === 'request invalid on disconnected device') {
          console.log(`📱 DEVICE NOT CONNECTED: WhatsApp device +${formattedPhone} is not connected to Fonnte`);
          console.log(`🔧 SOLUTION: Connect your WhatsApp to Fonnte dashboard or use manual URL below`);
        } else if (result.reason === 'invalid token') {
          console.log(`🔑 INVALID TOKEN: Fonnte API token is invalid or expired`);
          console.log(`🔧 SOLUTION: Update FONNTE_API_TOKEN in .env file`);
        } else {
          console.log(`⚠️ API ERROR: ${result.reason || 'Unknown error'}`);
        }

        console.log(`📋 MANUAL FALLBACK: Use WhatsApp URL from console below`);
      }

    } catch (error) {
      console.error('❌ WhatsApp API error:', error);
      console.log(`⚠️ Failed to send via API, admin can use WhatsApp URL from console`);
    }
  }



  /**
   * Generate WhatsApp message for admin new order notification
   */
  static generateAdminOrderNotification(
    orderId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    productName: string,
    productCapacity: string,
    startDate: Date,
    endDate: Date,
    location: string,
    orderDate: Date,
    orderStatus: string = 'New Order'
  ): string {
    const formattedOrderDate = orderDate.toLocaleString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const formattedStartDate = startDate.toLocaleDateString('id-ID');
    const formattedEndDate = endDate.toLocaleDateString('id-ID');

    return `🚨 NEW RENTAL ORDER ALERT! 🚨

📋 Order Details:
• Order ID: ${orderId}
• Status: ${orderStatus}
• Date/Time: ${formattedOrderDate}

👤 Customer Information:
• Name: ${customerName}
• Phone: ${customerPhone}
• Email: ${customerEmail || 'Not provided'}

🔧 Product Details:
• Product: ${productName}
• Capacity: ${productCapacity}
• Rental Period: ${formattedStartDate} - ${formattedEndDate}
• Location: ${location || 'To be confirmed'}

⚡ Action Required:
• Review order details
• Confirm availability
• Contact customer for confirmation
• Process payment if needed

📱 Quick Actions:
Reply to this message to take action or check the admin dashboard for full details.

Rental Genset Admin System 🔧`;
  }



  /**
   * Generate WhatsApp URL for manual sending
   */
  static generateWhatsAppURL(phoneNumber: string, message: string): string {
    // Clean phone number (remove +, spaces, etc.)
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '');

    // Ensure it starts with country code (62 for Indonesia)
    const formattedPhone = cleanPhone.startsWith('62') ? cleanPhone : `62${cleanPhone.startsWith('0') ? cleanPhone.slice(1) : cleanPhone}`;

    // Encode message for URL
    const encodedMessage = encodeURIComponent(message);

    return `https://wa.me/${formattedPhone}?text=${encodedMessage}`;
  }

  /**
   * Log WhatsApp message for tracking
   */
  static async logMessage(whatsappMessage: WhatsAppMessage): Promise<void> {
    try {
      const logEntry = {
        ...whatsappMessage,
        timestamp: new Date(),
        id: `wa_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };

      console.log('📱 WhatsApp Message Log:', JSON.stringify(logEntry, null, 2));

      // In production, you would store this in database:
      // await prisma.whatsappLog.create({ data: logEntry });

    } catch (error) {
      console.error('Failed to log WhatsApp message:', error);
    }
  }

  /**
   * Send WhatsApp message (manual method with auto-open for admin)
   */
  static async sendMessage(
    phoneNumber: string,
    message: string,
    type: WhatsAppMessage['type'] = 'general',
    metadata?: WhatsAppMessage['metadata']
  ): Promise<WhatsAppResponse> {
    try {
      // Log the message
      await this.logMessage({
        to: phoneNumber,
        message,
        type,
        metadata
      });

      // Generate WhatsApp URL for manual sending
      const whatsappURL = this.generateWhatsAppURL(phoneNumber, message);

      console.log(`📱 WhatsApp message prepared for ${phoneNumber}`);
      console.log(`🔗 WhatsApp URL: ${whatsappURL}`);

      // For admin notifications, try to auto-open WhatsApp (in development)
      const adminPhone = process.env.WHATSAPP_ADMIN_PHONE || process.env.WHATSAPP_BUSINESS_PHONE;
      if (phoneNumber === adminPhone) {
        console.log(`🚨 ADMIN NOTIFICATION: New order requires attention!`);
        console.log(`📱 Auto-opening WhatsApp for admin notification...`);
        console.log(`🔗 URL: ${whatsappURL}`);

        // Create a clear log that admin can see
        console.log(`\n🔔 ===== ADMIN ALERT ===== 🔔`);
        console.log(`📋 New rental order received!`);
        console.log(`👤 Customer: ${metadata?.customerName || 'Unknown'}`);
        console.log(`📦 Product: ${metadata?.productName || 'Unknown'}`);
        console.log(`🆔 Order ID: ${metadata?.orderId || 'Unknown'}`);
        console.log(`⏰ Time: ${new Date().toLocaleString('id-ID')}`);
        console.log(`📱 WhatsApp URL ready: ${whatsappURL}`);
        console.log(`🔔 ======================= 🔔\n`);

        // Try to send via Fonnte API if configured
        await this.sendViaFonnteAPI(adminPhone, message, metadata?.orderId || 'unknown');
      }

      return {
        success: true,
        messageId: `wa_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        method: 'manual',
        timestamp: new Date(),
        whatsappURL
      };

    } catch (error) {
      console.error('WhatsApp message error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        method: 'manual',
        timestamp: new Date()
      };
    }
  }



  /**
   * Generate WhatsApp message for admin deposit payment notification
   */
  static generateAdminDepositNotification(
    orderId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    productName: string,
    depositAmount: number,
    paymentTime: Date = new Date()
  ): string {
    const formattedDepositAmount = depositAmount.toLocaleString('id-ID');
    const formattedPaymentTime = paymentTime.toLocaleString('id-ID');

    return `💰 DEPOSIT PAYMENT RECEIVED! 💰

📋 Payment Details:
• Order ID: ${orderId}
• Payment Type: Deposit Payment
• Amount: Rp ${formattedDepositAmount}
• Payment Time: ${formattedPaymentTime}
• Status: ✅ PAID

👤 Customer Information:
• Name: ${customerName}
• Phone: ${customerPhone}
• Email: ${customerEmail}

🔧 Product Details:
• Product: ${productName}
• Rental Status: ACTIVE (Deposit Paid)

⚡ Next Actions Required:
• Prepare equipment for delivery
• Schedule delivery/pickup time
• Contact customer for confirmation
• Update rental status to "IN_PROGRESS"

📱 Quick Actions:
Reply to this message or check admin dashboard for full order details.

Rental Genset Admin System 🔧`;
  }

  /**
   * Send admin notification for deposit payment
   */
  static async sendAdminDepositNotification(
    orderId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    productName: string,
    depositAmount: number,
    paymentTime: Date = new Date()
  ): Promise<WhatsAppResponse> {
    const adminPhone = process.env.WHATSAPP_ADMIN_PHONE;

    if (!adminPhone) {
      console.error('Admin WhatsApp phone number not configured');
      return {
        success: false,
        error: 'Admin WhatsApp phone number not configured',
        method: 'manual',
        timestamp: new Date()
      };
    }

    const message = this.generateAdminDepositNotification(
      orderId,
      customerName,
      customerPhone,
      customerEmail,
      productName,
      depositAmount,
      paymentTime
    );

    return this.sendMessage(adminPhone, message, 'deposit_payment', {
      orderId,
      customerName,
      depositAmount,
      productName,
      paymentTime: paymentTime.toISOString()
    });
  }

  /**
   * Send admin notification for new order
   */
  static async sendAdminOrderNotification(
    orderId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    productName: string,
    productCapacity: string,
    startDate: Date,
    endDate: Date,
    location: string,
    orderDate: Date,
    orderStatus: string = 'New Order'
  ): Promise<WhatsAppResponse> {
    const adminPhone = process.env.WHATSAPP_ADMIN_PHONE || process.env.WHATSAPP_BUSINESS_PHONE;

    if (!adminPhone) {
      console.error('Admin WhatsApp phone number not configured');
      return {
        success: false,
        error: 'Admin WhatsApp phone number not configured',
        method: 'manual',
        timestamp: new Date()
      };
    }

    const message = this.generateAdminOrderNotification(
      orderId,
      customerName,
      customerPhone,
      customerEmail,
      productName,
      productCapacity,
      startDate,
      endDate,
      location,
      orderDate,
      orderStatus
    );

    return this.sendMessage(adminPhone, message, 'general', {
      orderId,
      customerName,
      productName
    });
  }


}
