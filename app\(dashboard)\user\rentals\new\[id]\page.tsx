"use client";

import { useParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { LuArrowLeft } from "react-icons/lu";
import { useProductData } from "@/lib/hooks/product/use-product-data";
import { useRentalForm } from "@/lib/hooks/rental/use-rental-form";
import { RentalFormNew } from "@/app/components/rental/rental-form-new";
import { ProductSummary } from "@/app/components/rental/product-summary";

export default function RentPage() {
  const params = useParams();
  const { product, loading, isKnownUser } = useProductData();
  const formProps = useRentalForm(product, isKnownUser);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p className="text-muted-foreground">Memuat...</p>
      </div>
    );
  }
  
  if (!product) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-xl text-muted-foreground mb-4">Produk tidak ditemukan</p>
        <Link href="/user/catalog">
          <Button>Kembali ke Katalog</Button>
        </Link>
      </div>
    );
  }
  
  return (
    <>
      <div className="flex items-center mb-8">
        <Link href={`/user/product/${params.id}`} className="mr-4">
          <Button variant="outline" size="sm" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">
            <LuArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Sewa Genset</h1>
          <p className="text-muted-foreground">
            Isi detail penyewaan untuk melanjutkan
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RentalFormNew
            startDate={formProps.startDate}
            setStartDate={formProps.setStartDate}
            endDate={formProps.endDate}
            setEndDate={formProps.setEndDate}
            duration={formProps.duration}
            setDuration={formProps.setDuration}
            address={formProps.address}
            setAddress={formProps.setAddress}
            notes={formProps.notes}
            setNotes={formProps.setNotes}
            arrivalTime={formProps.arrivalTime}
            setArrivalTime={formProps.setArrivalTime}
            coordinates={formProps.coordinates}
            setCoordinates={formProps.setCoordinates}
            formErrors={formProps.formErrors}
            validateAddress={formProps.validateAddress}
          />
        </div>

        <div className="lg:col-span-1">
          <ProductSummary
            product={product}
            durationDays={formProps.durationDays}
            totalPrice={formProps.totalPrice}
            isKnownUser={isKnownUser}
            isSubmitting={formProps.isSubmitting}
            isFormValid={formProps.isFormValid}
            onConfirmOrder={formProps.handleConfirmOrder}
          />
        </div>
      </div>
    </>
  );
}
