import { Skeleton } from "@/app/components/ui/skeleton";

interface TableSkeletonProps {
  rowCount?: number;
  variant?: "default" | "violet" | "purple";
}

export function TableSkeleton({
  rowCount = 5,
  variant = "default"
}: TableSkeletonProps) {
  return (
    <div className="w-full overflow-hidden rounded-lg border border-border">
      {/* Table Header - Responsive */}
      <div className="hidden sm:grid grid-cols-5 gap-4 bg-gray-50 dark:bg-gray-800 p-4">
        <Skeleton className="h-4 w-full max-w-24" variant={variant} />
        <Skeleton className="h-4 w-full max-w-32" variant={variant} />
        <Skeleton className="h-4 w-full max-w-24" variant={variant} />
        <Skeleton className="h-4 w-full max-w-24" variant={variant} />
        <Skeleton className="h-4 w-full max-w-16 ml-auto" variant={variant} />
      </div>

      {/* Mobile Header */}
      <div className="sm:hidden p-3 bg-gray-50 dark:bg-gray-800">
        <Skeleton className="h-4 w-32" variant={variant} />
      </div>

      {/* Table Rows - Responsive */}
      <div className="divide-y divide-border">
        {Array.from({ length: rowCount }).map((_, index) => (
          <div key={index} className="p-4 grid grid-cols-1 sm:grid-cols-5 gap-4">
            {/* Desktop row */}
            <div className="hidden sm:flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" variant={variant} />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-full" variant={variant} />
                <Skeleton className="h-3 w-full max-w-16" variant={variant} />
              </div>
            </div>
            
            {/* Mobile row */}
            <div className="sm:hidden flex items-center space-x-3">
              <Skeleton className="h-12 w-12 rounded-full" variant={variant} />
              <div className="space-y-1 flex-1">
                <Skeleton className="h-4 w-full" variant={variant} />
                <Skeleton className="h-3 w-full max-w-20" variant={variant} />
              </div>
            </div>

            <div className="hidden sm:block">
              <div className="space-y-2 w-full">
                <Skeleton className="h-4 w-full" variant={variant} />
                <Skeleton className="h-3 w-full" variant={variant} />
              </div>
            </div>
            
            <div className="hidden sm:block">
              <Skeleton className="h-6 w-full max-w-24" variant={variant} />
            </div>
            
            <div className="hidden sm:block">
              <Skeleton className="h-4 w-full max-w-32" variant={variant} />
            </div>
            
            <div className="flex justify-end sm:ml-auto">
              <div className="flex space-x-2">
                <Skeleton className="h-10 w-10 rounded-lg" variant={variant} />
                <Skeleton className="h-10 w-10 rounded-lg" variant={variant} />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}