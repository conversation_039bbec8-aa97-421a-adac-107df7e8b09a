'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { LuBolt, LuClock, LuShield, LuTruck, LuPhone, LuArrowRight, LuCheck } from 'react-icons/lu';

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role: string;
}

interface Product {
  id: string;
  name: string;
  capacity?: number;
  price?: number;
  imageUrl?: string;
  description?: string;
  user?: User;
}

interface HomeContentProps {
  products?: Product[];
}

export default function HomeContent({ products = [] }: HomeContentProps) {
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Event Organizer',
      content: 'Rental Genset telah membantu acara kami berjalan lancar tanpa gangguan listrik. Layanan cepat dan genset berkualitas!',
      image: '/images/testimonial-1.jpg'
    },
    {
      name: '<PERSON><PERSON>',
      role: '<PERSON><PERSON><PERSON><PERSON>',
      content: 'Sangat puas dengan pelayanan yang diberikan. Genset datang tepat waktu dan beroperasi dengan sangat baik selama proyek berlangsung.',
      image: '/images/testimonial-2.jpg'
    },
    {
      name: 'Budi Santoso',
      role: 'Wedding Organizer',
      content: 'Saya telah menggunakan jasa Rental Genset untuk berbagai acara pernikahan. Mereka sangat profesional dan genset selalu dalam kondisi prima.',
      image: '/images/testimonial-3.jpg'
    }
  ];

  return (
    <div className="w-full">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 dark:from-blue-900 dark:to-blue-950 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-32 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 md:pr-12 mb-12 md:mb-0">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Solusi Genset Terpercaya untuk Kebutuhan Daya Anda
              </h1>
              <p className="text-lg md:text-xl opacity-90 mb-8">
                Kami menyediakan layanan sewa genset berkualitas tinggi dengan berbagai kapasitas untuk keperluan bisnis, acara, dan proyek konstruksi Anda.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/user/catalog">
                  <button className="px-8 py-3 bg-white text-blue-700 dark:bg-gray-100 dark:text-blue-800 font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md">
                    Sewa Sekarang
                  </button>
                </Link>
                <Link href="/user/catalog">
                  <button className="px-8 py-3 border border-white text-white font-medium rounded-lg hover:bg-white/20 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white shadow-md">
                    Lihat Katalog
                  </button>
                </Link>
              </div>
            </motion.div>
          </div>
          <div className="md:w-1/2">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative h-72 sm:h-80 md:h-96 w-full rounded-lg overflow-hidden shadow-2xl"
            >
              <Image
                src="/images/hero-genset.svg"
                alt="Genset Profesional"
                fill
                className="object-cover"
                priority
              />
            </motion.div>
          </div>
        </div>

        {/* Stats */}
        <div className="bg-white/10 dark:bg-white/5 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <p className="text-3xl md:text-4xl font-bold">100+</p>
                <p className="text-sm mt-1 opacity-80">Unit Genset</p>
              </div>
              <div className="text-center">
                <p className="text-3xl md:text-4xl font-bold">5000+</p>
                <p className="text-sm mt-1 opacity-80">Pelanggan Puas</p>
              </div>
              <div className="text-center">
                <p className="text-3xl md:text-4xl font-bold">24/7</p>
                <p className="text-sm mt-1 opacity-80">Dukungan Teknis</p>
              </div>
              <div className="text-center">
                <p className="text-3xl md:text-4xl font-bold">10+</p>
                <p className="text-sm mt-1 opacity-80">Tahun Pengalaman</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
              Mengapa Memilih Kami?
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Kami menyediakan solusi genset terbaik dengan layanan yang lengkap dan handal
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuBolt className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">Genset Berkualitas</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Unit genset kami selalu terjaga dengan perawatan rutin untuk memastikan performa terbaik saat dibutuhkan.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuClock className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">Pengiriman Cepat</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Kami menjamin ketepatan waktu pengiriman dan pemasangan genset di lokasi Anda.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuShield className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">Keamanan Terjamin</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Genset kami dilengkapi dengan fitur keamanan untuk mencegah kerusakan dan kecelakaan.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuTruck className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">Layanan Menyeluruh</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Termasuk pengiriman, pemasangan, monitoring, dan technical support selama masa sewa.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuPhone className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">24/7 Dukungan</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Tim teknisi kami siap membantu Anda kapan saja dengan penanganan gangguan yang cepat.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ y: -10 }}
              className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-md hover:shadow-lg transition"
            >
              <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                <LuCheck className="w-7 h-7 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900 dark:text-gray-100">Harga Transparan</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Tanpa biaya tersembunyi. Anda hanya membayar sesuai dengan layanan yang Anda pilih.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Products Preview */}
      <section className="py-20 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
              Pilihan Genset Kami
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Tersedia berbagai kapasitas untuk memenuhi kebutuhan daya Anda
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {products.map((product) => (
              <div key={product.id} className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition">
                <div className="relative h-56 w-full">
                  <Image
                    src={product.imageUrl || "/images/genset-placeholder.jpg"}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <div className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">{product.capacity} KVA</div>
                  <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-gray-100">{product.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {product.description}
                  </p>
                  <Link href={`/user/catalog/${product.id}`} className="text-blue-600 dark:text-blue-400 font-medium inline-flex items-center hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                    Lihat Detail
                    <LuArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/user/catalog">
              <button className="px-8 py-3 bg-blue-600 dark:bg-blue-700 text-white font-medium rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-md">
                Lihat Semua Produk
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100">
              Apa Kata Pelanggan Kami
            </h2>
            <p className="mt-4 text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Pengalaman dari pelanggan yang telah menggunakan jasa kami
            </p>
          </div>

          <div className="relative">
            <div className="overflow-hidden">
              <div className="relative h-64 sm:h-80 md:h-96">
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: index === activeTestimonial ? 1 : 0 }}
                    transition={{ duration: 0.5 }}
                    className="absolute inset-0 flex flex-col md:flex-row items-center"
                    style={{ display: index === activeTestimonial ? 'flex' : 'none' }}
                  >
                    <div className="md:w-1/3 p-6">
                      <div className="relative h-24 w-24 rounded-full overflow-hidden mx-auto mb-4">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <h3 className="text-xl font-bold text-center text-gray-900 dark:text-gray-100">{testimonial.name}</h3>
                      <p className="text-blue-600 dark:text-blue-400 text-center">{testimonial.role}</p>
                    </div>
                    <div className="md:w-2/3 p-6">
                      <p className="text-xl italic text-gray-700 dark:text-gray-300">&ldquo;{testimonial.content}&rdquo;</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Testimonial Navigation */}
            <div className="flex justify-center mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveTestimonial(index)}
                  className={`w-3 h-3 rounded-full mx-1 ${
                    index === activeTestimonial ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="bg-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-8 md:mb-0 md:pr-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Siap untuk Menyewa Genset?
              </h2>
              <p className="text-lg opacity-90">
                Hubungi kami sekarang untuk mendapatkan penawaran terbaik sesuai kebutuhan Anda.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/user/catalog">
                <button className="px-8 py-3 bg-white text-blue-700 font-medium rounded-lg hover:bg-gray-100 transition focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                  Sewa Sekarang
                </button>
              </Link>
              <Link href="/contact">
                <button className="px-8 py-3 border border-white text-white font-medium rounded-lg hover:bg-white/10 transition focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white">
                  Hubungi Kami
                </button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
