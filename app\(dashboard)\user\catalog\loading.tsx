import { Skeleton } from "@/app/components/ui/skeleton";

export default function CatalogLoading() {
  return (
    <>
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat"></div>
        <div className="relative">
          <Skeleton className="h-9 w-48 mb-3 animate-pulse" />
          <Skeleton className="h-4 w-96 animate-pulse" />
        </div>
      </div>

      {/* Filter Section Skeleton */}
      <div className="mb-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div className="p-5 border-b border-gray-100 dark:border-gray-700 flex items-center justify-between">
          <div>
            <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-64 animate-pulse" />
          </div>
          <Skeleton className="h-9 w-9 animate-pulse" />
        </div>
        <div className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
            <Skeleton className="h-10 w-full animate-pulse" />
            <Skeleton className="h-10 w-full animate-pulse" />
            <Skeleton className="h-10 w-full animate-pulse" />
            <Skeleton className="h-11 w-full animate-pulse" />
          </div>

          {/* Quick filter chips skeleton */}
          <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
            <Skeleton className="h-4 w-20 animate-pulse" />
            <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-28 rounded-full animate-pulse" />
            <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
          </div>
        </div>
      </div>

      {/* Results Header Skeleton */}
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-4 w-40 animate-pulse" />
        <Skeleton className="h-10 w-48 animate-pulse" />
      </div>

      {/* Product Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="group bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden flex flex-col">
            {/* Product Image Skeleton */}
            <div className="aspect-video w-full overflow-hidden bg-gray-50 dark:bg-gray-900 relative">
              <Skeleton className="w-full h-full animate-pulse" />
              <div className="absolute top-3 right-3">
                <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
              </div>
            </div>

            {/* Product Content Skeleton */}
            <div className="p-5">
              <Skeleton className="h-6 w-3/4 mb-1 animate-pulse" />
              <Skeleton className="h-7 w-32 mb-4 animate-pulse" />

              {/* Product Features Grid Skeleton */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                {Array.from({ length: 4 }).map((_, j) => (
                  <div key={j} className="flex flex-col items-center p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Skeleton className="h-5 w-5 mb-1 animate-pulse" />
                    <Skeleton className="h-3 w-12 animate-pulse" />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Actions Skeleton */}
            <div className="mt-auto p-4 pt-0 flex gap-2">
              <Skeleton className="h-11 flex-1 animate-pulse" />
              <Skeleton className="h-11 flex-1 animate-pulse" />
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button Skeleton */}
      <div className="mt-10 pb-10 flex justify-center">
        <Skeleton className="h-11 w-40 animate-pulse" />
      </div>
    </>
  );
}
