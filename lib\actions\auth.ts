"use server";

// Note: All authentication functions are now handled by Better Auth
// Use signIn and signUp from @/lib/auth/client instead

// This file is kept for potential future server-side auth utilities
// All signup/signin functionality is now handled by Better Auth client-side

export async function createTestUser() {
    try {
        // Hapus user test yang mungkin sudah ada
        await prisma.user.deleteMany({
            where: {
                email: "<EMAIL>"
            }
        });

        // Buat user test baru
        const testUser = await prisma.user.create({
            data: {
                name: "Test User",
                email: "<EMAIL>",
                phone: "081234567890",
                password: await hash("password123", 10),
                role: "USER"
            }
        });

        console.log("Test user created:", testUser.id);
        return { success: true };
    } catch (error) {
        console.error("Error creating test user:", error);
        return { success: false };
    }
}

export async function updateUserRole(userId: string, newRole: Role): Promise<{ success: boolean; message: string }> {
    try {
        const response = await fetch('/api/users/role', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ userId, role: newRole })
        });

        if (!response.ok) {
            throw new Error('Failed to update role');
        }

        await response.json(); // Tunggu response tapi tidak perlu simpan datanya
        
        return {
            success: true,
            message: `Role berhasil diubah menjadi ${newRole}`
        };
    } catch (error) {
        console.error("Update role error:", error);
        return {
            success: false,
            message: "Gagal mengubah role"
        };
    }
}
