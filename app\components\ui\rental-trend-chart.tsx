"use client"

import * as React from "react"
import { 
  ResponsiveContainer, 
  XAxis, 
  YAxis, 
  Tooltip,
  TooltipProps,
  CartesianGrid,
  Area,
  AreaChart
} from "recharts"

interface RentalTrendChartProps {
  data: { month: string; count: number }[]
}

export function RentalTrendChart({ data }: RentalTrendChartProps) {
  // Kustomisasi tooltip untuk menampilkan nilai lengkap
  const CustomTooltip = ({ 
    active, 
    payload, 
    label 
  }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm dark:border-gray-700" style={{ 
          backgroundColor: 'var(--tooltip-bg)',
          borderColor: 'var(--tooltip-border)'
        }}>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                Bulan
              </span>
              <span className="font-bold text-foreground">{label}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                Jumlah
              </span>
              <span className="font-bold text-violet-600 dark:text-violet-400">
                {(payload[0].value as number).toLocaleString('id-ID')} penyewaan
              </span>
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  // Deteksi tema saat ini
  const [theme, setTheme] = React.useState<'light' | 'dark'>('light');
  
  // Efek untuk memantau perubahan tema
  React.useEffect(() => {
    // Deteksi tema saat ini
    const isDark = document.documentElement.classList.contains('dark');
    setTheme(isDark ? 'dark' : 'light');
    
    // Pantau perubahan tema
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const isDarkNow = document.documentElement.classList.contains('dark');
          setTheme(isDarkNow ? 'dark' : 'light');
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);

  // Sesuaikan warna berdasarkan tema
  const strokeColor = theme === 'dark' ? '#8b5cf6' : '#7c3aed'; // violet
  const fillColor = theme === 'dark' ? '#8b5cf6' : '#7c3aed'; // violet
  const dotFill = theme === 'dark' ? '#1f2937' : '#ffffff';
  const activeDotFill = theme === 'dark' ? '#a78bfa' : '#6d28d9'; // purple to violet
  const gridColor = theme === 'dark' ? '#374151' : '#e5e7eb';
  const axisColor = theme === 'dark' ? '#9ca3af' : '#888888';

  return (
    <ResponsiveContainer width="100%" height={350}>
      <AreaChart 
        data={data} 
        margin={{ top: 10, right: 30, left: 5, bottom: 0 }}
      >
        <defs>
          <linearGradient id="colorCount" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={fillColor} stopOpacity={0.8}/>
            <stop offset="95%" stopColor={fillColor} stopOpacity={0.1}/>
          </linearGradient>
        </defs>
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke={gridColor}
          vertical={false} 
        />
        <XAxis
          dataKey="month"
          stroke={axisColor}
          fontSize={12}
          tickLine={false}
          axisLine={false}
          padding={{ left: 10, right: 10 }}
          style={{ fill: theme === 'dark' ? '#d1d5db' : '#6b7280' }}
        />
        <YAxis
          stroke={axisColor}
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => `${value}`}
          tickCount={5}
          domain={[0, 'auto']}
          style={{ fill: theme === 'dark' ? '#d1d5db' : '#6b7280' }}
        />
        <Tooltip 
          content={<CustomTooltip />}
          cursor={{ stroke: theme === 'dark' ? '#4b5563' : '#f3f4f6', strokeWidth: 1 }}
        />
        <Area
          type="monotone"
          dataKey="count"
          stroke={strokeColor}
          strokeWidth={2}
          fillOpacity={1}
          fill="url(#colorCount)"
          dot={{ r: 3, strokeWidth: 2, fill: dotFill, stroke: strokeColor }}
          activeDot={{ r: 5, strokeWidth: 0, fill: activeDotFill }}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
} 
