import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await getSession();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "No session" }, { status: 401 });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Check account table for password
    const account = await prisma.account.findFirst({
      where: { userId: session.user.id },
      select: {
        id: true,
        providerId: true,
        password: true,
        type: true,
      },
    });

    return NextResponse.json({
      session: {
        userId: session.user.id,
        userEmail: session.user.email,
        userName: session.user.name,
        userRole: session.user.role,
      },
      database: user ? {
        id: user.id,
        email: user.email,
        name: user.name,
        hasPassword: !!user.password,
        passwordLength: user.password ? user.password.length : 0,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      } : null,
      account: account ? {
        id: account.id,
        providerId: account.providerId,
        type: account.type,
        hasPassword: !!account.password,
        passwordLength: account.password ? account.password.length : 0,
      } : null,
    });
  } catch (error) {
    console.error("Debug error:", error);
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}
