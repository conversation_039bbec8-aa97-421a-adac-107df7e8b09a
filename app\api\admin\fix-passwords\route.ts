import { NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import * as bcrypt from "bcrypt-ts";

export async function POST() {
  try {
    const session = await getSession();
    
    // <PERSON>ya admin yang bisa menjalankan ini
    if (!session?.user?.role || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 403 }
      );
    }

    console.log("Starting password fix process...");

    // Cari semua account dengan password yang tidak dalam format bcrypt
    const accounts = await prisma.account.findMany({
      where: {
        providerId: "credential",
        password: {
          not: null
        }
      },
      select: {
        id: true,
        userId: true,
        password: true
      }
    });

    console.log(`Found ${accounts.length} accounts with passwords`);

    const fixedAccounts = [];
    const skippedAccounts = [];

    for (const account of accounts) {
      if (!account.password) continue;

      // Get user info separately since there's no direct relation in the schema
      const user = await prisma.user.findUnique({
        where: { id: account.userId },
        select: { email: true, name: true }
      });

      if (!user) {
        console.log(`Skipping account ${account.id} - user not found`);
        skippedAccounts.push({
          accountId: account.id,
          reason: "User not found"
        });
        continue;
      }

      // Cek apakah password sudah dalam format bcrypt (60 karakter, dimulai dengan $2)
      if (account.password.length === 60 && account.password.startsWith('$2')) {
        console.log(`Skipping account ${user.email} - already bcrypt format`);
        skippedAccounts.push({
          email: user.email,
          reason: "Already bcrypt format"
        });
        continue;
      }

      try {
        // Jika password adalah plain text atau format lain, hash dengan bcrypt
        let newHashedPassword;
        
        if (account.password === 'password123' || account.password === 'admin123') {
          // Password default yang diketahui
          newHashedPassword = await bcrypt.hash(account.password, 10);
          console.log(`Fixed default password for ${user.email}`);
        } else {
          // Untuk password lain, kita asumsikan itu adalah plain text
          // HANYA UNTUK DEVELOPMENT - di production harus lebih hati-hati
          newHashedPassword = await bcrypt.hash(account.password, 10);
          console.log(`Fixed password for ${user.email} (assumed plain text)`);
        }

        // Update password di database
        await prisma.account.update({
          where: { id: account.id },
          data: { password: newHashedPassword }
        });

        fixedAccounts.push({
          email: user.email,
          name: user.name,
          oldLength: account.password.length,
          newLength: newHashedPassword.length
        });

      } catch (error) {
        console.error(`Error fixing password for ${user.email}:`, error);
        skippedAccounts.push({
          email: user.email,
          reason: `Error: ${(error as Error).message}`
        });
      }
    }

    // Juga cek tabel user untuk password yang mungkin ada di sana
    const users = await prisma.user.findMany({
      where: {
        password: {
          not: null
        }
      },
      select: {
        id: true,
        email: true,
        name: true,
        password: true
      }
    });

    console.log(`Found ${users.length} users with passwords in user table`);

    for (const user of users) {
      if (!user.password) continue;

      // Cek apakah password sudah dalam format bcrypt
      if (user.password.length === 60 && user.password.startsWith('$2')) {
        console.log(`Skipping user ${user.email} - already bcrypt format`);
        continue;
      }

      try {
        // Hash password dengan bcrypt
        const newHashedPassword = await bcrypt.hash(user.password, 10);

        // Update password di database
        await prisma.user.update({
          where: { id: user.id },
          data: { password: newHashedPassword }
        });

        fixedAccounts.push({
          email: user.email,
          name: user.name,
          oldLength: user.password.length,
          newLength: newHashedPassword.length,
          table: 'user'
        });

        console.log(`Fixed password in user table for ${user.email}`);

      } catch (error) {
        console.error(`Error fixing user password for ${user.email}:`, error);
        skippedAccounts.push({
          email: user.email,
          reason: `User table error: ${(error as Error).message}`
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: "Password fix process completed",
      summary: {
        totalAccountsChecked: accounts.length,
        totalUsersChecked: users.length,
        fixed: fixedAccounts.length,
        skipped: skippedAccounts.length
      },
      details: {
        fixedAccounts,
        skippedAccounts
      }
    });

  } catch (error) {
    console.error("Error in password fix process:", error);
    return NextResponse.json(
      { error: "Internal server error: " + (error as Error).message },
      { status: 500 }
    );
  }
}
