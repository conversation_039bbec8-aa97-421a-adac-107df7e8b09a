"use client";

import { useState, useCallback, useRef, useEffect } from "react";
import { Input } from "@/app/components/ui/input";
import { SoundButton } from "@/app/components/ui/sound-button";
import { LuSearch, LuX } from "react-icons/lu";
import { SearchResult, MapSearchProps } from "@/lib/types/map";
import { useMapSearch } from "@/lib/hooks/useMapSearch";

export function MapSearch({
  onSearchResult,
  onSelectResult,
  initialQuery = "",
  placeholder = "Cari lokasi...",
  className = ""
}: MapSearchProps) {
  const { searchResults, isLoading, statusMessage, searchLocation } = useMapSearch();
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Efek untuk memantau hasil pencarian dan memperbarui hasil
  useEffect(() => {
    onSearchResult(searchResults);
  }, [searchResults, onSearchResult]);

  // Handler pencarian
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim()) return;

    try {
      await searchLocation(searchQuery);
      setShowSearchResults(true);
    } catch (error) {
      console.error("Error dalam pencarian:", error);
    }
  }, [searchQuery, searchLocation]);

  // Handler keyboard untuk memicu pencarian dengan Enter
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  }, [handleSearch]);

  // Handler untuk mengklik hasil pencarian
  const handleResultClick = useCallback((result: SearchResult) => {
    if (onSelectResult) {
      onSelectResult(result);
    }

    // Update search query with the selected location name
    setSearchQuery(result.display_name);
    setShowSearchResults(false);
  }, [onSelectResult]);

  // Handler untuk menutup hasil pencarian saat klik di luar
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery("");
    searchInputRef.current?.focus();
  }, []);

  return (
    <div className={`relative w-full ${className}`}>
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Input
            ref={searchInputRef}
            type="text"
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            className="pr-8"
          />
          {searchQuery && (
            <button
              onClick={handleClearSearch}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              aria-label="Clear search"
            >
              <LuX size={16} />
            </button>
          )}
        </div>
        <SoundButton
          onClick={handleSearch}
          disabled={isLoading || !searchQuery.trim()}
          className="flex items-center"
          soundType={isLoading || !searchQuery.trim() ? "none" : "click"}
        >
          <LuSearch className="mr-2" />
          Cari
        </SoundButton>
      </div>

      {/* Status message */}
      {statusMessage && (
        <div className="mt-2 text-sm text-gray-500">{statusMessage}</div>
      )}

      {/* Search Results */}
      {showSearchResults && searchResults.length > 0 && (
        <div
          ref={resultsRef}
          className="absolute z-10 mt-1 w-full max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-lg"
        >
          <ul className="divide-y divide-gray-100">
            {searchResults.map((result, index) => (
              <li
                key={`${result.display_name}-${index}`}
                onClick={() => handleResultClick(result)}
                className="p-2 hover:bg-gray-50 cursor-pointer"
              >
                <p className="font-medium text-sm">{result.display_name}</p>
                <p className="text-xs text-gray-500">
                  Koordinat: {result.lat.toFixed(5)}, {result.lon.toFixed(5)}
                </p>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* No Results */}
      {showSearchResults && searchResults.length === 0 && !isLoading && !statusMessage && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg p-2">
          <p className="text-sm text-gray-500">Tidak ada hasil yang ditemukan</p>
        </div>
      )}
    </div>
  );
}

export default MapSearch;
