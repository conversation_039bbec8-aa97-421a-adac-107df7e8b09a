# Google OAuth Setup untuk Better Auth

## 🎯 Overview

Panduan ini menjelaskan cara mengkonfigurasi Google OAuth untuk aplikasi Rental Genset menggunakan Better Auth.

## ✅ Status Konfigurasi

- ✅ Better Auth dikonfigurasi dengan Google provider
- ✅ Environment variables sudah diset
- ✅ Database schema mendukung OAuth (model `account`)
- ✅ Google button sudah ada di halaman login
- ✅ API endpoint `/api/auth/sign-in/social` berfungsi (status 200)

## 🔧 Konfigurasi Google Cloud Console

### 1. Buat OAuth 2.0 Client ID

1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Pilih project atau buat project baru
3. Navigasi ke **APIs & Services** > **Credentials**
4. Klik **Create Credentials** > **OAuth 2.0 Client ID**
5. Pilih **Web application**

### 2. Konfigurasi Authorized Redirect URIs

Tambahkan URI berikut ke **Authorized redirect URIs**:

```
http://localhost:3000/api/auth/callback/google
https://yourdomain.com/api/auth/callback/google
```

### 3. Dapatkan Credentials

Setelah dibuat, copy:
- **Client ID**
- **Client Secret**

## 🔐 Environment Variables

File `.env` sudah dikonfigurasi dengan:

```env
# OAuth
GOOGLE_CLIENT_ID="************-gjdnuv46n0fffhiig96nqqq2rvvfgvl3.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
```

## 📝 Konfigurasi Better Auth

File `lib/auth/config.ts`:

```typescript
export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      redirectURI: `${process.env.BETTER_AUTH_URL || "http://localhost:3000"}/api/auth/callback/google`,
    },
  },
  // ... rest of config
});
```

## 🎨 UI Components

### Google Button Component

File `app/components/auth/social-button.tsx` sudah dikonfigurasi dengan:

```typescript
const handleSignIn = async () => {
  const result = await signIn.social({
    provider: "google",
    callbackURL: "/user/dashboard",
  });
};
```

## 🧪 Testing

### 1. Test Login Flow

1. Buka http://localhost:3000/login
2. Klik tombol "Lanjutkan dengan Google"
3. Akan redirect ke Google OAuth
4. Setelah authorize, redirect kembali ke aplikasi
5. User akan login dan redirect ke dashboard

### 2. Verifikasi Database

Setelah login dengan Google, cek tabel:
- `User` - data user baru
- `account` - data OAuth account dengan `providerId: "google"`
- `session` - session aktif

## 🔍 Troubleshooting

### Error: "Provider not found"

**Solusi**: Pastikan konfigurasi `socialProviders.google` sudah benar di `lib/auth/config.ts`

### Error: "Redirect URI mismatch"

**Solusi**: 
1. Cek Google Cloud Console
2. Pastikan redirect URI sesuai: `http://localhost:3000/api/auth/callback/google`
3. Untuk production, gunakan domain yang benar

### Error: "Invalid client"

**Solusi**: 
1. Cek `GOOGLE_CLIENT_ID` dan `GOOGLE_CLIENT_SECRET` di `.env`
2. Pastikan credentials dari Google Cloud Console benar

## 🚀 Production Setup

Untuk production:

1. Update environment variables:
```env
BETTER_AUTH_URL="https://yourdomain.com"
GOOGLE_CLIENT_ID="your-production-client-id"
GOOGLE_CLIENT_SECRET="your-production-client-secret"
```

2. Tambahkan production redirect URI di Google Cloud Console:
```
https://yourdomain.com/api/auth/callback/google
```

## 📊 Status Saat Ini

- ✅ Konfigurasi Better Auth: **SELESAI**
- ✅ Environment variables: **SELESAI**
- ✅ Database schema: **SELESAI**
- ✅ UI components: **SELESAI**
- ✅ API endpoints: **BERFUNGSI** (status 200)
- ⚠️ Google Cloud Console: **PERLU VERIFIKASI**

## 🎯 Next Steps

1. **Verifikasi Google Cloud Console**: Pastikan redirect URI sudah dikonfigurasi
2. **Test complete flow**: Test login dari awal sampai dashboard
3. **Handle edge cases**: Error handling untuk OAuth failures
4. **Production setup**: Konfigurasi untuk production environment
