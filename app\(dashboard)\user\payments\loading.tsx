import { Skeleton } from "@/app/components/ui/skeleton";

export default function PaymentsLoading() {
  return (
    <div>
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat"></div>
        <div className="relative">
          <Skeleton className="h-9 w-56 mb-3 animate-pulse" />
          <Skeleton className="h-4 w-96 mb-4 animate-pulse" />
          <div className="flex flex-wrap items-center gap-4 mt-4">
            <Skeleton className="h-10 w-40 animate-pulse" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-2 w-2 rounded-full animate-pulse" />
              <Skeleton className="h-4 w-12 animate-pulse" />
              <Skeleton className="h-2 w-2 rounded-full ml-3 animate-pulse" />
              <Skeleton className="h-4 w-32 animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Info Cards Section Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Card 1 - Process */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-12 w-12 rounded-lg animate-pulse" />
            <div>
              <Skeleton className="h-5 w-32 mb-2 animate-pulse" />
              <Skeleton className="h-4 w-24 animate-pulse" />
            </div>
          </div>
          <Skeleton className="h-4 w-full animate-pulse" />
        </div>

        {/* Card 2 - Settlement */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-12 w-12 rounded-lg animate-pulse" />
            <div>
              <Skeleton className="h-5 w-20 mb-2 animate-pulse" />
              <Skeleton className="h-4 w-32 animate-pulse" />
            </div>
          </div>
          <Skeleton className="h-4 w-full animate-pulse" />
        </div>

        {/* Card 3 - Overtime */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-12 w-12 rounded-lg animate-pulse" />
            <div>
              <Skeleton className="h-5 w-28 mb-2 animate-pulse" />
              <Skeleton className="h-4 w-36 animate-pulse" />
            </div>
          </div>
          <Skeleton className="h-4 w-full animate-pulse" />
        </div>
      </div>

      {/* Section Header Skeleton */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-6 w-40 animate-pulse" />
          <Skeleton className="h-6 w-32 animate-pulse" />
        </div>
      </div>

      {/* Payment Cards Skeleton */}
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
            {/* Header */}
            <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-700">
              <div className="flex flex-wrap justify-between items-center gap-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-lg animate-pulse" />
                  <div>
                    <Skeleton className="h-4 w-24 mb-2 animate-pulse" />
                    <Skeleton className="h-5 w-32 animate-pulse" />
                  </div>
                </div>
                <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Product Info */}
              <div className="flex flex-wrap items-center gap-3 mb-6">
                <Skeleton className="h-5 w-32 animate-pulse" />
                <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
                <Skeleton className="h-6 w-28 rounded-full ml-auto animate-pulse" />
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex justify-between text-sm mb-2">
                  <Skeleton className="h-4 w-32 animate-pulse" />
                  <Skeleton className="h-4 w-8 animate-pulse" />
                </div>
                <Skeleton className="h-2 w-full rounded-full animate-pulse" />
              </div>

              {/* Payment Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                {/* Deposit */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                  <Skeleton className="h-4 w-20 mb-2 animate-pulse" />
                  <Skeleton className="h-6 w-24 mb-2 animate-pulse" />
                  <Skeleton className="h-3 w-16 animate-pulse" />
                </div>

                {/* Remaining */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                  <Skeleton className="h-4 w-24 mb-2 animate-pulse" />
                  <Skeleton className="h-6 w-24 mb-2 animate-pulse" />
                  <Skeleton className="h-3 w-20 animate-pulse" />
                </div>

                {/* Overtime */}
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-800 border border-gray-100 dark:border-gray-700">
                  <Skeleton className="h-4 w-32 mb-2 animate-pulse" />
                  <Skeleton className="h-6 w-16 mb-2 animate-pulse" />
                  <Skeleton className="h-3 w-24 animate-pulse" />
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-6 border-t border-gray-100 dark:border-gray-700 bg-gradient-to-r from-gray-50/50 to-blue-50/30 dark:from-gray-800/50 dark:to-blue-900/20">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex flex-col gap-1">
                  <Skeleton className="h-4 w-20 animate-pulse" />
                  <Skeleton className="h-4 w-32 animate-pulse" />
                </div>
                <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                  <Skeleton className="h-11 w-40 animate-pulse" />
                  <Skeleton className="h-11 w-32 animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination Skeleton */}
      <div className="flex justify-center items-center gap-2 mt-8">
        <Skeleton className="h-10 w-10 animate-pulse" />
        <div className="flex items-center gap-1">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="h-10 w-10 animate-pulse" />
          ))}
        </div>
        <Skeleton className="h-10 w-10 animate-pulse" />
      </div>
    </div>
  );
}
