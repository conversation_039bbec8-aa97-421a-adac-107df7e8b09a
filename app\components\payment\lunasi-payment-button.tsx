"use client";

import { useState, useEffect } from "react";
import { SoundButton } from "@/app/components/ui/sound-button";
import { LuDollarSign } from "react-icons/lu";
import { usePaymentFlow } from "@/lib/hooks/use-payment-flow";
import { useToast } from "@/lib/hooks/use-toast";

interface LunasiPaymentButtonProps {
  rentalId: string;
  amount: number;
  className?: string;
}

export function LunasiPaymentButton({ rentalId, amount, className = "" }: LunasiPaymentButtonProps) {
  const [loading, setLoading] = useState(false);
  const [snapLoaded, setSnapLoaded] = useState(false);
  const { showError } = useToast();
  const { processPayment } = usePaymentFlow();

  // Load Midtrans Snap script
  useEffect(() => {
    const snapScript = 'https://app.sandbox.midtrans.com/snap/snap.js';
    const clientKey = process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY;

    if (!clientKey) {
      console.error('Midtrans client key not found');
      return;
    }

    const script = document.createElement('script');
    script.src = snapScript;
    script.setAttribute('data-client-key', clientKey);
    script.onload = () => {
      setSnapLoaded(true);
    };
    script.onerror = () => {
      console.error('Failed to load Midtrans Snap script');
      showError('Gagal memuat sistem pembayaran');
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, [showError]);

  const handleClick = async () => {
    if (!snapLoaded) {
      showError('Sistem pembayaran belum siap. Silakan coba lagi.');
      return;
    }

    try {
      setLoading(true);
      await processPayment({
        rentalId,
        type: "remaining",
        amount,
      });
    } catch (error) {
      console.error("Payment failed:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SoundButton
      variant="gradient"
      size="mobile"
      className={`w-full sm:w-auto ${className}`}
      soundType="click"
      onClick={handleClick}
      disabled={loading || !snapLoaded}
      loading={loading}
    >
      {!loading && <LuDollarSign className="mr-2 h-4 w-4" />}
      {loading ? "Memproses..." : !snapLoaded ? "Memuat..." : "Lunasi Pembayaran"}
    </SoundButton>
  );
}
