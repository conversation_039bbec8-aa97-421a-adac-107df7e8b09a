'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Moon, Sun } from "lucide-react";
import { useTheme } from 'next-themes';
import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { gsap } from 'gsap';

interface MenuItem {
  href: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface BottomNavigationProps {
  menuItems: MenuItem[];
  variant?: 'user' | 'marketing';
}

export function BottomNavigation({ menuItems, variant = 'user' }: BottomNavigationProps) {
  const pathname = usePathname();
  const { theme, setTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<HTMLAnchorElement[]>([]);
  const themeButtonRef = useRef<HTMLButtonElement>(null);

  // <PERSON>ya render tema setelah mounted untuk menghindari hydration mismatch
  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Animate container on mount
  useEffect(() => {
    if (containerRef.current) {
      gsap.fromTo(containerRef.current,
        {
          y: 100,
          opacity: 0
        },
        {
          y: 0,
          opacity: 1,
          duration: 0.5,
          ease: "back.out(1.7)"
        }
      );
    }
  }, []);

  // Animate items on mount
  useEffect(() => {
    if (itemRefs.current.length > 0) {
      gsap.fromTo(itemRefs.current,
        {
          y: 20,
          opacity: 0,
          scale: 0.8
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.4,
          stagger: 0.1,
          delay: 0.2,
          ease: "back.out(1.7)"
        }
      );
    }
  }, [mounted]);

  // Add hover animations
  useEffect(() => {
    itemRefs.current.forEach((item, index) => {
      if (!item) return;

      const handleMouseEnter = () => {
        gsap.to(item, {
          scale: 1.1,
          duration: 0.2,
          ease: "power2.out"
        });
      };

      const handleMouseLeave = () => {
        gsap.to(item, {
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        });
      };

      const handleTouchStart = () => {
        gsap.to(item, {
          scale: 0.95,
          duration: 0.1,
          ease: "power2.out"
        });
      };

      const handleTouchEnd = () => {
        gsap.to(item, {
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        });
      };

      item.addEventListener('mouseenter', handleMouseEnter);
      item.addEventListener('mouseleave', handleMouseLeave);
      item.addEventListener('touchstart', handleTouchStart);
      item.addEventListener('touchend', handleTouchEnd);

      return () => {
        item.removeEventListener('mouseenter', handleMouseEnter);
        item.removeEventListener('mouseleave', handleMouseLeave);
        item.removeEventListener('touchstart', handleTouchStart);
        item.removeEventListener('touchend', handleTouchEnd);
      };
    });
  }, [mounted]);

  const toggleTheme = () => {
    const newTheme = (theme === "light" || resolvedTheme === "light") ? "dark" : "light";
    console.log(`${variant}Nav: Mengganti tema dari`, theme, "ke", newTheme);

    // Animate theme button
    if (themeButtonRef.current) {
      gsap.to(themeButtonRef.current, {
        scale: 0.8,
        duration: 0.1,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      });
    }

    // Setel tema
    setTheme(newTheme);

    // Update manual untuk memastikan
    if (typeof window !== 'undefined') {
      const htmlElement = document.documentElement;
      htmlElement.setAttribute('data-theme', newTheme);

      if (newTheme === 'dark') {
        htmlElement.classList.add('dark');
        htmlElement.classList.remove('light');
      } else {
        htmlElement.classList.add('light');
        htmlElement.classList.remove('dark');
      }

      // Kirim event
      const event = new CustomEvent('themeChange', {
        detail: { prevTheme: theme, theme: newTheme }
      });
      document.dispatchEvent(event);
    }
  };

  // Gunakan resolvedTheme untuk mendapatkan tema aktual yang diterapkan
  const currentTheme = resolvedTheme || theme;

  // Styling berdasarkan variant
  const containerClass = variant === 'marketing'
    ? "md:hidden fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-50 dark:bg-gray-800 dark:border-gray-700"
    : "md:hidden fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-50 dark:bg-gray-900 dark:border-gray-800";

  const heightClass = variant === 'marketing' ? "h-16" : "h-18";

  const activeColorClass = variant === 'marketing'
    ? 'text-blue-600 dark:text-blue-400'
    : 'text-violet-600 dark:text-violet-400';

  const inactiveColorClass = 'text-gray-500 dark:text-gray-400';

  const textSizeClass = variant === 'marketing' ? 'text-xs' : 'text-[10px]';

  return (
    <div ref={containerRef} className={containerClass}>
      <div className={`grid grid-cols-4 ${heightClass}`}>
        {menuItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = variant === 'marketing'
            ? pathname === item.href
            : pathname === item.href || pathname.startsWith(`${item.href}/`);

          return (
            <Link
              key={item.href}
              href={item.href}
              ref={(el) => {
                if (el) itemRefs.current[index] = el;
              }}
              className="flex flex-col items-center justify-center px-1 py-2 transition-all duration-200"
            >
              <Icon
                className={`w-5 h-5 mb-1 ${isActive ? activeColorClass : inactiveColorClass
                  }`}
              />
              <span
                className={`${textSizeClass} truncate w-full text-center ${isActive
                  ? `${activeColorClass} font-medium`
                  : inactiveColorClass
                  }`}
              >
                {item.title}
              </span>
            </Link>
          );
        })}

        {/* Tombol Theme Toggle */}
        <button
          ref={themeButtonRef}
          className="flex flex-col items-center justify-center px-1 py-2 transition-all duration-200"
          onClick={toggleTheme}
          aria-label={mounted ? (currentTheme === "light" ? "Aktifkan mode gelap" : "Aktifkan mode terang") : "Toggle theme"}
        >
          {mounted ? (
            <>
              <div className="relative w-5 h-5 mb-1">
                {/* Ikon matahari */}
                <Sun
                  className={cn(
                    "w-full h-full transition-all",
                    currentTheme === "dark" ? "opacity-0 scale-0" : "opacity-100 scale-100 text-amber-500"
                  )}
                />

                {/* Ikon bulan */}
                <Moon
                  className={cn(
                    "absolute inset-0 w-full h-full transition-all",
                    currentTheme === "dark" ? "opacity-100 scale-100 text-blue-400" : "opacity-0 scale-0"
                  )}
                />
              </div>

              <span className={`${textSizeClass} truncate w-full text-center ${inactiveColorClass}`}>
                {currentTheme === 'light' ? 'Gelap' : 'Terang'}
              </span>
            </>
          ) : (
            <>
              <div className="w-5 h-5 mb-1 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="w-8 h-2 bg-gray-200 rounded animate-pulse mx-auto"></div>
            </>
          )}
        </button>
      </div>
    </div>
  );
}