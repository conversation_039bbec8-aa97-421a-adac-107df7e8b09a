import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy API untuk Nominatim Geocoding
 * Mengatasi masalah CORS saat mengakses API Nominatim dari browser
 */
export async function GET(request: NextRequest) {
  try {
    // Ambil parameter dari query string
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('q');
    const lat = searchParams.get('lat');
    const lon = searchParams.get('lon');
    
    // Parameter tambahan
    const format = searchParams.get('format') || 'json';
    const limit = searchParams.get('limit') || '5';
    const addressdetails = searchParams.get('addressdetails') || '1';
    const countrycodes = searchParams.get('countrycodes') || 'id';
    const lang = searchParams.get('accept-language') || 'id';
    
    console.log(`[Nominatim API] Parameter permintaan:`, { query, lat, lon, format, limit, addressdetails, countrycodes, lang });
    
    // Tentukan apakah ini permintaan reverse geocoding atau pencarian normal
    let url = '';
    if (lat && lon) {
      // Ini adalah permintaan reverse geocoding
      url = `https://nominatim.openstreetmap.org/reverse?lat=${lat}&lon=${lon}&format=${format}&addressdetails=${addressdetails}&accept-language=${lang}`;
    } else if (query) {
      // Ini adalah permintaan pencarian biasa
      url = `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(query)}+NTB&format=${format}&addressdetails=${addressdetails}&countrycodes=${countrycodes}&limit=${limit}&accept-language=${lang}`;
    } else {
      // Tidak ada parameter yang diperlukan
      console.error('[Nominatim API] Parameter yang diperlukan tidak ada');
      return NextResponse.json(
        { error: 'Parameter query (q) atau koordinat (lat & lon) diperlukan' },
        { status: 400 }
      );
    }
    
    console.log(`[Nominatim API] Mengirim permintaan ke: ${url}`);
    
    // Kirim permintaan ke API Nominatim dengan timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 detik timeout
    
    try {
      // Kirim permintaan ke API Nominatim dengan kepala permintaan yang diperlukan
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'RentalGansetApp/1.0', // Penting: Nominatim memerlukan header User-Agent
          'Referer': 'https://rentalganset.id'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Jika API merespons dengan error
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[Nominatim API] Error dari API Nominatim: ${response.status} ${response.statusText}`, errorText);
        return NextResponse.json(
          { error: `Error dari API Nominatim: ${response.status} ${response.statusText}` },
          { status: response.status }
        );
      }
      
      // Ambil data dari respons API
      const data = await response.json();
      console.log(`[Nominatim API] Berhasil mendapatkan respon: ${Array.isArray(data) ? data.length : 'objek'}`);
      
      // Kirim respons
      return NextResponse.json(data);
      
    } catch (fetchError: unknown) {
      clearTimeout(timeoutId);
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('[Nominatim API] Permintaan ke API Nominatim timeout');
        return NextResponse.json(
          { error: 'API Nominatim timeout' },
          { status: 504 }
        );
      }
      
      console.error('[Nominatim API] Gagal fetch API Nominatim:', fetchError);
      return NextResponse.json(
        { error: `Gagal mengakses API Nominatim: ${fetchError instanceof Error ? fetchError.message : 'Kesalahan tidak diketahui'}` },
        { status: 502 }
      );
    }
    
  } catch (error) {
    console.error('[Nominatim API] Error saat mengakses API Nominatim:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
} 
