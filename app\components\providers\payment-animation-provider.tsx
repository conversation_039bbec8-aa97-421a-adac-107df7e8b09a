"use client";

import { createContext, useContext, useState, useCallback, useEffect } from "react";
import { PaymentAnimation } from "@/app/components/ui/payment-animation";
import { useAppSounds } from "@/lib/hooks/use-app-sounds";

interface PaymentAnimationState {
  isVisible: boolean;
  type: "success" | "error" | "processing";
  title: string;
  message: string;
  amount?: string;
}

interface PaymentAnimationContextType {
  animationState: PaymentAnimationState;
  showSuccess: (title: string, message: string, amount?: string) => void;
  showError: (title: string, message: string, amount?: string) => void;
  showProcessing: (title: string, message: string, amount?: string) => void;
  hideAnimation: () => void;
}

const PaymentAnimationContext = createContext<PaymentAnimationContextType | undefined>(undefined);

export function PaymentAnimationProvider({ children }: { children: React.ReactNode }) {
  const [animationState, setAnimationState] = useState<PaymentAnimationState>({
    isVisible: false,
    type: "processing",
    title: "",
    message: "",
    amount: undefined
  });

  const { sounds } = useAppSounds();

  const showSuccess = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "success",
      title,
      message,
      amount
    });
  }, []);

  const showError = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "error",
      title,
      message,
      amount
    });
  }, []);

  const showProcessing = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "processing",
      title,
      message,
      amount
    });
  }, []);

  const hideAnimation = useCallback(() => {
    setAnimationState(prev => ({
      ...prev,
      isVisible: false
    }));
  }, []);

  // Play sound when animation becomes visible
  useEffect(() => {
    if (animationState.isVisible) {
      switch (animationState.type) {
        case "success":
          sounds.onPaymentSuccess();
          break;
        case "error":
          sounds.onPaymentError();
          break;
        case "processing":
          sounds.onNotification();
          break;
      }
    }
  }, [animationState.isVisible, animationState.type, sounds]);

  const handleAnimationComplete = () => {
    // All animations will be hidden manually by user clicking the button
    // No auto-hide for any animation type
  };

  const contextValue: PaymentAnimationContextType = {
    animationState,
    showSuccess,
    showError,
    showProcessing,
    hideAnimation
  };

  return (
    <PaymentAnimationContext.Provider value={contextValue}>
      {children}
      {/* Global Payment Animation */}
      {animationState.isVisible && (
        <PaymentAnimation
          type={animationState.type}
          title={animationState.title}
          message={animationState.message}
          amount={animationState.amount}
          onAnimationComplete={handleAnimationComplete}
          onClose={hideAnimation}
        />
      )}
    </PaymentAnimationContext.Provider>
  );
}

export function usePaymentAnimationContext() {
  const context = useContext(PaymentAnimationContext);
  if (context === undefined) {
    throw new Error('usePaymentAnimationContext must be used within a PaymentAnimationProvider');
  }
  return context;
}
