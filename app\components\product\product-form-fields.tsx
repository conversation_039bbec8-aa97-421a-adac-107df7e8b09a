"use client";

import { Product } from "@/lib/types/product";
import { useState, useEffect } from "react";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";

import { Alert } from "@/app/components/ui/alert";
import {
  LuInfo,
  LuPackage,
  LuDollarSign,
  LuZap,
  LuWarehouse,
  LuClock,
  LuFileText,
  LuX,
  LuCheck
} from "react-icons/lu";

interface ProductFormFieldsProps {
  product: Product | null;
  errors?: Record<string, string>;
  onValidationChange?: (isValid: boolean) => void;
}

interface FormErrors {
  name?: string;
  capacity?: string;
  price?: string;
  stock?: string;
  overtimeRate?: string;
  description?: string;
}

export function ProductFormFields({ product, errors: externalErrors, onValidationChange }: ProductFormFieldsProps) {
  const [ovrRate, setOvrRate] = useState<string>(product?.overtimeRate?.toString() || "50000");
  const [status, setStatus] = useState(product?.status || "AVAILABLE");
  const [formData, setFormData] = useState({
    name: product?.name || "",
    capacity: product?.capacity?.toString() || "",
    price: product?.price?.toString() || "",
    stock: product?.stock?.toString() || "",
    description: product?.description || "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validation functions
  const validateField = (name: string, value: string): string | undefined => {
    switch (name) {
      case 'name':
        if (!value.trim()) return 'Nama produk harus diisi';
        if (value.length < 3) return 'Nama produk minimal 3 karakter';
        if (value.length > 100) return 'Nama produk maksimal 100 karakter';
        break;
      case 'capacity':
        const capacityNum = parseFloat(value);
        if (!value) return 'Kapasitas harus diisi';
        if (isNaN(capacityNum)) return 'Kapasitas harus berupa angka';
        if (capacityNum <= 0) return 'Kapasitas harus lebih dari 0';
        if (capacityNum > 10000) return 'Kapasitas maksimal 10,000 KVA';
        break;
      case 'price':
        const priceNum = parseFloat(value);
        if (!value) return 'Harga harus diisi';
        if (isNaN(priceNum)) return 'Harga harus berupa angka';
        if (priceNum <= 0) return 'Harga harus lebih dari 0';
        if (priceNum > 1000000000) return 'Harga maksimal 1 miliar rupiah';
        break;
      case 'stock':
        const stockNum = parseInt(value);
        if (!value) return 'Stok harus diisi';
        if (isNaN(stockNum)) return 'Stok harus berupa angka';
        if (stockNum < 0) return 'Stok tidak boleh negatif';
        if (stockNum > 1000) return 'Stok maksimal 1000 unit';
        break;
      case 'description':
        if (value && value.length > 1000) return 'Deskripsi maksimal 1000 karakter';
        break;
      case 'overtimeRate':
        if (!value) return 'Tarif overtime harus diisi';
        const rateNum = parseFloat(value);
        if (isNaN(rateNum)) return 'Tarif overtime harus berupa angka';
        if (rateNum < 0) return 'Tarif overtime tidak boleh negatif';
        if (rateNum > 10000000) return 'Tarif overtime maksimal 10 juta rupiah';
        break;
    }
    return undefined;
  };

  // Handler yang menyesuaikan tipe untuk Select
  const handleStatusChange = (value: string) => {
    setStatus(value as "AVAILABLE" | "NOT_AVAILABLE" | "MAINTENANCE");
  };

  // Handle input changes with validation
  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validate field
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));

    // Check overall form validity - need to get current values for validation
    const newErrors = { ...errors, [name]: error };
    const hasNoErrors = Object.values(newErrors).every(err => !err);

    // Get current form values including the one being updated
    const currentFormData = name === 'overtimeRate' ? formData : { ...formData, [name]: value };
    const currentOvrRate = name === 'overtimeRate' ? value : ovrRate;

    const hasRequiredFields = Boolean(
      currentFormData.name &&
      currentFormData.capacity &&
      currentFormData.price &&
      currentFormData.stock &&
      currentOvrRate
    );
    const isValid = hasNoErrors && hasRequiredFields;

    if (onValidationChange) {
      onValidationChange(isValid);
    }
  };

  // Handle field blur (mark as touched)
  const handleBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  // Update form data when product changes
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || "",
        capacity: product.capacity?.toString() || "",
        price: product.price?.toString() || "",
        stock: product.stock?.toString() || "",
        description: product.description || "",
      });
      setOvrRate(product.overtimeRate?.toString() || "50000");
      setStatus(product.status || "AVAILABLE");
    }
  }, [product]);

  // Get error message (external errors take precedence)
  const getError = (field: string) => {
    return externalErrors?.[field] || (touched[field] ? errors[field as keyof FormErrors] : undefined);
  };

  // Check if field has error
  const hasError = (field: string) => {
    return !!getError(field);
  };

  return (
    <Card className="border bg-white dark:bg-gray-950 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
          <LuPackage className="h-5 w-5 text-blue-500" />
          Informasi Produk
        </CardTitle>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Masukkan detail produk genset yang akan disewakan dengan lengkap dan akurat
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Product Name Field */}
        <div className="space-y-3">
          <Label
            htmlFor="name"
            className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            <LuPackage className="h-4 w-4 text-gray-500" />
            Nama Produk
            <span className="text-red-500">*</span>
          </Label>
          <div className="relative">
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              onBlur={() => handleBlur('name')}
              placeholder="Contoh: Genset Honda 10 KVA"
              required
              aria-describedby={hasError('name') ? 'name-error' : 'name-help'}
              aria-invalid={hasError('name')}
              className={`h-12 text-base ${hasError('name')
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                } transition-colors duration-200`}
            />
            {hasError('name') && (
              <LuX className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-500" />
            )}
          </div>
          {hasError('name') ? (
            <p id="name-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
              <LuX className="h-4 w-4" />
              {getError('name')}
            </p>
          ) : (
            <p id="name-help" className="text-xs text-gray-500 dark:text-gray-400">
              Berikan nama yang jelas dan mudah dipahami pelanggan
            </p>
          )}
        </div>

        {/* Capacity and Price Fields - Vertical on mobile, horizontal on desktop */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Capacity Field */}
          <div className="space-y-3">
            <Label
              htmlFor="capacity"
              className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              <LuZap className="h-4 w-4 text-gray-500" />
              Kapasitas (KVA)
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="capacity"
                name="capacity"
                type="number"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', e.target.value)}
                onBlur={() => handleBlur('capacity')}
                placeholder="10"
                min={1}
                max={10000}
                step="0.1"
                required
                aria-describedby={hasError('capacity') ? 'capacity-error' : 'capacity-help'}
                aria-invalid={hasError('capacity')}
                className={`h-12 text-base ${hasError('capacity')
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  } transition-colors duration-200`}
              />
              {hasError('capacity') && (
                <LuX className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-500" />
              )}
            </div>
            {hasError('capacity') ? (
              <p id="capacity-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                <LuX className="h-4 w-4" />
                {getError('capacity')}
              </p>
            ) : (
              <p id="capacity-help" className="text-xs text-gray-500 dark:text-gray-400">
                Kapasitas daya genset dalam satuan KVA (1-10,000)
              </p>
            )}
          </div>

          {/* Price Field */}
          <div className="space-y-3">
            <Label
              htmlFor="price"
              className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              <LuDollarSign className="h-4 w-4 text-gray-500" />
              Harga Sewa (per 8 jam)
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="price"
                name="price"
                type="number"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                onBlur={() => handleBlur('price')}
                placeholder="1000000"
                min={0}
                max={1000000000}
                step="1000"
                required
                aria-describedby={hasError('price') ? 'price-error' : 'price-help'}
                aria-invalid={hasError('price')}
                className={`h-12 text-base ${hasError('price')
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  } transition-colors duration-200`}
              />
              {hasError('price') && (
                <LuX className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-500" />
              )}
            </div>
            {hasError('price') ? (
              <p id="price-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                <LuX className="h-4 w-4" />
                {getError('price')}
              </p>
            ) : (
              <p id="price-help" className="text-xs text-gray-500 dark:text-gray-400">
                Harga sewa per 8 jam dalam rupiah (tanpa titik atau koma)
              </p>
            )}
          </div>
        </div>

        {/* Stock and Overtime Rate Fields */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Stock Field */}
          <div className="space-y-3">
            <Label
              htmlFor="stock"
              className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              <LuWarehouse className="h-4 w-4 text-gray-500" />
              Stok Tersedia
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="stock"
                name="stock"
                type="number"
                value={formData.stock}
                onChange={(e) => handleInputChange('stock', e.target.value)}
                onBlur={() => handleBlur('stock')}
                placeholder="1"
                min={0}
                max={1000}
                step="1"
                required
                aria-describedby={hasError('stock') ? 'stock-error' : 'stock-help'}
                aria-invalid={hasError('stock')}
                className={`h-12 text-base ${hasError('stock')
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  } transition-colors duration-200`}
              />
              {hasError('stock') && (
                <LuX className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-500" />
              )}
            </div>
            {hasError('stock') ? (
              <p id="stock-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                <LuX className="h-4 w-4" />
                {getError('stock')}
              </p>
            ) : (
              <p id="stock-help" className="text-xs text-gray-500 dark:text-gray-400">
                Jumlah unit genset yang tersedia untuk disewa
              </p>
            )}
          </div>

          {/* Overtime Rate Field */}
          <div className="space-y-3">
            <Label
              htmlFor="overtimeRate"
              className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              <LuClock className="h-4 w-4 text-gray-500" />
              Tarif Overtime (Rp/jam)
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="overtimeRate"
                name="overtimeRate"
                type="number"
                value={ovrRate}
                onChange={(e) => {
                  setOvrRate(e.target.value);
                  handleInputChange('overtimeRate', e.target.value);
                }}
                onBlur={() => handleBlur('overtimeRate')}
                placeholder="50000"
                min={0}
                max={10000000}
                step="1000"
                required
                aria-describedby={hasError('overtimeRate') ? 'overtime-error' : 'overtime-help'}
                aria-invalid={hasError('overtimeRate')}
                className={`h-12 text-base ${hasError('overtimeRate')
                  ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  } transition-colors duration-200`}
              />
              {hasError('overtimeRate') && (
                <LuX className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-red-500" />
              )}
            </div>
            {hasError('overtimeRate') ? (
              <p id="overtime-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
                <LuX className="h-4 w-4" />
                {getError('overtimeRate')}
              </p>
            ) : (
              <div className="space-y-2">
                <p id="overtime-help" className="text-xs text-gray-500 dark:text-gray-400">
                  Tarif tambahan per jam jika penyewaan melebihi waktu normal
                </p>
                <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-950/50">
                  <LuInfo className="h-4 w-4 text-blue-600" />
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    Masukkan 0 jika tidak ada tarif overtime khusus
                  </p>
                </Alert>
              </div>
            )}
          </div>
        </div>

        {/* Description Field */}
        <div className="space-y-3">
          <Label
            htmlFor="description"
            className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            <LuFileText className="h-4 w-4 text-gray-500" />
            Deskripsi Produk
          </Label>
          <div className="relative">
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              onBlur={() => handleBlur('description')}
              placeholder="Contoh: Genset Honda berkualitas tinggi dengan konsumsi bahan bakar efisien. Cocok untuk acara outdoor, konstruksi, atau backup power rumah. Dilengkapi dengan sistem proteksi otomatis dan panel kontrol yang mudah digunakan."
              rows={4}
              maxLength={1000}
              aria-describedby={hasError('description') ? 'description-error' : 'description-help'}
              aria-invalid={hasError('description')}
              className={`resize-y min-h-[120px] text-base ${hasError('description')
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                } transition-colors duration-200`}
            />
            {hasError('description') && (
              <LuX className="absolute right-3 top-3 h-5 w-5 text-red-500" />
            )}
          </div>
          {hasError('description') ? (
            <p id="description-error" className="text-sm text-red-600 dark:text-red-400 flex items-center gap-1">
              <LuX className="h-4 w-4" />
              {getError('description')}
            </p>
          ) : (
            <div className="flex justify-between items-center">
              <p id="description-help" className="text-xs text-gray-500 dark:text-gray-400">
                Jelaskan fitur, keunggulan, dan spesifikasi genset secara detail
              </p>
              <span className="text-xs text-gray-400">
                {formData.description.length}/1000
              </span>
            </div>
          )}
        </div>

        {/* Status Field - Only show for existing products */}
        {product && (
          <div className="space-y-3">
            <Label
              htmlFor="status"
              className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              <LuCheck className="h-4 w-4 text-gray-500" />
              Status Produk
            </Label>
            <Select
              value={status}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status produk" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="AVAILABLE">✅ Tersedia</SelectItem>
                <SelectItem value="NOT_AVAILABLE">❌ Tidak Tersedia</SelectItem>
                <SelectItem value="MAINTENANCE">🔧 Maintenance</SelectItem>
              </SelectContent>
            </Select>
            <p id="status-help" className="text-xs text-gray-500 dark:text-gray-400">
              Status ketersediaan produk untuk penyewaan
            </p>
          </div>
        )}

        {/* Information Alert */}
        <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-950/50">
          <LuInfo className="h-4 w-4 text-blue-600" />
          <div className="space-y-1">
            <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Tips Pengisian Form
            </p>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1 ml-4 list-disc">
              <li>Pastikan semua informasi akurat dan lengkap</li>
              <li>Gunakan nama produk yang jelas dan mudah dipahami</li>
              <li>Harga yang kompetitif akan menarik lebih banyak pelanggan</li>
              <li>Deskripsi detail membantu pelanggan memahami produk</li>
            </ul>
          </div>
        </Alert>
      </CardContent>
    </Card>
  );
}
