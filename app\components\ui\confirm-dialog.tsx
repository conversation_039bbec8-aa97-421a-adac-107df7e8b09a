"use client";

import * as React from "react";
import { SoundButton } from "./sound-button";
import { LoadingSpinner } from "./loading-spinner";

interface ConfirmDialogProps {
  title: string;
  description: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void | Promise<void>;
  isLoading?: boolean;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "destructive";
  userInfo?: {
    name: string;
    email: string;
  };
}

export function ConfirmDialog({
  title,
  description,
  open,
  onOpenChange,
  onConfirm,
  isLoading,
  confirmText = "Konfirmasi",
  cancelText = "Batal",
  variant = "destructive",
  userInfo
}: ConfirmDialogProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black/80 animate-in fade-in-0"
        onClick={() => onOpen<PERSON>hange(false)}
      />

      {/* Dialog Content */}
      <div className="relative z-50 grid w-full max-w-lg gap-6 border bg-white dark:bg-gray-800 p-6 shadow-lg animate-in fade-in-0 zoom-in-95 slide-in-from-bottom-2 sm:rounded-lg md:w-[420px]">
        <div className="flex flex-col space-y-4 text-center sm:text-left">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {description}
          </p>

          {userInfo && (
            <div className="rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50 p-4">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">{userInfo.name}</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">{userInfo.email}</p>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col-reverse sm:flex-row sm:justify-end gap-3">
          <SoundButton
            variant="outline"
            disabled={isLoading}
            onClick={() => onOpenChange(false)}
            className="h-11"
            soundType="click"
          >
            {cancelText}
          </SoundButton>
          <SoundButton
            variant={variant}
            onClick={onConfirm}
            disabled={isLoading}
            className="h-11"
            soundType={variant === "destructive" ? "error" : "success"}
          >
            {isLoading ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                <span>Memproses...</span>
              </>
            ) : (
              confirmText
            )}
          </SoundButton>
        </div>
      </div>
    </div>
  );
}
