import { Skeleton } from "@/app/components/ui/skeleton";

export default function OperationsLoading() {
  return (
    <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8 max-w-6xl">
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat"></div>
        <div className="relative">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <Skeleton className="h-9 w-80 mb-3 animate-pulse" />
              <Skeleton className="h-4 w-96 animate-pulse" />
            </div>
            <div className="flex items-center gap-4 mt-4 md:mt-0">
              <Skeleton className="h-8 w-32 animate-pulse" />
              <Skeleton className="h-8 w-24 animate-pulse" />
            </div>
          </div>
          <Skeleton className="h-4 w-64 mt-4 animate-pulse" />
        </div>
      </div>

      {/* Info Card Skeleton */}
      <div className="mb-8 p-5 bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/30 dark:to-indigo-950/30 rounded-xl shadow-sm border border-violet-100 dark:border-violet-900/50">
        <div className="flex items-start gap-3">
          <Skeleton className="h-10 w-10 rounded-full animate-pulse" />
          <div className="flex-1">
            <Skeleton className="h-5 w-48 mb-2 animate-pulse" />
            <Skeleton className="h-4 w-full mb-1 animate-pulse" />
            <Skeleton className="h-4 w-3/4 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Status Summary Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        {/* Card 1 - Pending */}
        <div className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl">
          <div className="h-2 bg-gradient-to-r from-yellow-400 to-yellow-500 dark:from-yellow-500 dark:to-yellow-600"></div>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-4 w-32 mb-2 animate-pulse" />
                <Skeleton className="h-10 w-12 animate-pulse" />
              </div>
              <Skeleton className="h-14 w-14 rounded-full animate-pulse" />
            </div>
          </div>
        </div>

        {/* Card 2 - Running */}
        <div className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl">
          <div className="h-2 bg-gradient-to-r from-green-400 to-green-500 dark:from-green-500 dark:to-green-600"></div>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-4 w-36 mb-2 animate-pulse" />
                <Skeleton className="h-10 w-12 animate-pulse" />
              </div>
              <Skeleton className="h-14 w-14 rounded-full animate-pulse" />
            </div>
          </div>
        </div>

        {/* Card 3 - Completed */}
        <div className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-lg rounded-xl">
          <div className="h-2 bg-gradient-to-r from-indigo-400 to-indigo-500 dark:from-indigo-500 dark:to-indigo-600"></div>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-4 w-28 mb-2 animate-pulse" />
                <Skeleton className="h-10 w-12 animate-pulse" />
              </div>
              <Skeleton className="h-14 w-14 rounded-full animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Section Title Skeleton */}
      <div className="flex items-center gap-3 mb-6">
        <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
        <Skeleton className="h-6 w-32 animate-pulse" />
      </div>

      {/* Operations List Skeleton */}
      <div className="grid gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="overflow-hidden bg-white dark:bg-gray-900 border-0 shadow-md rounded-xl">
            <div className="p-0">
              <div className="flex flex-col md:flex-row">
                {/* Product Image Skeleton */}
                <div className="w-full md:w-72 h-60 md:h-auto flex-shrink-0 rounded-tl-xl rounded-tr-xl md:rounded-tr-none md:rounded-bl-xl overflow-hidden">
                  <Skeleton className="w-full h-full animate-pulse" />
                </div>

                {/* Content Section */}
                <div className="flex-1 p-6 space-y-4">
                  {/* Header with Status */}
                  <div className="flex justify-between items-start">
                    <div>
                      <Skeleton className="h-6 w-48 mb-2 animate-pulse" />
                      <div className="flex items-center gap-1">
                        <Skeleton className="h-2 w-2 rounded-full animate-pulse" />
                        <Skeleton className="h-4 w-16 animate-pulse" />
                      </div>
                    </div>
                    <Skeleton className="h-6 w-24 rounded-full animate-pulse" />
                  </div>

                  {/* Operational Info Grid */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                    {/* Period */}
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
                      <div>
                        <Skeleton className="h-3 w-20 mb-1 animate-pulse" />
                        <Skeleton className="h-4 w-40 animate-pulse" />
                      </div>
                    </div>

                    {/* Duration */}
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
                      <div>
                        <Skeleton className="h-3 w-12 mb-1 animate-pulse" />
                        <Skeleton className="h-4 w-16 animate-pulse" />
                      </div>
                    </div>

                    {/* Arrival Time */}
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
                      <div>
                        <Skeleton className="h-3 w-28 mb-1 animate-pulse" />
                        <Skeleton className="h-4 w-20 animate-pulse" />
                      </div>
                    </div>

                    {/* Cost */}
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
                      <div>
                        <Skeleton className="h-3 w-10 mb-1 animate-pulse" />
                        <Skeleton className="h-4 w-24 animate-pulse" />
                      </div>
                    </div>
                  </div>

                  {/* Operation Status */}
                  <div className="border-t border-gray-100 dark:border-gray-700 pt-4 mt-4">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full animate-pulse" />
                      <div className="flex-1">
                        <Skeleton className="h-5 w-64 mb-2 animate-pulse" />
                        <Skeleton className="h-4 w-48 animate-pulse" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
