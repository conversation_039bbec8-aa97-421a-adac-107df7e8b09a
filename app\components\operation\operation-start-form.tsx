"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/app/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { IoPlayCircle, IoCalendar } from "react-icons/io5";

interface OperationStartFormProps {
  operationId: string;
  disabled?: boolean;
}

export function OperationStartForm({ operationId, disabled = false }: OperationStartFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [startTime, setStartTime] = useState<string | null>(null);
  const router = useRouter();
  const { showSuccess, showError } = useToast();

  const handleStartOperation = async () => {
    try {
      setIsLoading(true);

      // Gunakan waktu saat ini sebagai waktu mulai
      const currentTime = new Date();
      const startTimeISO = currentTime.toISOString();

      const response = await fetch(`/api/operations/${operationId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "start",
          startTime: startTimeISO
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || error.error || "Gagal memulai operasi");
      }

      // Set waktu mulai untuk ditampilkan
      setStartTime(currentTime.toLocaleString('id-ID'));

      showSuccess("Operasi dimulai. Operasi genset berhasil dimulai");

      router.refresh();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal memulai operasi";
      showError(`Terjadi kesalahan. ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <Button
        onClick={handleStartOperation}
        disabled={isLoading || disabled || startTime !== null}
        loading={isLoading}
        variant="success"
        size="lg"
        className="w-full mb-3"
      >
        {!isLoading && <IoPlayCircle className="mr-2 h-6 w-6" />}
        {isLoading ? "Memulai Operasi..." : "Mulai Operasi Sekarang"}
      </Button>

      {startTime && (
        <div className="bg-green-50 p-3 rounded-md border border-green-100 flex items-center">
          <IoCalendar className="h-5 w-5 text-green-600 mr-2" />
          <div>
            <p className="text-sm font-medium text-green-700">Operasi dimulai pada:</p>
            <p className="text-sm text-green-800">{startTime}</p>
          </div>
        </div>
      )}
    </div>
  );
}
