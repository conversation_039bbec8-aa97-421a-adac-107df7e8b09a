"use client";

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from "react";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: string;
}

interface NotificationCounts {
  total: number;
  counts: {
    rental: number;
    payment: number;
    stock: number;
    other: number;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  counts: NotificationCounts;
  isLoading: boolean;
  fetchNotifications: () => Promise<void>;
  fetchCounts: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refreshAll: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [counts, setCounts] = useState<NotificationCounts>({
    total: 0,
    counts: { rental: 0, payment: 0, stock: 0, other: 0 }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<number>(0);

  // Fetch notification counts
  const fetchCounts = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/counts');
      if (response.ok) {
        const data = await response.json();
        setCounts(data);
      }
    } catch (error) {
      console.error('Error fetching notification counts:', error);
    }
  }, []);

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/notifications?limit=20');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications || []);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PATCH',
      });
      if (response.ok) {
        // Update local state immediately for better UX
        setNotifications(prev =>
          prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
        );

        // Update counts
        setCounts(prev => ({
          ...prev,
          total: Math.max(0, prev.total - 1)
        }));

        // Refresh counts from server to ensure accuracy
        await fetchCounts();
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      // Revert optimistic update on error
      await fetchNotifications();
      await fetchCounts();
    }
  }, [fetchCounts, fetchNotifications]);

  // Mark all as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'PATCH',
      });
      if (response.ok) {
        // Update local state immediately
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
        setCounts(prev => ({ ...prev, total: 0 }));

        // Refresh from server to ensure accuracy
        await fetchCounts();
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      // Revert optimistic update on error
      await fetchNotifications();
      await fetchCounts();
    }
  }, [fetchCounts, fetchNotifications]);

  // Refresh all data with debouncing
  const refreshAll = useCallback(async () => {
    const now = Date.now();
    // Debounce: only refresh if last refresh was more than 2 seconds ago
    if (now - lastRefresh < 2000) {
      return;
    }

    setLastRefresh(now);
    await Promise.all([fetchNotifications(), fetchCounts()]);
  }, [fetchNotifications, fetchCounts, lastRefresh]);

  // Initial load and periodic refresh
  useEffect(() => {
    refreshAll();

    // Refresh counts every 30 seconds
    const interval = setInterval(fetchCounts, 30000);

    // Refresh all data every 2 minutes
    const fullRefreshInterval = setInterval(refreshAll, 120000);

    return () => {
      clearInterval(interval);
      clearInterval(fullRefreshInterval);
    };
  }, [refreshAll, fetchCounts]);

  // Listen for visibility change to refresh when user comes back to tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        refreshAll();
      }
    };

    // Listen for focus events
    const handleFocus = () => {
      refreshAll();
    };

    // Listen for online/offline events
    const handleOnline = () => {
      refreshAll();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('online', handleOnline);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('online', handleOnline);
    };
  }, [refreshAll]);

  const contextValue: NotificationContextType = {
    notifications,
    counts,
    isLoading,
    fetchNotifications,
    fetchCounts,
    markAsRead,
    markAllAsRead,
    refreshAll,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}
