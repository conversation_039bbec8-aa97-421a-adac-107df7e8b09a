"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { IoRefresh } from "react-icons/io5";
import { useAppSounds } from "@/lib/hooks/use-app-sounds";


interface AutoRefreshProps {
  intervalMs?: number; // Refresh interval in milliseconds
  enabled?: boolean; // Whether auto-refresh is enabled
  showIndicator?: boolean; // Whether to show refresh indicator
}

export function AutoRefresh({
  intervalMs = 999999999, // EMERGENCY DISABLED, // Default 30 seconds
  enabled = false, // EMERGENCY DISABLED, // Disable auto-refresh by default to prevent infinite reloads
  showIndicator = true
}: AutoRefreshProps) {
  const router = useRouter();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      setIsRefreshing(true);

      // Refresh the current page
      router.refresh();

      // Update last refresh time
      setLastRefresh(new Date());

      // Reset refreshing state after a short delay
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }, intervalMs);

    return () => clearInterval(interval);
  }, [enabled, intervalMs, router]);

  if (!showIndicator) return null;

  return (
    <div className="flex items-center gap-2 text-xs text-white">
      <IoRefresh
        className={`h-3 w-3 ${isRefreshing ? 'animate-spin text-blue-500' : ''}`}
      />
      <span>
        {isRefreshing ? 'Memperbarui...' : `Terakhir diperbarui: ${lastRefresh.toLocaleTimeString('id-ID')}`}
      </span>
    </div>
  );
}

interface ManualRefreshButtonProps {
  onRefresh?: () => void;
  disabled?: boolean;
}

export function ManualRefreshButton({ onRefresh, disabled = false }: ManualRefreshButtonProps) {
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { sounds } = useAppSounds();

  const handleRefresh = async () => {
    if (disabled || isRefreshing) return;

    setIsRefreshing(true);
    sounds.onButtonClick(); // Play click sound

    try {
      if (onRefresh) {
        await onRefresh();
      } else {
        router.refresh();
      }
    } finally {
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={disabled || isRefreshing}
      className="inline-flex items-center gap-2 px-3 py-1.5 text-xs font-medium text-gray-800 bg-white dark:bg-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 shadow-sm border border-gray-300 dark:border-gray-700 rounded-md transition-all duration-200 transform hover:-translate-y-0.5"
    >
      <IoRefresh
        className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`}
      />
      {isRefreshing ? 'Memperbarui...' : 'Perbarui Status'}
    </button>
  );
}

interface StatusPollingProps {
  rentalId: string;
  onStatusChange?: (statusData: { status: string;[key: string]: unknown }) => void;
  pollingInterval?: number;
}

export function StatusPolling({
  rentalId,
  onStatusChange,
  pollingInterval = 15000 // 15 seconds
}: StatusPollingProps) {
  const [lastStatus, setLastStatus] = useState<string | null>(null);
  const { sounds } = useAppSounds();

  useEffect(() => {
    if (!rentalId) return;

    const pollStatus = async () => {
      try {
        const response = await fetch(`/api/operations/${rentalId}/status`, {
          cache: 'no-store'
        });

        if (response.ok) {
          const data = await response.json();
          const currentStatus = data.status;

          // If status changed, trigger callback and refresh
          if (lastStatus && lastStatus !== currentStatus) {
            // Play notification sound for status change
            sounds.onStatusUpdate();

            if (onStatusChange) {
              onStatusChange(data);
            }

            // Force page refresh on status change
            window.location.reload();
          }

          setLastStatus(currentStatus);
        }
      } catch (error) {
        console.error('Error polling status:', error);
      }
    };

    // Initial poll
    pollStatus();

    // Set up polling interval
    const interval = setInterval(pollStatus, pollingInterval);

    return () => clearInterval(interval);
  }, [rentalId, lastStatus, onStatusChange, pollingInterval, sounds]);

  return null; // This component doesn't render anything
}







