import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";

export async function PATCH(request: NextRequest) {
  try {
    // Get session using Better Auth
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;

    // Update semua notifikasi user menjadi dibaca
    const result = await prisma.notification.updateMany({
      where: {
        userId: userId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    return NextResponse.json({
      success: true,
      updatedCount: result.count
    });
  } catch (error) {
    console.error("[MARK_ALL_NOTIFICATIONS_READ_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal menandai semua notifikasi sebagai dibaca" },
      { status: 500 }
    );
  }
}
