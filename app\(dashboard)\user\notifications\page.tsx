import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import { NotificationsList } from "@/app/components/user/notifications-list";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';

export default async function UserNotificationsPage() {
  const session = await getSession();

  // Redirect ke login jika tidak ada session
  if (!session?.user) {
    redirect('/login');
  }

  // Ambil notifikasi user dari database
  const notifications = await prisma.notification.findMany({
    where: {
      userId: session.user.id
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 50 // Ambil 50 notifikasi terbaru
  });

  // Hitung jumlah notifikasi yang belum dibaca
  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">Notifikasi</h1>
          <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
            {unreadCount > 0 ? `${unreadCount} notifikasi belum dibaca` : 'Semua notifikasi sudah dibaca'}
          </p>
        </div>
      </div>

      <NotificationsList 
        notifications={notifications}
        unreadCount={unreadCount}
      />
    </div>
  );
}
