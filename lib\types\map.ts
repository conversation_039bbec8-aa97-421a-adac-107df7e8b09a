/**
 * Tipe-tipe yang digunakan dalam komponen peta
 */

// Interface untuk feature dari <PERSON>n API
export interface PhotonFeature {
  geometry: {
    coordinates: [number, number]; // [longitude, latitude]
  };
  properties: {
    name?: string;
    street?: string;
    housenumber?: string;
    city?: string;
    state?: string;
    country?: string;
    [key: string]: string | undefined;
  };
}

// Interface untuk Nominatim API
export interface NominatimResult {
  lat: string;
  lon: string;
  display_name: string;
  address?: {
    city?: string;
    town?: string;
    village?: string;
    state?: string;
    country?: string;
    [key: string]: string | undefined;
  };
}

// Interface untuk props komponen AddressPicker
export interface AddressPickerProps {
  onSelectLocation: (location: LocationData) => void;
  defaultAddress?: string;
}

// Interface untuk props komponen MapDisplay
export interface MapDisplayProps {
  latitude: number;
  longitude: number;
  zoom?: number;
  markerDraggable?: boolean;
  onMarkerDrag?: (location: LocationData) => void;
  markerFollowMouse?: boolean;
  className?: string;
}

// Interface untuk props komponen MapSearch
export interface MapSearchProps {
  onSearchResult: (result: SearchResult[]) => void;
  onSelectResult?: (result: SearchResult) => void;
  initialQuery?: string;
  placeholder?: string;
  className?: string;
}

// Interface untuk lokasi yang dipilih
export interface LocationData {
  lat: number;
  lng: number;
  address: string;
}

// Interface untuk hasil pencarian
export interface SearchResult {
  lat: number;
  lon: number;
  display_name: string;
  properties: {
    state?: string;
    county?: string;
    city?: string;
    name?: string;
  };
} 
