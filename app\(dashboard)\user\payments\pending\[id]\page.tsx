import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { notFound } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { SoundButton } from "@/app/components/ui/sound-button";
import { LuClock, LuRefreshCw, LuArrowLeft } from "react-icons/lu";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils/format";

interface PendingPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PendingPaymentPage({ params }: PendingPageProps) {
  const session = await getSession();
  if (!session?.user) {
    return notFound();
  }

  const { id } = await params;

  const rental = await prisma.rental.findUnique({
    where: {
      id,
      userId: session.user.id
    },
    include: {
      product: true,
      payment: true
    }
  });

  if (!rental || !rental.payment) {
    return notFound();
  }

  const isDepositPending = rental.payment.status === "DEPOSIT_PENDING";
  const paymentAmount = isDepositPending ? rental.payment.deposit : rental.payment.remaining;

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/user/payments">
            <Button variant="outline" size="sm">
              <LuArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Pembayaran Sedang Diproses</h1>
        </div>

        {/* Pending Status Card */}
        <Card className="mb-6">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4">
              <LuClock className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
            </div>
            <CardTitle className="text-xl text-yellow-600 dark:text-yellow-400">
              Pembayaran Sedang Diproses
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              Pembayaran Anda sedang diproses oleh sistem pembayaran.
              Mohon tunggu beberapa saat untuk konfirmasi.
            </p>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Produk:</p>
                  <p className="font-medium">{rental.product.name}</p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Jumlah Pembayaran:</p>
                  <p className="font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(paymentAmount)}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Jenis Pembayaran:</p>
                  <p className="font-medium">
                    {isDepositPending ? "Deposit" : "Sisa Pembayaran"}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Status:</p>
                  <p className="font-medium text-yellow-600 dark:text-yellow-400">
                    Menunggu Konfirmasi
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Pembayaran biasanya dikonfirmasi dalam 1-5 menit.
                Jika lebih dari 10 menit belum ada konfirmasi, silakan hubungi customer service.
              </p>

              <div className="flex gap-3 justify-center">
                <SoundButton
                  variant="outline"
                  onClick={() => window.location.reload()}
                  className="flex items-center gap-2"
                  soundType="click"
                >
                  <LuRefreshCw className="h-4 w-4" />
                  Refresh Status
                </SoundButton>

                <Link href={`/user/rentals/${rental.id}`}>
                  <SoundButton variant="default" soundType="click">
                    Lihat Detail Rental
                  </SoundButton>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Info Card */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Informasi Penting</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Jangan menutup halaman ini sampai pembayaran dikonfirmasi</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Anda akan menerima notifikasi email setelah pembayaran berhasil</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
              <p>Jika ada masalah, silakan hubungi customer service kami</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
