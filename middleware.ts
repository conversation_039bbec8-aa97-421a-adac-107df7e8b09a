import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Skip middleware for static files and API routes
  if (pathname.startsWith('/_next') ||
      pathname.startsWith('/api/') ||
      pathname.includes('.')) {
    return NextResponse.next();
  }

  // Protected routes
  const isUserRoute = pathname.startsWith('/user');
  const isAdminRoute = pathname.startsWith('/admin');
  const isProtectedRoute = isUserRoute || isAdminRoute;

  // For protected routes, validate session by calling our auth check API
  if (isProtectedRoute) {
    try {
      const authCheckUrl = new URL('/api/auth/check', request.url);
      const authResponse = await fetch(authCheckUrl.toString(), {
        headers: {
          'Cookie': request.headers.get('cookie') || '',
        },
      });

      if (!authResponse.ok || authResponse.status === 401) {
        // No valid session, redirect to login
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(loginUrl);
      }

      // Check if user has valid session data
      const authData = await authResponse.json();
      if (!authData.authenticated) {
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(loginUrl);
      }

      // Role-based redirection - Fixed to properly handle admin operations
      if (isAdminRoute && authData.role !== 'ADMIN') {
        // Non-admin user trying to access admin routes - redirect to user dashboard
        return NextResponse.redirect(new URL('/user/dashboard', request.url));
      }

      if (isUserRoute && authData.role === 'ADMIN') {
        // Admin user trying to access user routes - redirect to admin dashboard
        return NextResponse.redirect(new URL('/admin/dashboard', request.url));
      }

    } catch (error) {
      console.error('Middleware auth check error:', error);
      // If auth check fails, redirect to login
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  // For auth pages, check if user is already logged in
  if (pathname === '/login' || pathname === '/register') {
    try {
      const authCheckUrl = new URL('/api/auth/check', request.url);
      const authResponse = await fetch(authCheckUrl.toString(), {
        headers: {
          'Cookie': request.headers.get('cookie') || '',
        },
      });

      if (authResponse.ok) {
        const authData = await authResponse.json();
        if (authData.authenticated) {
          // User is already logged in, redirect to appropriate dashboard
          const dashboardUrl = authData.role === 'ADMIN' ? '/admin/dashboard' : '/user/dashboard';
          return NextResponse.redirect(new URL(dashboardUrl, request.url));
        }
      }
    } catch (error) {
      // If auth check fails, let them access login/register page
      console.error('Middleware auth check error for auth pages:', error);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/admin/:path*',
    '/user/:path*',
    '/login',
    '/register'
  ],
};