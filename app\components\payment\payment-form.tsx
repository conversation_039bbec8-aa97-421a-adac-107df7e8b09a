"use client";

import { PaymentButton } from "./payment-button.client";
import { formatCurrency } from "@/lib/utils/format";

interface PaymentFormProps {
  rentalId: string;
  amount: number;
  type: "deposit" | "remaining" | "full";
}

export function PaymentForm({ rentalId, amount, type }: PaymentFormProps) {
  const label = type === "deposit" 
    ? "Bayar Deposit Sekarang" 
    : type === "remaining" 
      ? "Lunasi Pembayaran" 
      : "Bayar Sekarang";
  
  const colorClass = type === "deposit" 
    ? "bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600" 
    : "bg-gradient-to-r from-violet-600 to-indigo-500 hover:from-violet-700 hover:to-indigo-600";

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
      <h2 className="text-xl font-semibold mb-4">
        {type === "deposit" 
          ? "Pembayaran Deposit" 
          : type === "remaining" 
            ? "Pelunasan P<PERSON>" 
            : "Pembayaran"}
      </h2>
      
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-600 dark:text-gray-300">Jumlah yang harus dibayar:</span>
          <span className="text-xl font-semibold text-gray-900 dark:text-white">{formatCurrency(amount)}</span>
        </div>
      </div>
      
      <div className="text-center">
        <PaymentButton
          rentalId={rentalId}
          amount={amount}
          type={type}
          label={label}
          colorClass={colorClass}
        />
      </div>
    </div>
  );
} 
