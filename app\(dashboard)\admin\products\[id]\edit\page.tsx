"use client";

import { notFound } from "next/navigation";
import { ProductEditForm } from "@/app/components/product/product-edit-form";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Product } from "@/lib/types/product";

interface EditProductPageProps {
    params: Promise<{ id: string }>;
}

export default function EditProductPage({
    params
}: EditProductPageProps) {
    const router = useRouter();
    const [product, setProduct] = useState<Product | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchProduct = async () => {
            try {
                const resolvedParams = await params;
                const id = resolvedParams?.id;

                if (!id) {
                    notFound();
                    return;
                }

                // Menggunakan API route alih-alih Prisma langsung
                const response = await fetch(`/api/products/${id}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                });

                if (!response.ok) {
                    if (response.status === 404) {
                        notFound();
                        return;
                    }
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const productData = await response.json();
                setProduct(productData);
            } catch (error) {
                console.error("Error fetching product:", error);
                setError(error instanceof Error ? error.message : 'Gagal memuat data produk');
            } finally {
                setLoading(false);
            }
        };

        fetchProduct();
    }, [params]);

    const handleCancel = () => {
        // Redirect ke halaman admin products jika dibatalkan
        router.push("/admin/products");
    };

    if (loading) {
        return (
            <div className="max-w-4xl mx-auto p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">Memuat data produk...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="max-w-4xl mx-auto p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-center">
                        <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Gagal Memuat Data</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
                        <button
                            onClick={() => router.push('/admin/products')}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                        >
                            Kembali ke Daftar Produk
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!product) {
        return null; // notFound() will handle this
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <ProductEditForm
                product={product}
                onCancel={handleCancel}
            />
        </div>
    );
}