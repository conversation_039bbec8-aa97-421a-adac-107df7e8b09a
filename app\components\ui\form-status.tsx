"use client";

import { useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { toast } from "sonner";

export function FormStatus() {
  const { pending } = useFormStatus();
  const [prevPending, setPrevPending] = useState(false);
  
  useEffect(() => {
    // Jika status berubah dari pending menjadi not pending, tampilkan toast
    if (prevPending && !pending) {
      toast.success("Perubahan berhasil disimpan");
    }
    
    setPrevPending(pending);
  }, [pending, prevPending]);
  
  return null; // Komponen ini tidak merender apa-apa
} 
