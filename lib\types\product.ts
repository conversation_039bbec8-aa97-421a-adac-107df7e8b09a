import { ProductStatus } from "@prisma/client";
import { User } from "./user";

export interface UpdateProductResult {
    success: boolean;
    message?: string;
    data?: Product;
    error?: string;
}

export interface Product {
    id: string;
    name: string;
    description: string | null;
    price: number;
    capacity: number;
    stock: number;
    status: ProductStatus;
    image: string | null;
    imageUrl: string | null;
    category: string | null;
    overtimeRate: number | null;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface ProductWithUser extends Product {
    user: User;
}

// Tambahkan tipe untuk form data
export interface ProductFormData {
    name: string;
    capacity: number;
    price: number;
    description: string | null;
    imageUrl: string | null;
    category: string | null;
    overtimeRate: number | null;
    status?: "Available" | "Rented" | "Maintenance";
}
