import { Metadata } from "next";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import { updateProfile, updatePassword } from "./actions";
import { ProfileClient } from "./profile-client";

export const metadata: Metadata = {
  title: "Profil Saya",
  description: "Kelola profil pengguna Anda",
};

export default async function ProfilePage() {
  const session = await getSession();
  if (!session?.user) {
    redirect('/login');
  }

  // Gunakan data yang tersedia dari session dan beri default value untuk yang tidak ada
  const user = {
    id: session.user.id || "",
    name: session.user.name || "Pengguna",
    email: session.user.email || "-",
    phone: session.user.phone || "-",
    image: session.user.image || "/images/avatar-placeholder.png",
    role: session.user.role || "USER",
    // Data tambahan yang tidak ada di session
    address: "",
    bio: ""
  };

  // Format tanggal bergabung dengan nilai default
  let joinDate = "Hari ini";

  // Gunakan try-catch untuk menangani potensi error
  try {
    if (session.user.createdAt) {
      const date = new Date(session.user.createdAt);
      joinDate = date.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }
  } catch (error) {
    console.error("Error formatting join date:", error);
    // Gunakan default jika gagal memformat
  }

  const handleProfileUpdate = async (formData: FormData) => {
    "use server";

    try {
      // Jalankan fungsi updateProfile untuk mengupdate profil
      const result = await updateProfile(formData);

      // Log hasil untuk debug
      console.log("Profile update result:", result);
    } catch (error) {
      console.error("Error updating profile:", error);
    }
  };



  return (
    <div className="container mx-auto py-6">
      {/* Gunakan komponen client baru untuk menampilkan dan mengupdate profil */}
      <ProfileClient
        user={{
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          image: user.image,
          role: user.role,
          address: user.address,
          bio: user.bio,
          joinDate: joinDate
        }}
        handleProfileUpdate={handleProfileUpdate}
      />
    </div>
  );
}
