"use client";

import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { <PERSON><PERSON><PERSON><PERSON>, LuX, LuCreditCard, LuDollarSign, LuHistory } from "react-icons/lu";
import { useRouter } from "next/navigation";

interface PaymentAnimationProps {
  type: "success" | "error" | "processing";
  title: string;
  message: string;
  amount?: string;
  onAnimationComplete?: () => void;
  onClose?: () => void;
}

export function PaymentAnimation({
  type,
  title,
  message,
  amount,
  onAnimationComplete,
  onClose
}: PaymentAnimationProps) {
  const router = useRouter();
  const [showButton, setShowButton] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const messageRef = useRef<HTMLParagraphElement>(null);
  const amountRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);
  const circleRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const tl = gsap.timeline({
      onComplete: onAnimationComplete
    });

    // Reset initial states
    gsap.set([iconRef.current, titleRef.current, messageRef.current, amountRef.current, buttonRef.current], {
      opacity: 0,
      y: 30,
      scale: 0.8
    });

    gsap.set(circleRef.current, {
      scale: 0,
      rotation: -180
    });

    gsap.set(containerRef.current, {
      opacity: 0,
      scale: 0.9
    });

    // Container entrance
    tl.to(containerRef.current, {
      opacity: 1,
      scale: 1,
      duration: 0.8,
      ease: "back.out(1.7)"
    });

    // Circle animation
    tl.to(circleRef.current, {
      scale: 1,
      rotation: 0,
      duration: 1.2,
      ease: "elastic.out(1, 0.5)"
    }, "-=0.4");

    // Icon animation with different effects based on type
    if (type === "success") {
      tl.to(iconRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 1,
        ease: "back.out(2)"
      })
        .to(iconRef.current, {
          scale: 1.2,
          duration: 0.4,
          yoyo: true,
          repeat: 2,
          ease: "power2.inOut"
        });
    } else if (type === "error") {
      tl.to(iconRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.5)"
      })
        .to(iconRef.current, {
          rotation: 10,
          duration: 0.2,
          yoyo: true,
          repeat: 8,
          ease: "power2.inOut"
        });
    } else {
      // Processing animation
      tl.to(iconRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "power2.out"
      });

      // Continuous rotation for processing
      gsap.to(iconRef.current, {
        rotation: 360,
        duration: 2,
        repeat: -1,
        ease: "none"
      });
    }

    // Title animation
    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.3");

    // Message animation
    tl.to(messageRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4");

    // Amount animation (if provided)
    if (amount && amountRef.current) {
      tl.to(amountRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.2");
    }

    // Success particles animation with responsive movement
    if (type === "success" && particlesRef.current) {
      const particles = particlesRef.current.children;
      const isMobile = window.innerWidth < 640;
      const particleRange = isMobile ? 60 : 100;
      const particleDistance = isMobile ? -30 : -50;

      tl.to(particles, {
        opacity: 1,
        scale: 1,
        y: particleDistance,
        x: (i) => (Math.random() - 0.5) * particleRange,
        duration: 2,
        stagger: 0.2,
        ease: "power2.out"
      }, "-=0.5")
        .to(particles, {
          opacity: 0,
          y: particleDistance * 2,
          duration: 1,
          ease: "power2.in"
        }, "-=0.5")
        // Show button after particles animation
        .call(() => setShowButton(true))
        .to(buttonRef.current, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "back.out(1.7)"
        }, "+=0.5");
    } else {
      // For non-success animations, show button after shorter delay
      tl.to({}, { duration: 1 })
        .call(() => setShowButton(true))
        .to(buttonRef.current, {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "back.out(1.7)"
        });
    }

    return () => {
      tl.kill();
    };
  }, [type, onAnimationComplete, amount]);

  const getIconColor = () => {
    switch (type) {
      case "success":
        return "text-green-600 dark:text-green-400";
      case "error":
        return "text-red-600 dark:text-red-400";
      case "processing":
        return "text-blue-600 dark:text-blue-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  const getCircleColor = () => {
    switch (type) {
      case "success":
        return "bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800";
      case "error":
        return "bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800";
      case "processing":
        return "bg-blue-100 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800";
      default:
        return "bg-gray-100 dark:bg-gray-900/30 border-gray-200 dark:border-gray-800";
    }
  };

  const renderIcon = () => {
    const iconClass = "h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12";
    switch (type) {
      case "success":
        return <LuCheck className={iconClass} />;
      case "error":
        return <LuX className={iconClass} />;
      case "processing":
        return <LuCreditCard className={iconClass} />;
      default:
        return <LuDollarSign className={iconClass} />;
    }
  };

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm"
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 md:p-8 shadow-2xl max-w-xs sm:max-w-sm md:max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700 transform translate-x-0 translate-y-0">
        {/* Success Particles */}
        {type === "success" && (
          <div ref={particlesRef} className="absolute inset-0 pointer-events-none">
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute top-1/2 left-1/2 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-400 rounded-full opacity-0"
                style={{
                  transform: `translate(-50%, -50%) rotate(${i * 45}deg) translateY(-15px) sm:translateY(-20px)`
                }}
              />
            ))}
          </div>
        )}

        {/* Main Content */}
        <div className="text-center space-y-4 sm:space-y-6">
          {/* Icon Circle */}
          <div className="flex justify-center">
            <div
              ref={circleRef}
              className={`w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 rounded-full border-2 flex items-center justify-center ${getCircleColor()}`}
            >
              <div ref={iconRef} className={getIconColor()}>
                {renderIcon()}
              </div>
            </div>
          </div>

          {/* Title */}
          <h2
            ref={titleRef}
            className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white"
          >
            {title}
          </h2>

          {/* Message */}
          <p
            ref={messageRef}
            className="text-sm sm:text-base text-gray-600 dark:text-gray-400 leading-relaxed px-2 sm:px-0"
          >
            {message}
          </p>

          {/* Amount (if provided) */}
          {amount && (
            <div
              ref={amountRef}
              className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-700 rounded-xl"
            >
              <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-1">
                Jumlah Pembayaran
              </p>
              <p className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white">
                {amount}
              </p>
            </div>
          )}

          {/* Action Button */}
          {showButton && (
            <div ref={buttonRef} className="mt-6">
              <button
                onClick={() => {
                  if (onClose) onClose();
                  router.push('/user/payments');
                }}
                className="w-full bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              >
                <LuHistory className="h-5 w-5" />
                Lihat Detail Pembayaran
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
