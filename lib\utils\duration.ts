import { RENTAL_DURATIONS, type RentalDuration } from './constants';

/**
 * Mapping dari format lama ke format baru untuk backward compatibility
 */
const OLD_TO_NEW_MAPPING: Record<string, string> = {
  '1x8_HOURS': '8_JAM',
  '2x8_HOURS': '16_JAM',
  '3x8_HOURS': '24_JAM',
  '4x8_HOURS': '32_JAM',
  '5x8_HOURS': '40_JAM',
  '6x8_HOURS': '48_JAM',
  '7x8_HOURS': '56_JAM',
  '8x8_HOURS': '64_JAM',
  '9x8_HOURS': '72_JAM',
  '10x8_HOURS': '80_JAM'
};

/**
 * Format durasi untuk ditampilkan di UI
 * Mendukung format baru (8_JAM) dan format lama (1x8_HOURS) untuk backward compatibility
 * @param duration Duration string
 * @returns Formatted duration string (e.g., "8 Jam", "24 Jam")
 */
export function formatDuration(duration: string | null): string {
  if (!duration) return '-';
  
  // Jika duration ada di konstanta RENTAL_DURATIONS, gunakan label dari sana
  if (duration in RENTAL_DURATIONS) {
    return RENTAL_DURATIONS[duration as keyof typeof RENTAL_DURATIONS].label;
  }
  
  // Cek apakah ini format lama dan konversi ke format baru
  if (duration in OLD_TO_NEW_MAPPING) {
    const newFormat = OLD_TO_NEW_MAPPING[duration];
    return RENTAL_DURATIONS[newFormat as keyof typeof RENTAL_DURATIONS].label;
  }
  
  // Fallback: coba parse dari format lama atau format lain
  if (duration.includes('x8')) {
    const multiplier = parseInt(duration.replace('x8_HOURS', '').replace('_HOURS', ''));
    if (!isNaN(multiplier)) {
      return `${multiplier * 8} Jam`;
    }
  }
  
  // Jika tidak bisa diparse, tampilkan apa adanya
  return duration;
}

/**
 * Konversi format lama ke format baru
 * @param oldDuration Duration dalam format lama (e.g., "1x8_HOURS")
 * @returns Duration dalam format baru (e.g., "8_JAM") atau null jika tidak valid
 */
export function convertOldToNewFormat(oldDuration: string): string | null {
  return OLD_TO_NEW_MAPPING[oldDuration] || null;
}

/**
 * Mendapatkan jumlah jam dari duration string
 * @param duration Duration string dalam format baru atau lama
 * @returns Jumlah jam
 */
export function getDurationHours(duration: string): number {
  // First, try to get hours from RENTAL_DURATIONS if it's a valid key (new format)
  const durationKey = duration as RentalDuration;
  if (durationKey in RENTAL_DURATIONS) {
    return RENTAL_DURATIONS[durationKey].hours;
  }
  
  // Check if it's old format and convert
  if (duration in OLD_TO_NEW_MAPPING) {
    const newFormat = OLD_TO_NEW_MAPPING[duration];
    return RENTAL_DURATIONS[newFormat as keyof typeof RENTAL_DURATIONS].hours;
  }
  
  // Legacy: If duration is in the format 'Nx8_HOURS', extract N and multiply by 8
  if (duration.includes('x8_HOURS')) {
    const [count] = duration.split('x');
    return parseInt(count) * 8;
  }
  
  // Default fallback
  return 8; // Default to 8 hours if we can't determine
}

/**
 * Mendapatkan deskripsi durasi
 * @param duration Duration string
 * @returns Deskripsi durasi
 */
export function getDurationDescription(duration: string): string {
  // First, try to get description from RENTAL_DURATIONS if it's a valid key (new format)
  const durationKey = duration as RentalDuration;
  if (durationKey in RENTAL_DURATIONS) {
    return RENTAL_DURATIONS[durationKey].description;
  }
  
  // Check if it's old format and convert
  if (duration in OLD_TO_NEW_MAPPING) {
    const newFormat = OLD_TO_NEW_MAPPING[duration];
    return RENTAL_DURATIONS[newFormat as keyof typeof RENTAL_DURATIONS].description;
  }
  
  // Fallback
  return `Sewa ${formatDuration(duration)}`;
}

/**
 * Validasi apakah duration string valid
 * @param duration Duration string
 * @returns True jika valid
 */
export function isValidDuration(duration: string): boolean {
  return duration in RENTAL_DURATIONS || duration in OLD_TO_NEW_MAPPING;
}

/**
 * Mendapatkan semua opsi durasi untuk dropdown/select
 * @returns Array of duration options
 */
export function getDurationOptions(): Array<{ value: string; label: string; hours: number }> {
  return Object.entries(RENTAL_DURATIONS).map(([key, value]) => ({
    value: key,
    label: value.label,
    hours: value.hours
  }));
}
