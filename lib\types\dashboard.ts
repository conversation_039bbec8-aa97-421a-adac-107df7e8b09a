export interface RevenueData {
  date: string;
  revenue: number;
}

export interface TopProduct {
  id: string;
  name: string;
  capacity: number;
  rentalCount: number;
}

export interface DashboardStats {
  totalRevenue: number;
  totalRentals: number;
  totalProducts: number;
  occupancyRate: number;
  recentRentals: RecentRental[];
  revenueGrowth: number;
  rentalGrowth: number;
  productGrowth: number;
  occupancyGrowth: number;
  monthlyRevenue: MonthlyRevenue[];
  monthlyRentals: MonthlyRentals[];
  topProducts: TopProduct[];
}

export interface RecentRental {
  id: string;
  productName: string;
  customerName: string;
  amount: number;
  status: string;
  date: Date;
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
}

export interface MonthlyRentals {
  month: string;
  count: number;
} 
