import { useState, useCallback } from 'react';
import { SearchResult, PhotonFeature, NominatimResult } from '@/lib/types/map';

// Fungsi utility untuk mengakses sessionStorage dengan aman
const safeSessionStorage = {
  getItem: (key: string): string | null => {
    try {
      return sessionStorage.getItem(key);
    } catch (e) {
      console.error("Error accessing sessionStorage:", e);
      return null;
    }
  },
  setItem: (key: string, value: string): boolean => {
    try {
      sessionStorage.setItem(key, value);
      return true;
    } catch (e) {
      console.error("Error writing to sessionStorage:", e);
      return false;
    }
  }
};

export function useMapSearch() {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");

  // Fungsi untuk mencari dengan Photon API
  const searchWithPhoton = useCallback(async (query: string) => {
    try {
      // Validasi query
      if (!query || query.trim().length < 2) {
        console.log("[MapSearch] Query terlalu pendek untuk Photon API, menggunakan OSM");
        return await searchWithOSM(query);
      }

      console.log("[MapSearch] Mencoba pencarian dengan Photon API:", query);

      // Cek apakah hasil ada di cache (dengan key yang include bahasa)
      const cacheKey = `photon_search_default_${query.toLowerCase()}`;
      const cachedResults = safeSessionStorage.getItem(cacheKey);

      if (cachedResults) {
        try {
          const parsedResults = JSON.parse(cachedResults);
          console.log("[MapSearch] Menggunakan hasil cache Photon API:", parsedResults.length);
          setSearchResults(parsedResults);
          setStatusMessage("");
          return parsedResults;
        } catch (e) {
          console.error("Error parsing cached results:", e);
          // Lanjut ke pencarian normal jika ada error parsing
        }
      }

      // Tambahkan status untuk menunjukkan proses pencarian
      setStatusMessage("Mencari lokasi di Photon API...");
      
      const response = await fetch(
        `/api/geocode/photon?q=${encodeURIComponent(query)}&lang=default`
      ).catch(error => {
        console.error("Error fetching from Photon:", error);
        throw new Error("Gagal terhubung ke API Photon");
      });
      
      if (!response) {
        throw new Error("Tidak ada respon dari API Photon");
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error dari Photon API: ${response.status} ${response.statusText}`, errorText);

        // Jika timeout atau bad request, coba menggunakan OSM API sebagai fallback
        if (response.status === 504 || response.status === 400 || response.status >= 500) {
          console.log(`[MapSearch] Photon API error ${response.status}, mencoba OSM sebagai fallback`);
          return await searchWithOSM(query);
        }

        throw new Error(`Error dari Photon API: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json().catch(error => {
        console.error("Error parsing Photon API response:", error);
        throw new Error("Format respon Photon API tidak valid");
      });
      
      if (!data || !data.features) {
        console.log("Tidak ada hasil pencarian dari Photon API:", data);
        setSearchResults([]);
        return [];
      }
      
      if (data.features.length === 0) {
        // Tidak ada hasil pencarian
        console.log("[MapSearch] Photon API tidak menemukan hasil, mencoba OSM sebagai fallback");
        return await searchWithOSM(query);
      }
      
      // Transform data dari Photon ke format SearchResult
      const results: SearchResult[] = data.features.map((feature: PhotonFeature) => {
        const [lon, lat] = feature.geometry.coordinates;
        
        // Membuat display_name dari properti yang tersedia
        const nameParts = [];
        if (feature.properties.name) nameParts.push(feature.properties.name);
        if (feature.properties.street) {
          if (feature.properties.housenumber) {
            nameParts.push(`${feature.properties.street} ${feature.properties.housenumber}`);
          } else {
            nameParts.push(feature.properties.street);
          }
        }
        
        if (feature.properties.city) nameParts.push(feature.properties.city);
        if (feature.properties.state) nameParts.push(feature.properties.state);
        if (feature.properties.country) nameParts.push(feature.properties.country);
        
        // Pastikan ada nama tempat, jika kosong gunakan placeholder
        if (nameParts.length === 0) {
          nameParts.push("Lokasi di NTB");
        }
        
        return {
          lat,
          lon,
          display_name: nameParts.join(', '),
          properties: {
            state: feature.properties.state,
            city: feature.properties.city,
            name: feature.properties.name
          }
        };
      });
      
      // Simpan hasil ke cache untuk penggunaan di masa depan
      safeSessionStorage.setItem(cacheKey, JSON.stringify(results));
      
      console.log(`[MapSearch] ${results.length} hasil ditemukan dari Photon API`);
      setSearchResults(results);
      setStatusMessage("");
      return results;
    } catch (error) {
      console.warn("Error dalam searchWithPhoton, mencoba fallback:", error);

      // Coba fallback ke OSM jika Photon error
      try {
        console.log("[MapSearch] Menggunakan OSM sebagai fallback untuk error Photon");
        const fallbackResults = await searchWithOSM(query);
        return fallbackResults;
      } catch (fallbackError) {
        console.error("Fallback ke OSM juga gagal:", fallbackError);
        setStatusMessage("Semua layanan pencarian gagal. Coba lagi nanti.");
        setSearchResults([]);
        throw fallbackError; // Re-throw untuk ditangani di level atas
      }
    }
  }, []);

  // Fungsi untuk mencari dengan OSM API sebagai fallback
  const searchWithOSM = useCallback(async (query: string) => {
    try {
      // Validasi query
      if (!query || query.trim().length < 1) {
        console.log("[MapSearch] Query kosong untuk OSM API");
        setSearchResults([]);
        setStatusMessage("Masukkan kata kunci pencarian");
        return [];
      }

      console.log("[MapSearch] Mencoba pencarian dengan OSM API:", query);
      setStatusMessage("Mencari lokasi di OSM API...");
      
      // Cek apakah hasil ada di cache (dengan key yang include bahasa)
      const cacheKey = `osm_search_id_${query.toLowerCase()}`;
      const cachedResults = safeSessionStorage.getItem(cacheKey);
      
      if (cachedResults) {
        try {
          const parsedResults = JSON.parse(cachedResults);
          console.log("[MapSearch] Menggunakan hasil cache OSM API:", parsedResults.length);
          setSearchResults(parsedResults);
          setStatusMessage("");
          return parsedResults;
        } catch (e) {
          console.error("Error parsing cached OSM results:", e);
          // Lanjut ke pencarian normal jika ada error parsing
        }
      }
      
      const response = await fetch(
        `/api/geocode/nominatim?q=${encodeURIComponent(query)}&lang=id`
      ).catch(error => {
        console.error("Error fetching from Nominatim:", error);
        throw new Error("Gagal terhubung ke API OSM Nominatim");
      });
      
      if (!response) {
        throw new Error("Tidak ada respon dari API OSM");
      }
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error dari OSM API: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Error dari OSM API: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!Array.isArray(data) || data.length === 0) {
        console.log("Tidak ada hasil pencarian dari OSM API");
        setSearchResults([]);
        return [];
      }
      
      // Transform data dari OSM ke format SearchResult
      const results: SearchResult[] = data.map((item: NominatimResult) => {
        return {
          lat: parseFloat(item.lat),
          lon: parseFloat(item.lon),
          display_name: item.display_name,
          properties: {
            state: item.address?.state,
            city: item.address?.city || item.address?.town || item.address?.village,
            name: item.address?.city || item.address?.town || item.address?.village
          }
        };
      });
      
      // Simpan hasil ke cache untuk penggunaan di masa depan
      safeSessionStorage.setItem(cacheKey, JSON.stringify(results));
      
      console.log(`[MapSearch] ${results.length} hasil ditemukan dari OSM API`);
      setSearchResults(results);
      setStatusMessage("");
      return results;
    } catch (error) {
      console.error("Error dalam searchWithOSM:", error);
      setStatusMessage(`Error: ${error instanceof Error ? error.message : 'Pencarian OSM gagal'}`);
      setSearchResults([]);
      return [];
    }
  }, []);

  // Fungsi untuk melakukan pencarian lokasi dengan system fallback
  const searchLocation = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setStatusMessage("");
      return [];
    }

    setIsLoading(true);

    try {
      // Coba dengan Photon API dahulu
      const results = await searchWithPhoton(query);
      return results;
    } catch (error) {
      // Jangan log error jika sudah ada fallback yang berhasil
      console.warn("Pencarian dengan Photon gagal, fallback sudah dijalankan:", error);

      // Coba langsung dengan OSM jika Photon benar-benar gagal
      try {
        const osmResults = await searchWithOSM(query);
        return osmResults;
      } catch (osmError) {
        console.error("Semua layanan pencarian gagal:", osmError);
        setStatusMessage("Pencarian gagal. Silakan coba lagi.");
        return [];
      }
    } finally {
      setIsLoading(false);
    }
  }, [searchWithPhoton, searchWithOSM]);

  return {
    searchResults,
    isLoading,
    statusMessage,
    searchLocation,
    setSearchResults
  };
}

export default useMapSearch; 
