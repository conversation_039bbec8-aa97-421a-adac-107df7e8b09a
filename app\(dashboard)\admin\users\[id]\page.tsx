import { Suspense } from 'react';
import { getUserById } from "@/lib/data/user";
import { notFound } from "next/navigation";
import { BackButton } from "@/app/components/ui/back-button";
import { UserRentals } from "@/app/components/user/user-rentals";
import UserDetailLoading from "./loading";

async function UserContent({ id }: { id: string }) {
  try {
    const user = await getUserById(id);
    if (!user) notFound();

    return (
      <>
        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4 dark:text-white">{user.name}</h1>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h2 className="text-lg font-semibold mb-2 dark:text-white">Informasi Pengguna</h2>
                <dl className="space-y-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                    <dd className="text-sm text-gray-900 dark:text-gray-200">{user.email}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Role</dt>
                    <dd className="text-sm text-gray-900 dark:text-gray-200">
                      {user.role === "ADMIN" ? "Admin" : "Customer"}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                    <dd className="text-sm">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.emailVerified ?? false
                            ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                            : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                          }`}
                      >
                        {user.emailVerified ? "Terverifikasi" : "Belum Verifikasi"}
                      </span>
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4 dark:text-white">Riwayat Penyewaan</h2>
            <UserRentals userId={id} />
          </div>
        </div>
      </>
    );
  } catch (error) {
    console.error("Error:", error);
    throw new Error("Gagal memuat detail pengguna");
  }
}

export default async function UserDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  // Await params sebelum mengakses propertinya
  const { id } = await params;

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <BackButton href="/dashboard/users" />
      <Suspense fallback={<UserDetailLoading />}>
        <UserContent id={id} />
      </Suspense>
    </div>
  );
}
