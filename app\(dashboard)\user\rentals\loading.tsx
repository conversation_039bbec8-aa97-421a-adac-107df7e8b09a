import { Skeleton } from "@/app/components/ui/skeleton";

export default function RentalsLoading() {
  return (
    <div className="">
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-contain bg-right bg-no-repeat"></div>
        <div className="relative">
          <Skeleton className="h-9 w-48 mb-3 animate-pulse" />
          <Skeleton className="h-4 w-80 animate-pulse" />
        </div>
      </div>

      {/* Search and Filter Section Skeleton */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Skeleton className="h-10 w-full animate-pulse" />
        </div>
        <Skeleton className="h-10 w-24 animate-pulse" />
      </div>

      {/* Rental Cards Skeleton */}
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="md:flex">
              {/* Product Image Skeleton */}
              <div className="md:w-1/4 h-48 md:h-auto overflow-hidden bg-gray-100 dark:bg-gray-700 relative">
                <Skeleton className="w-full h-full animate-pulse" />
              </div>

              {/* Content Section */}
              <div className="md:w-3/4">
                {/* Header */}
                <div className="p-6 pb-4">
                  <div className="flex justify-between items-start mb-3">
                    <Skeleton className="h-7 w-48 animate-pulse" />
                    <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
                  </div>
                  <Skeleton className="h-4 w-40 animate-pulse" />
                </div>

                {/* Content */}
                <div className="px-6 pb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      {/* Date Range */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 animate-pulse" />
                        <Skeleton className="h-4 w-56 animate-pulse" />
                      </div>

                      {/* Duration */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 animate-pulse" />
                        <Skeleton className="h-4 w-24 animate-pulse" />
                      </div>

                      {/* Location */}
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4 animate-pulse" />
                        <Skeleton className="h-4 w-40 animate-pulse" />
                      </div>
                    </div>

                    {/* Price Section */}
                    <div>
                      <Skeleton className="h-4 w-20 mb-2 animate-pulse" />
                      <Skeleton className="h-7 w-32 animate-pulse" />
                    </div>
                  </div>
                </div>

                {/* Footer Actions */}
                <div className="px-6 py-4 border-t border-gray-100 dark:border-gray-700 flex justify-end gap-2">
                  <Skeleton className="h-9 w-16 animate-pulse" />
                  <Skeleton className="h-9 w-24 animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
