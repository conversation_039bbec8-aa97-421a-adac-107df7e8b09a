"use client";

import { cn } from "@/lib/utils";

// Base skeleton component with proper animations
function SkeletonBase({ 
  className, 
  variant = "default",
  ...props 
}: { 
  className?: string; 
  variant?: "default" | "violet" | "shimmer";
} & React.HTMLAttributes<HTMLDivElement>) {
  const variantClasses = {
    default: "bg-gray-200 dark:bg-gray-700",
    violet: "bg-violet-100 dark:bg-violet-900/30",
    shimmer: "bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%] animate-[shimmer_1.5s_ease-in-out_infinite]"
  };

  return (
    <div
      className={cn(
        "rounded-lg animate-pulse",
        variantClasses[variant],
        className
      )}
      {...props}
    />
  );
}

// Desktop Navigation Skeleton (≥1024px)
export function DesktopNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("hidden lg:block", className)}>
      {/* Sidebar Navigation Skeleton */}
      <div className="space-y-2">
        {/* Navigation Items with Realistic Widths */}
        {[
          { name: "Dashboard", width: "w-20" },      // 80px - 9 characters
          { name: "Katalog", width: "w-16" },        // 64px - 7 characters
          { name: "Rental Saya", width: "w-28" },    // 112px - 11 characters ⭐ LONGEST
          { name: "Status Operasi", width: "w-32" }, // 128px - 14 characters
          { name: "Pembayaran", width: "w-24" },     // 96px - 10 characters
          { name: "Profil", width: "w-16" }          // 64px - 6 characters
        ].map((item, index) => (
          <div
            key={index}
            className="flex items-center px-3 py-3 rounded-lg"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Icon Skeleton */}
            <div className="mr-3 rounded-md p-2 bg-gray-100 dark:bg-gray-800">
              <SkeletonBase className="h-5 w-5" variant="default" />
            </div>
            
            {/* Text Content */}
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                {/* Main text with specific width */}
                <SkeletonBase 
                  className={cn("h-4", item.width)} 
                  variant={item.name === "Rental Saya" ? "violet" : "default"}
                />
                {/* Badge for Katalog */}
                {item.name === "Katalog" && (
                  <SkeletonBase className="h-4 w-12 rounded-full ml-2" variant="violet" />
                )}
              </div>
              {/* Description text */}
              <SkeletonBase className="h-3 w-32" variant="default" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Tablet Navigation Skeleton (769px-1023px)
export function TabletNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("hidden md:block lg:hidden", className)}>
      {/* Horizontal Navigation Bar */}
      <div className="flex items-center space-x-3">
        {[
          { name: "Dashboard", width: "w-18" },    // 72px
          { name: "Katalog", width: "w-14" },      // 56px
          { name: "Rental Saya", width: "w-24" },  // 96px ⭐ LONGEST
          { name: "Profil", width: "w-14" }        // 56px
        ].map((item, index) => (
          <div
            key={index}
            className="flex items-center px-3 py-2 rounded-lg bg-white dark:bg-gray-800 shadow-sm"
            style={{ animationDelay: `${index * 80}ms` }}
          >
            {/* Icon */}
            <SkeletonBase className="h-4 w-4 mr-2" variant="default" />
            {/* Text */}
            <SkeletonBase 
              className={cn("h-4", item.width)} 
              variant={item.name === "Rental Saya" ? "violet" : "default"}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Mobile Navigation Skeleton (≤768px)
export function MobileNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("md:hidden", className)}>
      {/* Mobile Menu Button */}
      <div className="flex items-center justify-between p-4">
        <SkeletonBase className="h-6 w-24" variant="default" />
        <SkeletonBase className="h-10 w-10 rounded-xl" variant="violet" />
      </div>
    </div>
  );
}

// Mobile Bottom Navigation Skeleton
export function MobileBottomNavigationSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "lg:hidden fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-t border-gray-200 dark:border-gray-700 z-40",
      className
    )}>
      <div className="grid grid-cols-3 h-16">
        {[
          { name: "Beranda", width: "w-12" },     // 48px - 7 characters
          { name: "Katalog", width: "w-14" },     // 56px - 7 characters
          { name: "Rental", width: "w-12" }       // 48px - 6 characters (shortened)
        ].map((item, index) => (
          <div 
            key={index} 
            className="flex flex-col items-center justify-center px-1 py-2"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Icon */}
            <SkeletonBase className="w-5 h-5 mb-1 rounded" variant="default" />
            {/* Text */}
            <SkeletonBase className={cn("h-3", item.width)} variant="default" />
          </div>
        ))}
      </div>
    </div>
  );
}

// Mobile Menu Panel Skeleton (when menu is open)
export function MobileMenuPanelSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn(
      "fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl shadow-2xl z-50 border-l border-gray-200 dark:border-gray-700",
      className
    )}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <SkeletonBase className="h-6 w-32 mb-2" variant="violet" />
        <SkeletonBase className="h-4 w-48" variant="default" />
      </div>

      {/* Menu Items */}
      <div className="p-4 space-y-2">
        {[
          { name: "Beranda", width: "w-16" },      // 64px - 7 characters
          { name: "Katalog", width: "w-14" },      // 56px - 7 characters
          { name: "Rental Saya", width: "w-20" },  // 80px - 11 characters ⭐ LONGEST
          { name: "Profil", width: "w-12" }        // 48px - 6 characters
        ].map((item, index) => (
          <div
            key={index}
            className="flex items-center min-h-[44px] px-4 py-3 rounded-xl"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            {/* Icon */}
            <SkeletonBase className="w-5 h-5 mr-3 rounded" variant="default" />
            {/* Text */}
            <SkeletonBase 
              className={cn("h-4", item.width)} 
              variant={item.name === "Rental Saya" ? "violet" : "default"}
            />
            {/* Active indicator for first item */}
            {index === 0 && (
              <SkeletonBase className="w-2 h-2 ml-2 rounded-full" variant="violet" />
            )}
          </div>
        ))}
      </div>

      {/* Logout Button */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
        <SkeletonBase className="w-full h-11 rounded-xl" variant="shimmer" />
      </div>
    </div>
  );
}

// User Profile Skeleton
export function UserProfileSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("border-t border-gray-200 dark:border-gray-700 pt-4 mt-4 px-4", className)}>
      <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
        <div className="flex items-center gap-3 mb-3">
          <SkeletonBase className="w-10 h-10 rounded-full" variant="violet" />
          <div className="flex-1">
            <SkeletonBase className="h-4 w-24 mb-1" variant="default" />
            <SkeletonBase className="h-3 w-32" variant="default" />
          </div>
        </div>
        <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <SkeletonBase className="h-8 w-8 rounded-full" variant="default" />
          <div className="flex gap-1">
            <SkeletonBase className="h-8 w-8 rounded-full" variant="default" />
            <SkeletonBase className="h-8 w-8 rounded-full" variant="default" />
          </div>
        </div>
      </div>
    </div>
  );
}
