'use client';

import React, { useState, useMemo, useCallback, Suspense } from "react";
import { User } from "@/lib/types/user";
import { Search, ChevronLeft, ChevronRight } from "lucide-react";
import { useDebounce } from "@/lib/hooks";
import { useRouter, useSearchParams } from "next/navigation";
import { UserTableRow } from "../user/user-table-row";
import Link from "next/link";



export enum Role {
  ADMIN = "admin",
  USER = "user"
}

interface UserTableProps {
  searchQuery: string;
  users: User[];
  currentPage: number;
  totalPages: number;
  totalUsers: number;
}

function UserTableInner({ searchQuery, users: initialUsers = [], currentPage, totalPages, totalUsers }: UserTableProps) {
  const [search, setSearch] = useState(searchQuery);
  const debouncedSearch = useDebounce(search, 300);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Update URL with search parameter when debounced value changes
  React.useEffect(() => {
    if (debouncedSearch !== searchQuery) {
      const params = new URLSearchParams(searchParams.toString());
      if (debouncedSearch) {
        params.set('search', debouncedSearch);
      } else {
        params.delete('search');
      }
      router.push(`?${params.toString()}`);
    }
  }, [debouncedSearch, router, searchParams, searchQuery]);

  const filteredUsers = useMemo(() => {
    return initialUsers.filter(user => {
      const matchesSearch = !debouncedSearch ||
        user.name?.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        user.email.toLowerCase().includes(debouncedSearch.toLowerCase());

      const matchesRole = roleFilter === 'all' || user.role === roleFilter;

      return matchesSearch && matchesRole;
    });
  }, [initialUsers, debouncedSearch, roleFilter]);

  // Use server-provided users (already paginated)
  const paginatedUsers = initialUsers;

  // Calculate pagination info
  const itemsPerPage = 5;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalUsers);

  const handleUpdate = useCallback(() => {
    router.refresh();
  }, [router]);

  if (initialUsers.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 dark:text-gray-400">Tidak ada data pengguna</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Manajemen Pengguna
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Kelola pengguna dan peran mereka dalam sistem
          </p>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="min-w-[140px] h-11 rounded-lg border border-border px-3 py-2 text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
          >
            <option value="all">Semua Role</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
          </select>

          <div className="relative min-w-[280px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              type="text"
              placeholder="Cari nama atau email pengguna..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full h-11 pl-10 pr-4 py-2 text-sm border border-border rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-colors"
            />
          </div>
        </div>
      </div>

      {/* Results Summary */}
      {debouncedSearch && (
        <div className="text-sm text-muted-foreground">
          Menampilkan {filteredUsers.length} hasil untuk &quot;{debouncedSearch}&quot;
        </div>
      )}

      {/* Table Container */}
      <div className="bg-card shadow-sm rounded-xl border border-border overflow-hidden">
        <div className="overflow-x-auto scrollbar-hide">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted/50">
              <tr>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Pengguna
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Kontak
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Terdaftar
                </th>
                <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {paginatedUsers.length > 0 ? (
                paginatedUsers.map((user) => (
                  <UserTableRow
                    key={user.id}
                    user={user}
                    onUpdate={handleUpdate}
                  />
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center space-y-3">
                      <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                        <Search className="w-6 h-6 text-muted-foreground" />
                      </div>
                      <div className="text-sm font-medium text-foreground">
                        Tidak ada pengguna ditemukan
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {debouncedSearch
                          ? `Tidak ada hasil untuk "${debouncedSearch}"`
                          : 'Belum ada pengguna yang terdaftar'
                        }
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Server-Side Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6 px-2">
          <div className="text-sm text-muted-foreground">
            Menampilkan {startIndex + 1}-{endIndex} dari {totalUsers} data
          </div>

          <div className="flex items-center gap-2">
            {/* Previous Button */}
            {currentPage > 1 ? (
              <Link href={`?page=${currentPage - 1}`}>
                <button className="px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground flex items-center gap-1 transition-colors">
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </button>
              </Link>
            ) : (
              <button disabled className="px-3 py-2 text-sm bg-background border border-border rounded-md opacity-50 cursor-not-allowed flex items-center gap-1">
                <ChevronLeft className="w-4 h-4" />
                Previous
              </button>
            )}

            {/* Page Numbers */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Link key={page} href={`?page=${page}`}>
                <button
                  className={`px-3 py-2 text-sm border rounded-md min-w-[40px] transition-colors ${
                    page === currentPage
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-background text-foreground border-border hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  {page}
                </button>
              </Link>
            ))}

            {/* Next Button */}
            {currentPage < totalPages ? (
              <Link href={`?page=${currentPage + 1}`}>
                <button className="px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground flex items-center gap-1 transition-colors">
                  Next
                  <ChevronRight className="w-4 h-4" />
                </button>
              </Link>
            ) : (
              <button disabled className="px-3 py-2 text-sm bg-background border border-border rounded-md opacity-50 cursor-not-allowed flex items-center gap-1">
                Next
                <ChevronRight className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      )}

      {/* Table Footer with Stats */}
      {totalUsers > 0 && (
        <div className="flex items-center justify-between text-sm text-muted-foreground px-2 mt-4">
          <div>
            Total {totalUsers} pengguna
          </div>
          <div className="flex items-center space-x-4">
            <span>Admin: {initialUsers.filter(u => u.role === 'ADMIN' || u.role === 'admin').length}</span>
            <span>User: {initialUsers.filter(u => u.role === 'USER' || u.role === 'user').length}</span>
          </div>
        </div>
      )}
    </div>
  );
}

// Wrapper component with Suspense for useSearchParams
export function UserTable(props: UserTableProps) {
  return (
    <Suspense fallback={
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Manajemen Pengguna
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Memuat data pengguna...
            </p>
          </div>
        </div>
        <div className="bg-card shadow-sm rounded-xl border border-border overflow-hidden">
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Memuat data pengguna...</p>
          </div>
        </div>
      </div>
    }>
      <UserTableInner {...props} />
    </Suspense>
  );
}
