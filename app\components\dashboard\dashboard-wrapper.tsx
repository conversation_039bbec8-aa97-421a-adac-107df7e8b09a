'use client';

import { ReactNode } from 'react';
import dynamic from 'next/dynamic';

// Import ErrorBoundary secara dinamis di dalam client component
const DashboardErrorBoundary = dynamic(
  () => import('@/app/components/dashboard/dashboard-error-boundary'),
  { ssr: false }
);

interface DashboardWrapperProps {
  children: ReactNode;
}

export default function DashboardWrapper({ children }: DashboardWrapperProps) {
  return (
    <DashboardErrorBoundary>
      {children}
    </DashboardErrorBoundary>
  );
} 
