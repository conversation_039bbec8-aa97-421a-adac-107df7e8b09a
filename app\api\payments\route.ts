import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import MidtransService from "@/lib/services/midtrans";
import { calculateOvertimeCost } from "@/lib/utils/calculate";

export const runtime = 'nodejs';

// GET /api/payments - Get all payments
export async function GET() {
  try {
    const session = await getSession();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const payments = await prisma.payment.findMany({
      include: {
        rental: {
          include: {
            product: true,
            user: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json(payments);
  } catch (error) {
    console.error("[PAYMENTS_GET]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// POST /api/payments - Create new payment
export async function POST(request: Request) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { rentalId, type } = await request.json();

    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        payment: true,
        product: true // Include product untuk mendapatkan harga dasar
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }

    const deposit = Math.floor(rental.amount * 0.5);
    let amount;

    if (type === 'deposit') {
      amount = deposit;
    } else {
      // Hitung total sisa pembayaran + overtime
      const overtime = rental.overtime || 0;
      let overtimeCost = 0;

      if (overtime > 0) {
        // Jika ada payment.overtime yang sudah dihitung sebelumnya, gunakan itu
        if (rental.payment?.overtime) {
          overtimeCost = rental.payment.overtime;
        } else {
          // Jika tidak, hitung ulang berdasarkan overtime dan tarif produk
          overtimeCost = calculateOvertimeCost(overtime, rental.product.price);
        }
      }

      amount = Math.floor(rental.amount * 0.5) + overtimeCost; // 50% sisa + overtime
    }

    // Buat token pembayaran Midtrans dengan timestamp untuk menghindari duplikasi
    const payment = await MidtransService.createPayment({
      orderId: `${rental.id}_${type}_${Date.now()}`,
      amount: amount,
      email: session.user.email || "",
      name: session.user.name || "Customer",
      productName: `${type === 'deposit' ? 'Deposit' : 'Sisa Pembayaran'} Rental Genset`
    });

    return NextResponse.json({ token: payment.token });
  } catch (error) {
    console.error("[PAYMENT_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal memproses pembayaran" },
      { status: 500 }
    );
  }
}
