// Konfigurasi metadata untuk Next.js
export const siteMetadata = {
  title: 'Rental Genset',
  description: 'Penyewaan genset terpercaya untuk berbagai kebutuhan daya Anda',
  siteUrl: 'https://rental-genset.com',
  siteName: 'Rental Genset',
  themeColor: '#2463EB',
  locale: 'id-ID',
  twitter: {
    handle: '@rental_genset',
    site: '@rental_genset',
    cardType: 'summary_large_image',
  },
  facebook: {
    appId: '',
  },
};

// Helper function untuk mendapatkan URL gambar dengan path
export function getImageUrl(path: string): string {
  return `${siteMetadata.siteUrl}${path}`;
}

// Konfigurasi HTTP cache untuk berbagai jenis asset
export const cacheConfig = {
  images: {
    maxAge: 60 * 60 * 24 * 30, // 30 hari untuk gambar statis
    staleWhileRevalidate: 60 * 60 * 24 * 7, // 7 hari
  },
  fonts: {
    maxAge: 60 * 60 * 24 * 365, // 1 tahun untuk font
    staleWhileRevalidate: 60 * 60 * 24 * 30, // 30 hari
  },
  static: {
    maxAge: 60 * 60 * 24 * 7, // 7 hari untuk asset statis lainnya
    staleWhileRevalidate: 60 * 60 * 24, // 1 hari
  },
  api: {
    maxAge: 60, // 1 menit untuk API response
    staleWhileRevalidate: 60 * 5, // 5 menit
  }
}; 
