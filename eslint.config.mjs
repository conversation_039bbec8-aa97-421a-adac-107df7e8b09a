import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import next from "next/eslint";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
];

/** @type {import('next/eslint').NextEslintConfig} */
const config = {
  extends: [...next.configs],
  ignores: [
    "**/node_modules/**",
    ".next/**",
    "out/**",
    "dist/**",
    "app/(dashboard)/admin/notifications-test/**",
    "app/(dashboard)/user/layout.tsx",
    "app/(dashboard)/user/payments/**",
    "app/(dashboard)/user/profile/**",
    "app/api/**",
    "app/components/**",
    "lib/**",
  ],
  rules: {
    "@typescript-eslint/no-unused-vars": "off",
    "react-hooks/exhaustive-deps": "off",
  },
};

export default config;
