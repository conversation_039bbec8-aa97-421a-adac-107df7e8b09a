"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/app/components/ui/badge";
import { Wifi, WifiOff, RefreshCw } from "lucide-react";
import { useNotifications } from "@/app/components/providers/notification-provider";

export function NotificationStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [lastSync, setLastSync] = useState<Date>(new Date());
  const { isLoading, refreshAll } = useNotifications();

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Update last sync time when not loading
    if (!isLoading) {
      setLastSync(new Date());
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [isLoading]);

  const formatLastSync = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Baru saja';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m lalu`;
    return `${Math.floor(diffInSeconds / 3600)}j lalu`;
  };

  return (
    <div className="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
      {/* Connection Status */}
      <div className="flex items-center gap-1">
        {isOnline ? (
          <Wifi className="w-3 h-3 text-green-500" />
        ) : (
          <WifiOff className="w-3 h-3 text-red-500" />
        )}
        <span className={isOnline ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}>
          {isOnline ? "Online" : "Offline"}
        </span>
      </div>

      {/* Sync Status */}
      <div className="flex items-center gap-1">
        {isLoading ? (
          <RefreshCw className="w-3 h-3 animate-spin text-blue-500" />
        ) : (
          <div className="w-2 h-2 bg-green-400 rounded-full" />
        )}
        <span>
          {isLoading ? "Sinkronisasi..." : `Sinkron ${formatLastSync(lastSync)}`}
        </span>
      </div>
    </div>
  );
}

/**
 * Compact version for mobile
 */
export function NotificationStatusCompact() {
  const [isOnline, setIsOnline] = useState(true);
  const { isLoading } = useNotifications();

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return (
      <Badge variant="destructive" className="text-xs">
        <WifiOff className="w-3 h-3 mr-1" />
        Offline
      </Badge>
    );
  }

  if (isLoading) {
    return (
      <Badge variant="secondary" className="text-xs">
        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
        Sync
      </Badge>
    );
  }

  return (
    <div className="w-2 h-2 bg-green-400 rounded-full" title="Online & Tersinkron" />
  );
}
