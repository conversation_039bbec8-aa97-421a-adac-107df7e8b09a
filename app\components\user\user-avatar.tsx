"use client";

import Image from "next/image";

interface UserAvatarProps {
    imageUrl?: string | null;
    name: string;
    size?: "sm" | "md" | "lg";
}

export function UserAvatar({ imageUrl, name, size = "md" }: UserAvatarProps) {
    const sizeClasses = {
        sm: "h-8 w-8",
        md: "h-10 w-10",
        lg: "h-12 w-12",
    };

    return (
        <div className={`rounded-full overflow-hidden ${sizeClasses[size]}`}>
            {imageUrl ? (
                <Image
                    src={imageUrl}
                    alt={name}
                    width={size === "sm" ? 32 : size === "md" ? 40 : 48}
                    height={size === "sm" ? 32 : size === "md" ? 40 : 48}
                    className="object-cover"
                />
            ) : (
                <div className="flex items-center justify-center bg-gray-200 text-gray-500">
                    {name.charAt(0).toUpperCase()}
                </div>
            )}
        </div>
    );
} 
