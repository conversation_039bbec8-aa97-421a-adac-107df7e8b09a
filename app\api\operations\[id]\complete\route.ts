import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { revalidatePath } from "next/cache";
import { updateProductStock } from "@/lib/actions/product";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    // Only admin can complete operations
    if (!session?.user || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const { id: rentalId } = await params;

    // Cek apakah rental ada
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      select: {
        id: true,
        status: true,
        productId: true,
        quantity: true,
        product: {
          select: {
            id: true,
            stock: true
          }
        }
      }
    });

    if (!rental) {
      return NextResponse.json({ error: "Rental tidak ditemukan" }, { status: 404 });
    }

    // Hanya bisa menyelesaikan rental yang berstatus operational
    if (rental.status !== "ACTIVE") {
      return NextResponse.json(
        { error: "Hanya rental dengan status operasional yang dapat diselesaikan" },
        { status: 400 }
      );
    }

    // Ambil data dari request jika ada
    let overtimeHours = 0;
    try {
      const body = await request.json();
      overtimeHours = body.overtimeHours || 0;
    } catch {
      // Jika tidak ada body atau parsing gagal, gunakan nilai default
    }

    // 1. Update status rental menjadi completed
    await prisma.rental.update({
      where: { id: rentalId },
      data: {
        status: "COMPLETED",
        operationalEnd: new Date(),
        notes: overtimeHours > 0 ? `Overtime: ${overtimeHours} jam` : undefined
      }
    });

    console.log(`[COMPLETE] Rental ${rentalId} status diubah dari "ACTIVE" menjadi "COMPLETED"`);

    // 2. Kembalikan stok produk menggunakan fungsi updateProductStock
    // Mengembalikan stok karena operasi sudah selesai, produk bisa digunakan kembali
    console.log(`[COMPLETE] Mengembalikan stok produk: +${rental.quantity} unit`);
    const stockUpdateResult = await updateProductStock(rental.productId, rental.quantity);

    if (!stockUpdateResult.success) {
      console.error(`[COMPLETE] Gagal mengembalikan stok: ${stockUpdateResult.error}`);
      // Tetap lanjutkan proses meskipun ada error pada stok
    } else {
      console.log(`[COMPLETE] Stok berhasil dikembalikan. Stok baru: ${stockUpdateResult.data?.stock}`);
    }

    // 3. Tambahkan overtime fee ke payment jika ada
    if (overtimeHours > 0) {
      // Ambil data payment
      const payment = await prisma.payment.findFirst({
        where: { rentalId: rentalId }
      });

      if (payment) {
        // Hitung overtime fee (misalnya 20% lebih mahal dari rate normal per jam)
        const product = await prisma.product.findUnique({
          where: { id: rental.productId }
        });

        if (product) {
          const hourlyRate = product.overtimeRate || Math.ceil(product.price / 8 * 1.2);
          const overtimeFee = overtimeHours * hourlyRate;

          // Update payment dengan overtime fee
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              overtime: overtimeFee,
              remaining: payment.remaining + overtimeFee
            }
          });

          console.log(`[COMPLETE] Payment diupdate dengan overtime fee: ${overtimeFee}`);
        }
      }
    }

    // Revalidasi path terkait dengan force revalidation
    revalidatePath("/admin/rentals", "layout");
    revalidatePath(`/admin/rentals/${rentalId}`, "layout");
    revalidatePath("/admin/operations", "layout");
    revalidatePath(`/admin/operations/${rentalId}`, "layout");
    revalidatePath("/admin/products", "layout"); // Revalidasi halaman produk untuk melihat perubahan stok
    revalidatePath("/user/operations", "layout");
    revalidatePath(`/user/operations/${rentalId}`, "layout");
    revalidatePath("/admin/dashboard", "layout");
    revalidatePath("/user/dashboard", "layout");

    return NextResponse.json({
      success: true,
      message: "Operasi berhasil diselesaikan"
    });
  } catch (error) {
    console.error("Error completing operation:", error);
    return NextResponse.json(
      { error: "Gagal menyelesaikan operasi" },
      { status: 500 }
    );
  }
}