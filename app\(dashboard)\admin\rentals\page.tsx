import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { Search } from "lucide-react";
import { Input } from "@/app/components/ui/input";
import { RentalsTable } from "@/app/components/admin/rentals-table";

// Pastikan halaman selalu up-to-date
export const dynamic = 'force-dynamic';



export default async function AdminRentalsPage({
  searchParams
}: {
  searchParams: Promise<{ q?: string }>
}) {
  const session = await getSession();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil parameter pencarian dan tunggu resolusinya
  const params = await searchParams;
  const searchQuery = params.q || '';

  // Ambil data penyewaan dari database
  const rentals = await prisma.rental.findMany({
    where: {
      OR: [
        { product: { name: { contains: searchQuery, mode: 'insensitive' } } },
        { address: { contains: searchQuery, mode: 'insensitive' } },
        { notes: { contains: searchQuery, mode: 'insensitive' } }
      ]
    },
    include: {
      product: true,
      user: {
        select: {
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    },
    orderBy: {
      startDate: 'desc'
    }
  });

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Daftar Penyewaan</h1>
        <div className="flex items-center gap-2">
          <form className="relative" action="/admin/rentals">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              name="q"
              placeholder="Cari penyewaan..."
              className="pl-9 w-[250px]"
              defaultValue={searchQuery}
            />
          </form>
          <Link href="/admin/operations">
            <Button variant="outline" className="dark:border-gray-700 dark:text-gray-200 dark:hover:bg-gray-800">Lihat Operasi</Button>
          </Link>
        </div>
      </div>

      <RentalsTable
        rentals={rentals}
        searchQuery={searchQuery}
      />
    </div>
  );
}
