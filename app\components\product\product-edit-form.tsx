"use client";

import { useState } from "react";
import { updateProduct } from "@/lib/actions/product";
import { Product } from "@/lib/types/product";
import UploadForm from '../shared/upload-form';
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { ProductFormFields } from "./product-form-fields";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Alert } from "@/app/components/ui/alert";
import { LoadingSpinner } from "@/app/components/ui/loading-spinner";
import {
  LuSave,
  LuRefreshCw,
  LuCheck,
  LuImage,
  LuX,
  LuArrowLeft
} from "react-icons/lu";

interface ProductEditFormProps {
  product: Product;
  onCancel?: () => void;
}

export function ProductEditForm({ product, onCancel }: ProductEditFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(product.imageUrl);
  const [isFormValid, setIsFormValid] = useState(true); // Start as valid for existing product
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const router = useRouter();

  const handleFormSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Reset previous states
    setSaveSuccess(false);
    setFormErrors({});

    // Validate form before submission
    if (!isFormValid) {
      toast.error('Mohon perbaiki kesalahan pada form');
      return;
    }

    setIsLoading(true);

    try {
      const formData = new FormData(event.currentTarget);
      if (imageUrl) {
        formData.append('imageUrl', imageUrl);
      }

      await updateProduct(product.id, formData);
      setSaveSuccess(true);
      toast.success('Produk berhasil diupdate!');

      // Redirect ke halaman admin products setelah berhasil update
      setTimeout(() => {
        router.push('/admin/products');
      }, 1500);

    } catch (error) {
      console.error('Error updating product:', error);
      const errorMessage = error instanceof Error ? error.message : 'Terjadi kesalahan saat menyimpan produk';

      // Set form errors if available
      if (error instanceof Error && error.message.includes('validation')) {
        setFormErrors({ general: errorMessage });
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form validation changes
  const handleValidationChange = (isValid: boolean) => {
    setIsFormValid(isValid);
  };

  // Handle cancel action
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };





  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Form Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Edit Produk: {product.name}
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Perbarui informasi produk genset
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <LuArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
        </div>
      </div>

      {/* General Error Alert */}
      {formErrors.general && (
        <Alert className="border-red-200 bg-red-50 dark:bg-red-950/50">
          <LuX className="h-4 w-4 text-red-600" />
          <p className="text-sm text-red-800 dark:text-red-200">
            {formErrors.general}
          </p>
        </Alert>
      )}

      <form onSubmit={handleFormSubmit} className="space-y-6">
        {/* Image Upload Section */}
        <Card className="border bg-white dark:bg-gray-950 shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-white">
              <LuImage className="h-5 w-5 text-blue-500" />
              Gambar Produk
            </CardTitle>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Update gambar produk untuk menarik perhatian pelanggan
            </p>
          </CardHeader>
          <CardContent>
            <UploadForm
              onImageUploaded={(url) => setImageUrl(url)}
              currentImageUrl={imageUrl}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Format yang didukung: JPG, PNG, WebP. Maksimal 5MB. Rasio 16:9 direkomendasikan.
            </p>
          </CardContent>
        </Card>

        {/* Product Information Section */}
        <ProductFormFields
          product={product}
          errors={formErrors}
          onValidationChange={handleValidationChange}
        />
        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center justify-center gap-2 h-12 text-base"
            disabled={isLoading}
          >
            <LuArrowLeft className="h-4 w-4" />
            Batalkan
          </Button>

          <Button
            type="submit"
            disabled={isLoading || !isFormValid}
            className={`flex items-center justify-center gap-2 h-12 text-base flex-1 ${saveSuccess
              ? 'bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800'
              : 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
              } transition-colors duration-200`}
          >
            {isLoading ? (
              <>
                <LuRefreshCw className="h-4 w-4 animate-spin" />
                Menyimpan...
              </>
            ) : saveSuccess ? (
              <>
                <LuCheck className="h-4 w-4" />
                Tersimpan!
              </>
            ) : (
              <>
                <LuSave className="h-4 w-4" />
                Simpan Perubahan
              </>
            )}
          </Button>
        </div>
      </form>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl">
            <div className="flex items-center gap-3">
              <LoadingSpinner size="sm" />
              <p className="text-sm font-medium">
                Mengupdate produk...
              </p>
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
