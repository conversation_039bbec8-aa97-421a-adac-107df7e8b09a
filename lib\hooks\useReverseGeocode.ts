import { useState, useCallback } from 'react';

export function useReverseGeocode() {
  const [address, setAddress] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fungsi untuk mendapatkan alamat berdasarkan koordinat (reverse geocoding) dengan sistem fallback
  const getAddressFromCoordinates = useCallback(async (lat: number, lng: number) => {
    setIsLoading(true);
    setError(null);

    try {
      // Coba gunakan Nominatim dulu yang lebih detail melalui proxy API
      const nominatimResponse = await fetch(
        `/api/geocode/nominatim?lat=${lat}&lon=${lng}`
      ).catch(error => {
        console.error("Error fetching from Nominatim:", error);
        return null;
      });

      if (nominatimResponse && nominatimResponse.ok) {
        const nominatimData = await nominatimResponse.json();
        if (nominatimData && nominatimData.display_name) {
          // Format alamat Nominatim untuk Indonesia
          let fullAddress = nominatimData.display_name;

          // Jika ada address details, format ulang untuk Indonesia
          if (nominatimData.address) {
            const addr = nominatimData.address;
            const addressParts = [];

            // Format: Jalan/Nama Tempat, Kelurahan/Desa, Kecamatan, Kabupaten/Kota, Provinsi, Kode Pos
            if (addr.road || addr.street) {
              addressParts.push(addr.road || addr.street);
            } else if (addr.amenity || addr.building || addr.shop) {
              addressParts.push(addr.amenity || addr.building || addr.shop);
            }

            if (addr.village || addr.suburb || addr.neighbourhood) {
              addressParts.push(addr.village || addr.suburb || addr.neighbourhood);
            }

            if (addr.subdistrict || addr.town) {
              addressParts.push(addr.subdistrict || addr.town);
            }

            if (addr.city || addr.county) {
              addressParts.push(addr.city || addr.county);
            }

            if (addr.state || addr.province) {
              addressParts.push(addr.state || addr.province);
            } else {
              addressParts.push('Nusa Tenggara Barat');
            }

            if (addr.postcode) {
              addressParts.push(addr.postcode);
            }

            if (addressParts.length > 0) {
              fullAddress = addressParts.join(', ');
            }
          }

          // Pastikan NTB disebutkan
          if (!fullAddress.toLowerCase().includes('ntb') && !fullAddress.toLowerCase().includes('nusa tenggara barat')) {
            fullAddress += ', Nusa Tenggara Barat';
          }

          setAddress(fullAddress);
          setIsLoading(false);
          console.log("Alamat dari Nominatim:", fullAddress);
          return fullAddress;
        }
      }

      // Fallback ke Photon jika Nominatim gagal
      const response = await fetch(
        `/api/geocode/photon?lat=${lat}&lon=${lng}`
      ).catch(error => {
        console.error("Error fetching from Photon:", error);
        return null;
      });

      if (response && response.ok) {
        const data = await response.json();

        if (data && data.features && data.features.length > 0) {
          const result = data.features[0];
          // Membangun alamat lengkap dari properti yang tersedia dengan format Indonesia
          const addressParts = [];

          // Format: Nomor + Jalan/Nama Tempat, Kelurahan/Desa, Kecamatan, Kabupaten/Kota, Provinsi
          let streetPart = '';
          if (result.properties.housenumber && result.properties.street) {
            streetPart = `${result.properties.housenumber} ${result.properties.street}`;
          } else if (result.properties.street) {
            streetPart = result.properties.street;
          } else if (result.properties.name) {
            streetPart = result.properties.name;
          }

          if (streetPart) {
            addressParts.push(streetPart);
          }

          // Kelurahan/Desa
          if (result.properties.district || result.properties.suburb) {
            addressParts.push(result.properties.district || result.properties.suburb);
          }

          // Kecamatan
          if (result.properties.county && result.properties.county !== result.properties.city) {
            addressParts.push(result.properties.county);
          }

          // Kabupaten/Kota
          if (result.properties.city) {
            addressParts.push(result.properties.city);
          }

          // Provinsi
          if (result.properties.state) {
            addressParts.push(result.properties.state);
          } else {
            addressParts.push('Nusa Tenggara Barat');
          }

          // Kode pos jika ada
          if (result.properties.postcode) {
            addressParts.push(result.properties.postcode);
          }

          // Pastikan NTB disebutkan
          if (!addressParts.some(part =>
            part.toLowerCase().includes('ntb') ||
            part.toLowerCase().includes('nusa tenggara barat')
          )) {
            addressParts.push('Nusa Tenggara Barat');
          }

          // Format alamat final
          const fullAddress = addressParts.join(', ');
          setAddress(fullAddress);
          setIsLoading(false);
          console.log("Alamat dari Photon:", fullAddress);
          return fullAddress;
        }
      }

      // Jika kedua API gagal, gunakan alamat default berdasarkan koordinat
      const fallbackAddress = `Lokasi di titik koordinat ${lat.toFixed(6)}, ${lng.toFixed(6)}, Nusa Tenggara Barat, Indonesia`;
      setAddress(fallbackAddress);
      setIsLoading(false);
      console.log("Menggunakan alamat fallback:", fallbackAddress);
      return fallbackAddress;
    } catch (error) {
      console.error("Error melakukan reverse geocoding:", error);
      const fallbackAddress = `Titik koordinat ${lat.toFixed(6)}, ${lng.toFixed(6)}, Nusa Tenggara Barat, Indonesia`;
      setAddress(fallbackAddress);
      setError(`Gagal mendapatkan alamat: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
      setIsLoading(false);
      return fallbackAddress;
    }
  }, []);

  return {
    address,
    isLoading,
    error,
    getAddressFromCoordinates,
    setAddress
  };
}

export default useReverseGeocode;
