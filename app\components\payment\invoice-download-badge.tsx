"use client";

import { Badge } from "@/app/components/ui/badge";
import { LuDownload } from "react-icons/lu";
import { useToast } from "@/lib/hooks/use-toast";
import { useAppSounds } from "@/lib/hooks/use-app-sounds";

interface InvoiceDownloadBadgeProps {
  userId: string;
}

export function InvoiceDownloadBadge({ userId }: InvoiceDownloadBadgeProps) {
  const { showSuccess, showError, showInfo } = useToast();
  const { sounds } = useAppSounds();

  const handleDownloadAll = async () => {
    try {
      sounds.onButtonClick(); // Play click sound
      showInfo("Mengunduh Semua Invoice. Proses pengunduhan dimulai, harap tunggu...");

      // Ambil semua ID rental dari user
      const response = await fetch(`/api/payments/user/${userId}`);
      if (!response.ok) {
        throw new Error("Gagal mengambil data pembayaran");
      }

      const payments = await response.json();
      if (!payments || payments.length === 0) {
        showError("Tidak Ada Invoice. Tidak ada invoice yang tersedia untuk diunduh.");
        return;
      }

      // Download satu per satu dengan jeda waktu
      for (let i = 0; i < Math.min(payments.length, 5); i++) {
        const payment = payments[i];
        if (payment.rentalId) {
          // Buka di tab baru
          window.open(`/api/invoices/${payment.rentalId}/pdf`, '_blank');

          // Jeda 500ms antara setiap download
          if (i < payments.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      sounds.onSuccess(); // Play success sound
      showSuccess(`Unduhan Selesai. ${Math.min(payments.length, 5)} invoice telah dibuka di tab baru.`);

    } catch (error) {
      console.error("Error downloading all invoices:", error);
      sounds.onError(); // Play error sound
      showError("Gagal Mengunduh. Terjadi kesalahan saat mengunduh invoice. Silakan coba lagi nanti.");
    }
  };

  return (
    <Badge
      className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 cursor-pointer"
      onClick={handleDownloadAll}
    >
      <LuDownload className="mr-1 h-3 w-3" /> Download Semua Invoice
    </Badge>
  );
}
