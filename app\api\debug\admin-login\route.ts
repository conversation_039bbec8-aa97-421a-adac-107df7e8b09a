import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";

export async function GET() {
  try {
    const session = await getSession();
    
    console.log("=== ADMIN LOGIN DEBUG ===");
    console.log("Session:", session);
    
    if (!session?.user?.id) {
      console.log("❌ No session found");
      return NextResponse.json({ 
        error: "No session",
        step: "no_session",
        debug: {
          hasSession: !!session,
          hasUser: !!session?.user,
          hasUserId: !!session?.user?.id
        }
      }, { status: 401 });
    }

    console.log("✅ Session found:", {
      userId: session.user.id,
      userRole: session.user.role,
      userEmail: session.user.email,
      userName: session.user.name
    });

    // Check user in database
    const dbUser = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log("Database user:", dbUser);

    // Check account table
    const dbAccount = await prisma.account.findFirst({
      where: { 
        userId: session.user.id,
        providerId: "credential"
      },
      select: {
        id: true,
        providerId: true,
        userId: true,
        type: true,
        createdAt: true
      }
    });

    console.log("Database account:", dbAccount);

    const roleChecks = {
      sessionRole: session.user.role,
      sessionRoleType: typeof session.user.role,
      isAdminUppercase: session.user.role === "ADMIN",
      isAdminLowercase: session.user.role?.toString().toLowerCase() === "admin",
      isAdminMixed: session.user.role?.toLowerCase() === "admin",
      dbUserRole: dbUser?.role,
      dbUserRoleType: typeof dbUser?.role,
      isDbAdminUppercase: dbUser?.role === "ADMIN",
      isDbAdminLowercase: dbUser?.role?.toString().toLowerCase() === "admin",
    };

    console.log("Role checks:", roleChecks);

    return NextResponse.json({
      success: true,
      session: {
        userId: session.user.id,
        userEmail: session.user.email,
        userName: session.user.name,
        userRole: session.user.role,
      },
      database: {
        user: dbUser,
        account: dbAccount
      },
      roleChecks,
      authLayout: {
        shouldRedirectToAdmin: session.user.role === "ADMIN",
        shouldRedirectToUser: session.user.role !== "ADMIN",
        redirectTarget: session.user.role === "ADMIN" ? "/admin/dashboard" : "/user/dashboard"
      },
      adminProfile: {
        passesCheck: session.user.role === 'ADMIN', // uppercase check in admin profile
        failsCheck: session.user.role !== 'ADMIN'
      },
      adminDashboard: {
        passesCheck: session.user.role === "ADMIN", // uppercase check in admin dashboard
        failsCheck: session.user.role !== "ADMIN"
      }
    });
  } catch (error) {
    console.error("Debug admin login error:", error);
    return NextResponse.json({ 
      error: "Internal error",
      message: (error as Error).message,
      stack: (error as Error).stack
    }, { status: 500 });
  }
}
