"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Download, Mail, MessageCircle } from "lucide-react";
import { cn } from "@/lib/utils/cn";

interface InvoiceActionsProps {
  invoiceId: string;
  customerPhone?: string;
  customerEmail?: string;
  className?: string;
}

export function InvoiceActions({
  invoiceId,
  customerPhone,
  customerEmail,
  className
}: InvoiceActionsProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [isSendingWhatsApp, setIsSendingWhatsApp] = useState(false);

  const handleDownloadPDF = async () => {
    setIsDownloading(true);
    try {
      // Open the PDF in a new window for printing/saving
      const pdfUrl = `/api/admin/invoices/${invoiceId}/pdf`;
      const newWindow = window.open(pdfUrl, '_blank');

      if (newWindow) {
        // Wait a bit for the page to load, then trigger print dialog
        setTimeout(() => {
          newWindow.print();
        }, 1000);
        toast.success("PDF dibuka untuk diunduh/dicetak");
      } else {
        // Fallback: direct download
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = `invoice-${invoiceId.substring(0, 8)}.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("PDF berhasil diunduh");
      }
    } catch (error) {
      console.error('PDF generation error:', error);
      toast.error("Gagal mengunduh PDF");
    } finally {
      setIsDownloading(false);
    }
  };

  const handleSendEmail = async () => {
    if (!customerEmail) {
      toast.error("Email pelanggan tidak tersedia");
      return;
    }

    setIsSendingEmail(true);
    try {
      // Send email via API
      const response = await fetch(`/api/admin/invoices/${invoiceId}/email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send email');
      }

      const result = await response.json();
      toast.success(result.message || `Invoice berhasil dikirim ke ${customerEmail}`);
    } catch (error) {
      console.error('Email sending error:', error);
      toast.error(error instanceof Error ? error.message : "Gagal mengirim email");
    } finally {
      setIsSendingEmail(false);
    }
  };

  const handleSendWhatsApp = async () => {
    if (!customerPhone) {
      toast.error("Nomor WhatsApp pelanggan tidak tersedia");
      return;
    }

    setIsSendingWhatsApp(true);
    try {
      // Fetch invoice data to create a detailed message
      const response = await fetch(`/api/admin/invoices/${invoiceId}/pdf`);
      let invoiceDetails = '';

      if (response.ok) {
        // Create a detailed WhatsApp message with invoice information
        const invoiceNumber = `INV-${invoiceId.substring(0, 8)}`;
        const currentDate = new Date().toLocaleDateString('id-ID');

        invoiceDetails = `
🧾 *INVOICE RENTAL GENSET*

📋 *Detail Invoice:*
• Nomor: ${invoiceNumber}
• Tanggal: ${currentDate}

💰 *Informasi Pembayaran:*
• Mohon lakukan pembayaran deposit 50% terlebih dahulu
• Sisa pembayaran dilakukan setelah operasi selesai

🏦 *Rekening Pembayaran:*
• Bank: BCA
• No. Rekening: **********
• Atas Nama: PT Rental Genset

📄 *Lihat Invoice Lengkap:*
${window.location.origin}/admin/payments/${invoiceId}

📞 *Hubungi Kami:*
• Telepon: 021-1234567
• Email: <EMAIL>

Terima kasih telah menggunakan layanan Rental Genset! 🙏
        `.trim();
      } else {
        // Fallback message if invoice data fetch fails
        invoiceDetails = `
🧾 *INVOICE RENTAL GENSET*

Halo! Invoice pembayaran Anda telah siap.

📄 Lihat detail lengkap di: ${window.location.origin}/admin/payments/${invoiceId}

📞 Hubungi kami jika ada pertanyaan:
• Telepon: 021-1234567
• Email: <EMAIL>

Terima kasih! 🙏
        `.trim();
      }

      const message = encodeURIComponent(invoiceDetails);
      const cleanPhone = customerPhone.replace(/\D/g, '');
      const whatsappUrl = `https://wa.me/${cleanPhone}?text=${message}`;

      // Open WhatsApp with the message
      window.open(whatsappUrl, '_blank');
      toast.success("Membuka WhatsApp dengan pesan invoice...");
    } catch (error) {
      console.error('WhatsApp sending error:', error);
      toast.error("Gagal membuka WhatsApp");
    } finally {
      setIsSendingWhatsApp(false);
    }
  };

  return (
    <div className={cn("flex gap-3", className)}>
      <Button
        onClick={handleDownloadPDF}
        disabled={isDownloading}
        className={cn(
          "flex-1 min-h-[44px] bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white font-medium",
          isDownloading && "opacity-50 cursor-not-allowed"
        )}
      >
        <Download className="h-4 w-4 mr-2" />
        {isDownloading ? "Mengunduh..." : "Unduh PDF"}
      </Button>

      <Button
        variant="outline"
        onClick={handleSendEmail}
        disabled={isSendingEmail || !customerEmail}
        className={cn(
          "flex-1 min-h-[44px] border-border hover:bg-accent hover:text-accent-foreground font-medium",
          (isSendingEmail || !customerEmail) && "opacity-50 cursor-not-allowed"
        )}
      >
        <Mail className="h-4 w-4 mr-2" />
        {isSendingEmail ? "Mengirim..." : "Kirim Email"}
      </Button>

      <Button
        variant="outline"
        onClick={handleSendWhatsApp}
        disabled={isSendingWhatsApp || !customerPhone}
        className={cn(
          "flex-1 min-h-[44px] border-border hover:bg-green-50 hover:text-green-700 dark:hover:bg-green-900/20 dark:hover:text-green-400 font-medium",
          (isSendingWhatsApp || !customerPhone) && "opacity-50 cursor-not-allowed"
        )}
      >
        <MessageCircle className="h-4 w-4 mr-2" />
        {isSendingWhatsApp ? "Mengirim..." : "Kirim WhatsApp"}
      </Button>
    </div>
  );
}
