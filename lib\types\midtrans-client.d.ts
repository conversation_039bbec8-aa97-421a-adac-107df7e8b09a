declare module 'midtrans-client' {
  interface SnapOptions {
    isProduction: boolean;
    serverKey: string;
    clientKey: string;
  }

  interface TransactionOptions {
    transaction_details: {
      order_id: string;
      gross_amount: number;
    };
    credit_card?: {
      secure?: boolean;
    };
    enabled_payments?: string[];
    customer_details?: {
      first_name?: string;
      email?: string;
    };
    item_details?: Array<{
      id: string;
      price: number;
      quantity: number;
      name: string;
    }>;
    callbacks?: {
      finish?: string;
      error?: string;
      pending?: string;
    };
  }

  class Snap {
    constructor(options: SnapOptions);
    createTransaction(params: TransactionOptions): Promise<{token: string}>;
  }

  export { Snap };
}

// Midtrans Snap Frontend Interface
interface SnapInterface {
  pay: (token: string, options?: {
    onSuccess?: (result: SnapTransactionResult) => void;
    onPending?: (result: SnapTransactionResult) => void;
    onError?: (result: SnapTransactionResult) => void;
    onClose?: () => void;
  }) => void;
}

interface SnapTransactionResult {
  transaction_id: string;
  order_id: string;
  gross_amount: string;
  payment_type: string;
  transaction_time: string;
  transaction_status: string;
  status_code: string;
  status_message: string;
}

declare global {
  interface Window {
    snap?: SnapInterface;
  }
}
