"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { <PERSON><PERSON>n<PERSON>, LuTriangle } from "react-icons/lu";
import Link from "next/link";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: Date;
}

export function DashboardNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        const response = await fetch("/api/notifications", {
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          setNotifications(data.items || []);
          setLoading(false);
        }
      } catch (error) {
        console.error("Error fetching notifications:", error);
        setLoading(false);
      }
    };

    fetchNotifications();
  }, []);

  const unreadNotifications = notifications.filter((n) => !n.isRead);
  
  // Kategorikan notifikasi berdasarkan jenisnya
  const rentalNotifications = unreadNotifications.filter(
    (n) => n.type === "new_rental" || n.type === "rental_confirmed"
  );
  
  const paymentNotifications = unreadNotifications.filter(
    (n) => n.type === "new_payment" || n.type === "new_invoice"
  );
  
  const stockNotifications = unreadNotifications.filter(
    (n) => n.type === "low_stock"
  );

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Notifikasi Penting
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <p className="text-gray-500">Memuat notifikasi...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (unreadNotifications.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Notifikasi Penting
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32 bg-gray-50 rounded-md">
            <p className="text-gray-500">Tidak ada notifikasi baru</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Notifikasi Penting</span>
          <Badge className="ml-2">{unreadNotifications.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {stockNotifications.length > 0 && (
            <div className="bg-red-50 p-3 rounded-md border border-red-100">
              <h3 className="font-medium text-red-700 flex items-center">
                <LuTriangle className="mr-2" />
                Peringatan Stok Rendah
              </h3>
              <p className="text-sm text-red-600 mt-1">
                {stockNotifications.length} produk dengan stok hampir habis
              </p>
              <Link href="/admin/products">
                <span className="text-xs text-red-700 underline mt-2 inline-block">
                  Lihat Produk
                </span>
              </Link>
            </div>
          )}

          {rentalNotifications.length > 0 && (
            <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
              <h3 className="font-medium text-blue-700 flex items-center">
                <LuInfo className="mr-2" />
                Penyewaan Baru
              </h3>
              <p className="text-sm text-blue-600 mt-1">
                {rentalNotifications.length} penyewaan baru memerlukan perhatian
              </p>
              <Link href="/admin/rentals">
                <span className="text-xs text-blue-700 underline mt-2 inline-block">
                  Lihat Penyewaan
                </span>
              </Link>
            </div>
          )}

          {paymentNotifications.length > 0 && (
            <div className="bg-green-50 p-3 rounded-md border border-green-100">
              <h3 className="font-medium text-green-700 flex items-center">
                <LuInfo className="mr-2" />
                Pembayaran Baru
              </h3>
              <p className="text-sm text-green-600 mt-1">
                {paymentNotifications.length} pembayaran baru telah diterima
              </p>
              <Link href="/admin/payments">
                <span className="text-xs text-green-700 underline mt-2 inline-block">
                  Lihat Pembayaran
                </span>
              </Link>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
