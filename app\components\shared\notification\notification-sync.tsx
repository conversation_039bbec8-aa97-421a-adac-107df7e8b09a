"use client";

import { useEffect } from "react";
import { useNotificationSync } from "@/lib/hooks/use-notification-sync";

/**
 * Komponen untuk sinkronisasi notifikasi real-time
 * Tidak merender apapun, hanya mengelola sinkronisasi
 */
export function NotificationSync() {
  const { refreshNotifications } = useNotificationSync();

  useEffect(() => {
    // Refresh saat komponen mount
    refreshNotifications();

    // Setup interval untuk refresh berkala
    const interval = setInterval(() => {
      refreshNotifications();
    }, 60000); // Refresh setiap 1 menit

    // Setup event listener untuk storage changes (untuk sync antar tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'notification_update') {
        refreshNotifications();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [refreshNotifications]);

  // Trigger storage event untuk sync antar tab
  useEffect(() => {
    const triggerSync = () => {
      localStorage.setItem('notification_update', Date.now().toString());
    };

    // Listen untuk custom events
    const handleNotificationUpdate = () => {
      triggerSync();
      refreshNotifications();
    };

    window.addEventListener('notification-updated', handleNotificationUpdate);

    return () => {
      window.removeEventListener('notification-updated', handleNotificationUpdate);
    };
  }, [refreshNotifications]);

  return null; // Komponen ini tidak merender apapun
}

/**
 * Utility function untuk trigger update notifikasi
 */
export function triggerNotificationUpdate() {
  // Dispatch custom event
  window.dispatchEvent(new CustomEvent('notification-updated'));
}

/**
 * Utility function untuk trigger update dari server actions
 */
export function triggerNotificationUpdateFromServer() {
  if (typeof window !== 'undefined') {
    // Set localStorage untuk trigger cross-tab sync
    localStorage.setItem('notification_update', Date.now().toString());
    // Dispatch custom event untuk current tab
    window.dispatchEvent(new CustomEvent('notification-updated'));
  }
}

/**
 * Hook untuk trigger update notifikasi dari komponen
 */
export function useNotificationTrigger() {
  return {
    triggerUpdate: triggerNotificationUpdate,
    triggerServerUpdate: triggerNotificationUpdateFromServer
  };
}
