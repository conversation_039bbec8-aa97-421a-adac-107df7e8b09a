import { z } from "zod";
import { type RentalDuration } from "@/lib/utils/constants";
import { getDurationOptions } from "@/lib/utils/duration";

/**
 * Schema validasi untuk pembuatan rental baru
 * Menggunakan Zod 4.x dengan fitur terbaru
 */
export const createRentalSchema = (maxStock: number) => z.object({
  productId: z.string({
    required_error: "ID Produk harus diisi",
    invalid_type_error: "ID Produk harus berupa string",
  }).min(1, "ID Produk harus diisi"),
  
  startDate: z.string({
    required_error: "Tanggal mulai harus diisi",
  })
    .min(1, "Tanggal mulai harus diisi")
    .refine(date => new Date(date) >= new Date(), {
      message: "Tanggal mulai tidak boleh di masa lalu"
    }),
  
  duration: z.enum(getDurationOptions().map(option => option.value) as [RentalDuration, ...RentalDuration[]], {
    required_error: "Pilih durasi sewa",
    invalid_type_error: "Durasi tidak valid"
  }),
  
  arrivalTime: z.string({
    required_error: "Waktu kedatangan harus diisi",
  })
    .min(1, "Waktu kedatangan harus diisi")
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
      message: "Format waktu tidak valid (HH:MM)"
    }),
  
  address: z.string({
    required_error: "Alamat harus diisi",
  })
    .min(10, "Alamat terlalu singkat (minimal 10 karakter)")
    .max(200, "Alamat terlalu panjang (maksimal 200 karakter)")
    .transform(val => val.trim().replace(/\s+/g, ' ')),
  
  purpose: z.string({
    required_error: "Tujuan harus diisi",
  })
    .min(10, "Tujuan terlalu singkat (minimal 10 karakter)")
    .max(500, "Tujuan terlalu panjang (maksimal 500 karakter)"),
  
  quantity: z.coerce.number()
    .int("Jumlah unit harus berupa bilangan bulat")
    .min(1, "Jumlah unit minimal 1")
    .max(maxStock, `Jumlah unit maksimal ${maxStock}`)
});

/**
 * Schema untuk validasi alamat standalone
 * Digunakan untuk memvalidasi input alamat secara terpisah
 */
export const addressSchema = z.string({
  required_error: "Alamat harus diisi",
})
  .min(10, "Alamat terlalu singkat (minimal 10 karakter)")
  .max(200, "Alamat terlalu panjang (maksimal 200 karakter)")
  .transform(val => {
    // Normalisasi alamat: hapus whitespace berlebih
    const trimmed = val.trim().replace(/\s+/g, ' ');
    
    // Tambahkan ", Indonesia" jika masih kurang dari 10 karakter
    return trimmed.length < 10 ? trimmed + ", Indonesia" : trimmed;
  });

/**
 * Type untuk data rental yang divalidasi
 */
export type CreateRentalInput = z.infer<ReturnType<typeof createRentalSchema>>;

// Membuat objek schemas untuk ekspor
const rentalSchemas = { createRentalSchema, addressSchema };
export default rentalSchemas; 
