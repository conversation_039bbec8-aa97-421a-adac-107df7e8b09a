"use client";

/**
 * Utility function to clear all cookies and perform a complete logout
 */
export function clearAllCookies() {
  // Get all cookies
  const cookies = document.cookie.split(";");
  
  // Clear each cookie with different path and domain combinations
  cookies.forEach((cookie) => {
    const eqPos = cookie.indexOf("=");
    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
    
    if (name) {
      // Clear cookie for current path
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      
      // Clear cookie for root domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
      
      // Clear cookie for parent domain (if subdomain)
      const hostParts = window.location.hostname.split('.');
      if (hostParts.length > 2) {
        const parentDomain = hostParts.slice(-2).join('.');
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${parentDomain}`;
      }
      
      // Clear cookie without domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      
      // Clear cookie for different paths
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/api`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/auth`;
    }
  });
}

/**
 * Perform a complete logout with cookie clearing and redirect
 */
export function performCompleteLogout() {
  try {
    // Clear all cookies
    clearAllCookies();
    
    // Clear localStorage
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.clear();
    }
    
    // Clear sessionStorage
    if (typeof window !== 'undefined' && window.sessionStorage) {
      sessionStorage.clear();
    }
    
    // Force redirect to login page
    window.location.replace("/login");
  } catch (error) {
    console.error("Error during complete logout:", error);
    // Force redirect even if clearing fails
    window.location.replace("/login");
  }
}

/**
 * Enhanced logout function for Better Auth
 */
export async function enhancedLogout(signOutFunction: any) {
  try {
    // Try Better Auth signOut first
    await signOutFunction({
      fetchOptions: {
        onSuccess: () => {
          performCompleteLogout();
        },
        onError: () => {
          // If Better Auth signOut fails, still perform complete logout
          performCompleteLogout();
        }
      }
    });
  } catch (error) {
    console.error("Enhanced logout error:", error);
    // Force logout even if Better Auth signOut fails
    performCompleteLogout();
  }
}
