import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Dapatkan sesi pengguna
    const session = await getSession();
    
    // Jika tidak ada sesi, kembalikan error unauthorized
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Dapatkan user dari database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { image: true }
    });

    // Jika user tidak ditemukan, kembalikan error not found
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Kembalikan URL gambar atau default avatar
    const avatarUrl = user.image || `https://ui-avatars.com/api/?name=${encodeURIComponent(session.user.name || session.user.email || 'User')}&background=6366f1&color=fff&size=128`;

    return NextResponse.json({
      image: avatarUrl,
      hasCustomImage: !!user.image
    });
  } catch (error) {
    console.error("Error fetching user avatar:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 
