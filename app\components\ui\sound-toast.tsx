"use client";

import { useAppSounds } from "@/lib/hooks/use-app-sounds";
import { useEffect } from "react";
import { toast } from "sonner";

type ToastType = "success" | "error" | "info" | "warning";

interface SoundToastOptions {
  title?: string;
  description?: string;
  duration?: number;
  playSound?: boolean;
}

// Hook untuk toast dengan suara
export function useSoundToast() {
  const { sounds } = useAppSounds();

  const showToast = (type: ToastType, message: string, options: SoundToastOptions = {}) => {
    const { title, description, duration = 4000, playSound = true } = options;

    // Play sound based on type
    if (playSound) {
      switch (type) {
        case "success":
          sounds.onSuccess();
          break;
        case "error":
          sounds.onError();
          break;
        case "warning":
          sounds.onAlert();
          break;
        case "info":
          sounds.onNotification();
          break;
      }
    }

    // Show toast
    switch (type) {
      case "success":
        toast.success(title || message, {
          description,
          duration,
        });
        break;
      case "error":
        toast.error(title || message, {
          description,
          duration,
        });
        break;
      case "warning":
        toast.warning(title || message, {
          description,
          duration,
        });
        break;
      case "info":
        toast.info(title || message, {
          description,
          duration,
        });
        break;
    }
  };

  return {
    success: (message: string, options?: SoundToastOptions) => showToast("success", message, options),
    error: (message: string, options?: SoundToastOptions) => showToast("error", message, options),
    warning: (message: string, options?: SoundToastOptions) => showToast("warning", message, options),
    info: (message: string, options?: SoundToastOptions) => showToast("info", message, options),
  };
}

// Komponen untuk auto-play sound pada mount
interface SoundNotificationProps {
  type: ToastType;
  autoPlay?: boolean;
  children?: React.ReactNode;
}

export function SoundNotification({ type, autoPlay = true, children }: SoundNotificationProps) {
  const { sounds } = useAppSounds();

  useEffect(() => {
    if (autoPlay) {
      switch (type) {
        case "success":
          sounds.onSuccess();
          break;
        case "error":
          sounds.onError();
          break;
        case "warning":
          sounds.onAlert();
          break;
        case "info":
          sounds.onNotification();
          break;
      }
    }
  }, [type, autoPlay, sounds]);

  return <>{children}</>;
}
