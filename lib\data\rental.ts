import { prisma } from "@/lib/config/prisma";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Rental } from "@/lib/types/rental";
import { Prisma } from "@prisma/client";

// Untuk sementara gunakan tipe yang lebih fleksibel
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ExtendedRental = Record<string, any>;

const rentalInclude = {
    user: {
        select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            role: true,
            createdAt: true,
            updatedAt: true
        }
    },
    product: {
        include: {
            user: true
        }
    },
    payment: true
};

type RentalWithRelations = Prisma.RentalGetPayload<{
    include: typeof rentalInclude;
}>;


function transformRental(rental: RentalWithRelations): ExtendedRental {
    const { payment, ...rentalWithoutPayment } = rental;
    return {
        ...rentalWithoutPayment,
        totalPrice: rental.amount,
        duration: Math.ceil((new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime()) / (1000 * 60 * 60 * 8)),
        product: {
            id: rental.product.id,
            name: rental.product.name,
            capacity: rental.product.capacity,
            description: rental.product.description || undefined
        },
        payment: payment ? {
            ...payment,
            userId: payment.userId,
            rental: {
                user: {
                    name: rental.user.name,
                    email: rental.user.email
                },
                product: {
                    id: rental.product.id,
                    name: rental.product.name,
                    image: rental.product.image,
                    price: rental.product.price,
                    capacity: rental.product.capacity
                }
            }
        } : null
    };
}

export async function getRentalHistory(productId: string): Promise<ExtendedRental[]> {
    try {
        const rentals = await prisma.rental.findMany({
            where: { productId },
            include: rentalInclude,
            orderBy: { startDate: "desc" },
        });
        return rentals.map(transformRental);
    } catch (error) {
        console.error("Error fetching rental history:", error);
        throw new Error("Gagal mengambil riwayat rental");
    }
}

export async function getRental(id: string) {
    return prisma.rental.findUnique({
        where: { id },
        include: {
            payment: true,
            product: {
                select: {
                    id: true,
                    name: true,
                    price: true,
                    capacity: true,
                    description: true,
                    imageUrl: true,
                    image: true,
                    status: true,
                    user: true
                }
            },
            user: {
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true
                }
            }
        }
    });
}

export async function getUserRentals(userId: string): Promise<{
    id: string;
    userId: string;
    productId: string;
    startDate: Date;
    endDate: Date;
    status: string;
    amount: number;
    location: string;
    address: string | null;
    purpose: string;
    arrivalTime: string;
    quantity: number;
    duration?: string | null;
    product: {
        id: string;
        name: string;
        price: number;
        imageUrl?: string | null;
        image?: string | null;
        capacity: number;
        description?: string | null;
    } & Record<string, unknown>;
    payment: ({
        id: string;
        status: string;
        amount: number;
    } & Record<string, unknown>) | null;
    createdAt: Date;
    updatedAt: Date;
}[]> {
    const MAX_RETRIES = 3;
    let retries = 0;
    
    while (retries < MAX_RETRIES) {
        try {
            // Gunakan query yang sederhana tanpa relasi kompleks
            await prisma.$connect(); // Memastikan koneksi telah dibuka sebelum query
            
            const results = await prisma.rental.findMany({
                where: { userId },
                include: {
                    product: true,
                    payment: true
                },
                orderBy: { startDate: "desc" },
            });
            
            return results;
        } catch (error) {
            retries++;
            console.error(`Error fetching user rentals (Attempt ${retries}/${MAX_RETRIES}):`, error);
            
            // Coba refresh koneksi jika terjadi error
            try {
                await prisma.$disconnect();
                // Tunggu sebentar sebelum mencoba lagi
                await new Promise(resolve => setTimeout(resolve, 1000 * retries));
            } catch (disconnectError) {
                console.error("Error disconnecting Prisma:", disconnectError);
            }
            
            // Jika sudah mencapai batas retry, lempar error
            if (retries >= MAX_RETRIES) {
                throw new Error("Gagal mengambil data rental: Koneksi ke database terputus");
            }
        }
    }
    
    // Kode ini seharusnya tidak pernah tercapai karena loop while di atas
    // baik akan return results atau throw error
    throw new Error("Gagal mengambil data rental");
}

export async function getRentalById(id: string): Promise<ExtendedRental | null> {
    try {
        // 1. Coba dapatkan rental tanpa include user dahulu
        const rental = await prisma.rental.findUnique({
            where: { id },
            include: {
                product: {
                    include: {
                        user: true
                    }
                },
                payment: true
            },
        });
        
        if (!rental) {
            return null;
        }
        
        // 2. Coba dapatkan user secara terpisah
        let userData = null;
        try {
            userData = await prisma.user.findUnique({
                where: { id: rental.userId },
                select: {
                    id: true,
                    name: true,
                    email: true,
                    phone: true,
                    role: true,
                    createdAt: true,
                    updatedAt: true
                }
            });
        } catch (userError) {
            console.log("User data not found, using default user", userError);
        }
        
        // 3. Jika user tidak ditemukan, gunakan data default
        const user = userData || {
            id: 'deleted-user',
            name: 'Pengguna Terhapus',
            email: '<EMAIL>',
            phone: '-',
            role: 'USER',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        // 4. Transformasi data dengan user yang aman
        const { payment, ...rentalWithoutPayment } = rental;
        return {
            ...rentalWithoutPayment,
            user: {
                id: user.id,
                name: user.name || 'User',
                email: user.email
            },
            totalPrice: rental.amount,
            duration: Math.ceil((new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime()) / (1000 * 60 * 60 * 8)),
            product: {
                id: rental.product.id,
                name: rental.product.name,
                capacity: rental.product.capacity,
                description: rental.product.description || undefined
            },
            payment: payment ? {
                ...payment,
                userId: payment.userId,
                rental: {
                    user: {
                        name: user.name || 'User',
                        email: user.email
                    },
                    product: {
                        id: rental.product.id,
                        name: rental.product.name,
                        image: rental.product.image,
                        price: rental.product.price,
                        capacity: rental.product.capacity
                    }
                }
            } : null
        };
    } catch (error) {
        console.error("Error fetching rental:", error);
        throw new Error("Gagal mengambil detail rental");
    }
}

export async function getRentals(query: string = ''): Promise<Prisma.RentalGetPayload<{
  include: {
    product: true;
    payment: true;
  }
}>[]> {
    try {
        // Get rentals without including user to avoid null reference errors
        const rentals = await prisma.rental.findMany({
            include: {
                product: true,
                payment: true,
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        
        // Jika ada query, filter hanya berdasarkan product name
        if (query && query.trim() !== '') {
            const lowerQuery = query.toLowerCase();
            return rentals.filter(rental => 
                rental.product?.name?.toLowerCase().includes(lowerQuery)
            );
        }
        
        return rentals;
    } catch (error) {
        console.error('Error getting rentals:', error);
        throw new Error("Gagal mengambil daftar rental");
    }
}
