"use server";

import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import * as bcrypt from "bcrypt-ts";
import { put } from "@vercel/blob";
import { PasswordUpdateSchema } from "@/lib/validations/user/schema";

// Schema untuk validasi update profil
const ProfileSchema = z.object({
  name: z.string().min(2, "Nama minimal 2 karakter"),
  email: z.string().email("Email tidak valid"),
  phone: z.string().min(9, "Nomor telepon tidak valid"),
  address: z.string().min(5, "Alamat terlalu pendek"),
  bio: z.string().optional(),
});



// Fungsi untuk menangani upload file menggunakan Vercel Blob Storage
async function handleFileUpload(file: File | null): Promise<string | null> {
  try {
    // Validasi file ada dan bukan null atau file kosong
    if (!file || file.size === 0 || !(file instanceof File)) {
      console.log("File tidak valid untuk upload");
      return null;
    }
    
    // Validasi file (ukuran, tipe, dll)
    if (file.size > 5 * 1024 * 1024) { // 5MB
      console.error("File terlalu besar (max 5MB)");
      return null;
    }
    
    // Validasi tipe file (hanya gambar)
    if (!file.type.startsWith("image/")) {
      console.error("Hanya file gambar yang diperbolehkan");
      return null;
    }
    
    // Buat nama file yang unik berdasarkan timestamp
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || "jpg";
    const fileName = `avatar-${timestamp}.${fileExtension}`;
    
    // Upload file ke Vercel Blob
    const { url } = await put(`profile-images/${fileName}`, file, {
      access: 'public',
      contentType: file.type,
    });
    
    return url;
  } catch (error) {
    console.error("Error uploading file to Vercel Blob:", error);
    return null;
  }
}

// Fungsi untuk menyimpan data profil tambahan di local storage
// Catatan: Ini hanya untuk demo, bukan pendekatan yang disarankan untuk produksi
// Harusnya disimpan sebagai field di database
async function saveProfileDataToSession(data: { address: string, bio: string }) {
  try {
    // Di sini kita bisa menyimpan data di cookies atau session
    // Tetapi fungsi ini perlu diimplementasikan di client-side
    
    // Untuk demo ini, kita hanya mencatat data dan mengandalkan state client
    console.log("Profile data for client storage:", data);
    return true;
  } catch (error) {
    console.error("Error saving profile data:", error);
    return false;
  }
}

export async function updateProfile(formData: FormData) {
  const session = await getSession();
  if (!session?.user?.id) {
    return { success: false, message: "Anda harus login terlebih dahulu" };
  }

  try {
    const userId = session.user.id;
    
    // Dapatkan data formulir
    const rawData = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      phone: formData.get("phone") as string,
      address: formData.get("address") as string,
      bio: formData.get("bio") as string,
    };

    // Validasi data
    const validatedData = ProfileSchema.safeParse(rawData);
    if (!validatedData.success) {
      return { 
        success: false, 
        message: "Data tidak valid", 
        errors: validatedData.error.errors 
      };
    }

    // Proses file avatar jika ada
    let imageUrl = null;
    
    try {
      const avatarFile = formData.get("avatar");
      // Pastikan avatarFile adalah instance File yang valid sebelum melakukan upload
      if (avatarFile && avatarFile instanceof File && avatarFile.size > 0) {
        imageUrl = await handleFileUpload(avatarFile);
        console.log("Image uploaded successfully, URL:", imageUrl);
      } else {
        console.log("No valid avatar file provided, skipping upload");
      }
    } catch (error) {
      console.error("Error handling avatar file:", error);
      // Lanjutkan proses meskipun upload avatar gagal
    }

    // Cek apakah user ada di database
    const userExists = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true }
    });
    
    if (!userExists) {
      console.log(`User dengan ID ${userId} tidak ditemukan`);
      return { success: false, message: "User tidak ditemukan" };
    }
    
    // Update data user yang ada di model (name, email, phone, image)
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: validatedData.data.name,
        email: validatedData.data.email,
        phone: validatedData.data.phone,
        ...(imageUrl && { image: imageUrl }), // Hanya update jika ada image baru
      },
    });
    
    if (!updatedUser) {
      return { success: false, message: "Gagal memperbarui profil, user tidak ditemukan" };
    }

    // Untuk data address dan bio yang tidak ada di model User,
    // simpan di localStorage client-side melalui session
    const profileExtendedData = {
      address: validatedData.data.address,
      bio: validatedData.data.bio || ""
    };

    // Nyimpan data tambahan
    await saveProfileDataToSession(profileExtendedData);

    // Force revalidate untuk memperbarui tampilan
    revalidatePath("/user/profile");
    revalidatePath("/");
    
    return { 
      success: true, 
      message: "Profil berhasil diperbarui",
      profileData: {
        name: updatedUser.name,
        email: updatedUser.email,
        phone: updatedUser.phone,
        ...profileExtendedData,
        imageUrl: imageUrl || updatedUser.image,
        ...(imageUrl && { avatarUrl: imageUrl })
      }
    };
  } catch (error) {
    console.error("Gagal memperbarui profil:", error);
    return { success: false, message: "Gagal memperbarui profil" };
  }
}

export async function updatePassword(formData: FormData) {
  const session = await getSession();
  if (!session?.user?.id) {
    return { success: false, message: "Anda harus login terlebih dahulu" };
  }

  try {
    console.log("Session user ID:", session.user.id);

    const rawData = {
      currentPassword: formData.get("currentPassword") as string,
      newPassword: formData.get("newPassword") as string,
      confirmPassword: formData.get("confirmPassword") as string,
    };

    console.log("Raw form data:", {
      currentPassword: rawData.currentPassword ? "***" : "empty",
      newPassword: rawData.newPassword ? "***" : "empty",
      confirmPassword: rawData.confirmPassword ? "***" : "empty"
    });

    // Validasi data menggunakan schema dari validations
    const validatedData = PasswordUpdateSchema.safeParse(rawData);
    if (!validatedData.success) {
      console.log("Validation errors:", validatedData.error.errors);
      return {
        success: false,
        message: "Data tidak valid",
        errors: validatedData.error.errors
      };
    }

    console.log("Data validation passed");

    // Cek user di database dengan lebih detail
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        password: true
      },
    });

    console.log("User found:", user ? { id: user.id, email: user.email, hasPassword: !!user.password } : "null");

    if (!user) {
      console.error("User not found in database with ID:", session.user.id);
      return { success: false, message: "User tidak ditemukan di database" };
    }

    // Cek password di tabel user atau account (Better Auth)
    let currentPassword = user.password;
    let updateTarget: 'user' | 'account' = 'user';

    if (!currentPassword) {
      console.log("No password in user table, checking account table...");

      // Cek di tabel account untuk Better Auth
      const account = await prisma.account.findFirst({
        where: {
          userId: session.user.id,
          providerId: "credential" // Better Auth menggunakan "credential" untuk email/password
        },
        select: {
          id: true,
          password: true
        },
      });

      console.log("Account found:", account ? { id: account.id, hasPassword: !!account.password } : "null");

      if (account?.password) {
        currentPassword = account.password;
        updateTarget = 'account';
      }
    }

    if (!currentPassword) {
      console.error("No password found in user or account table for user:", user.id);
      return { success: false, message: "Password tidak ditemukan untuk user ini. Silakan hubungi administrator." };
    }

    console.log("Using password from:", updateTarget, "table");
    console.log("Password hash length:", currentPassword.length);

    // Verifikasi password lama
    console.log("Verifying current password...");
    let isPasswordValid = false;

    try {
      // Cek apakah hash dalam format bcrypt yang benar (60 karakter, dimulai dengan $2)
      if (currentPassword.length === 60 && currentPassword.startsWith('$2')) {
        isPasswordValid = await bcrypt.compare(
          validatedData.data.currentPassword,
          currentPassword
        );
      } else {
        console.log("Password hash format tidak valid, panjang:", currentPassword.length);
        // Jika hash tidak dalam format bcrypt, coba verifikasi langsung (untuk testing)
        // HANYA UNTUK DEVELOPMENT - JANGAN GUNAKAN DI PRODUCTION
        if (process.env.NODE_ENV === 'development') {
          isPasswordValid = validatedData.data.currentPassword === currentPassword;
          console.log("Development mode: direct password comparison");
        }
      }
    } catch (error) {
      console.error("Error comparing password:", error);
      isPasswordValid = false;
    }

    console.log("Password verification result:", isPasswordValid);

    if (!isPasswordValid) {
      return {
        success: false,
        message: "Password saat ini tidak valid",
        errors: [{ path: ["currentPassword"], message: "Password saat ini salah" }]
      };
    }

    // Hash password baru
    console.log("Hashing new password...");
    const hashedPassword = await bcrypt.hash(validatedData.data.newPassword, 10);

    // Update password di database sesuai target
    console.log("Updating password in", updateTarget, "table...");

    if (updateTarget === 'user') {
      await prisma.user.update({
        where: { id: session.user.id },
        data: { password: hashedPassword },
      });
    } else {
      // Update di tabel account
      await prisma.account.updateMany({
        where: {
          userId: session.user.id,
          providerId: "credential"
        },
        data: { password: hashedPassword },
      });
    }

    console.log("Password updated successfully for user:", user.id);

    // Revalidasi halaman profil
    revalidatePath("/user/profile");
    return { success: true, message: "Password berhasil diperbarui" };
  } catch (error) {
    console.error("Error in updatePassword:", error);
    return { success: false, message: "Gagal memperbarui password: " + (error as Error).message };
  }
}
