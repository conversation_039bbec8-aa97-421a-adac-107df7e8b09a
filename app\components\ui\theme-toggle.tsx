"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "../ui/button"
import { cn } from "@/lib/utils"

export function ThemeToggle({ className }: { className?: string }) {
  const { theme, setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Hanya render komponen setelah mounted untuk menghindari hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  // Efek untuk memastikan tema diterapkan dengan benar
  React.useEffect(() => {
    if (mounted && typeof window !== 'undefined') {
      // Setel manual atribut dan kelas pada elemen HTML
      const currentTheme = resolvedTheme || theme;
      const htmlElement = document.documentElement;

      // Atur atribut data-theme
      htmlElement.setAttribute('data-theme', currentTheme || 'light');

      // Atur kelas tema untuk Tailwind
      if (currentTheme === 'dark') {
        htmlElement.classList.add('dark');
        htmlElement.classList.remove('light');
      } else {
        htmlElement.classList.add('light');
        htmlElement.classList.remove('dark');
      }

      // Log untuk debugging
      console.log('Current theme (useEffect):', currentTheme, {
        'data-theme': htmlElement.getAttribute('data-theme'),
        'classes': htmlElement.className
      });
    }
  }, [theme, resolvedTheme, mounted]);

  const toggleTheme = () => {
    const newTheme = (theme === "light" || resolvedTheme === "light") ? "dark" : "light"
    console.log("Mengganti tema dari", theme, "ke", newTheme)

    // Setel tema
    setTheme(newTheme)

    // Trigger custom event untuk debugging
    if (typeof window !== 'undefined') {
      // Manual update juga untuk memastikan
      const htmlElement = document.documentElement;
      htmlElement.setAttribute('data-theme', newTheme);

      if (newTheme === 'dark') {
        htmlElement.classList.add('dark');
        htmlElement.classList.remove('light');
      } else {
        htmlElement.classList.add('light');
        htmlElement.classList.remove('dark');
      }

      // Kirim event
      const event = new CustomEvent('themeChange', {
        detail: { prevTheme: theme, theme: newTheme }
      })
      document.dispatchEvent(event)
    }
  }

  if (!mounted) {
    return (
      <Button
        variant="outline"
        size="icon"
        className={cn("rounded-md border h-9 w-9", className)}
        disabled
      >
        <div className="h-4 w-4 bg-gray-200 rounded-full animate-pulse" />
        <span className="sr-only">Memuat tema</span>
      </Button>
    )
  }

  // Gunakan resolvedTheme untuk mendapatkan tema aktual yang diterapkan
  const currentTheme = resolvedTheme || theme;

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className={cn(
        "rounded-md h-9 w-9 relative text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800",
        className
      )}
      title={currentTheme === "light" ? "Aktifkan mode gelap" : "Aktifkan mode terang"}
    >
      {/* Ikon matahari */}
      <Sun
        className={cn(
          "h-[1.15rem] w-[1.15rem] transition-all",
          currentTheme === "dark" ? "opacity-0 scale-0 rotate-90" : "opacity-100 scale-100 rotate-0 text-amber-500"
        )}
      />

      {/* Ikon bulan */}
      <Moon
        className={cn(
          "absolute h-[1.15rem] w-[1.15rem] transition-all",
          currentTheme === "dark" ? "opacity-100 scale-100 rotate-0 text-blue-400" : "opacity-0 scale-0 rotate-90"
        )}
      />

      <span className="sr-only">Ubah tema</span>
    </Button>
  )
} 
