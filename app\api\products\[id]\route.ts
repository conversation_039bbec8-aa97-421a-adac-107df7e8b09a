import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { ProductStatus } from "@prisma/client";
import { revalidatePath } from "next/cache";
import { getSession } from "@/lib/auth/server";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    // Gunakan select untuk mengambil field yang diperlukan saja
    const product = await prisma.product.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        price: true,
        description: true,
        capacity: true,
        stock: true,
        status: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        imageUrl: true,
        category: true,
        overtimeRate: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!product) {
      return NextResponse.json({ error: "Produk tidak ditemukan" }, { status: 404 });
    }

    // Tambahkan header cache control
    return new NextResponse(JSON.stringify(product), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, s-maxage=10, stale-while-revalidate=59'
      }
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json({ error: "Terjadi kesalahan server" }, { status: 500 });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    const data = await request.json();

    if (!data) {
      return NextResponse.json({
        success: false,
        message: 'Data tidak ditemukan'
      }, { status: 400 });
    }

    const product = await prisma.product.update({
      where: { id },
      data: {
        name: data.name,
        price: parseFloat(data.price),
        capacity: parseInt(data.capacity),
        stock: parseInt(data.stock),
        description: data.description,
        status: data.status as ProductStatus,
        imageUrl: data.imageUrl,
        overtimeRate: parseFloat(data.overtimeRate) || 0,
      },
      select: {
        id: true,
        name: true,
        status: true,
        imageUrl: true,
        overtimeRate: true,
      }
    });

    // Revalidasi halaman terkait
    revalidatePath('/admin/products');
    revalidatePath('/user/catalog');

    return NextResponse.json({ success: true, product });
  } catch (error) {
    console.error('Error updating product:', error);

    if (error instanceof Error) {
      return NextResponse.json({
        success: false,
        message: 'Gagal memperbarui produk',
        error: error.message
      }, { status: 500 });
    }

    return NextResponse.json({
      success: false,
      message: 'Gagal memperbarui produk',
      error: 'Terjadi kesalahan yang tidak diketahui'
    }, { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();

    const updatedProduct = await prisma.product.update({
      where: { id },
      data: {
        ...body,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    return new NextResponse("Error updating product", { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id },
    });

    if (!product) {
      return NextResponse.json(
        { error: 'Produk tidak ditemukan' },
        { status: 404 }
      );
    }

    // Check if product has active rentals
    const activeRentals = await prisma.rental.count({
      where: {
        productId: id,
        status: {
          in: ['PENDING', 'CONFIRMED', 'ACTIVE']
        }
      }
    });

    if (activeRentals > 0) {
      return NextResponse.json(
        { error: 'Tidak dapat menghapus produk yang sedang disewa' },
        { status: 400 }
      );
    }

    // Check if product has related payments
    const relatedPayments = await prisma.payment.count({
      where: {
        rental: {
          productId: id
        }
      }
    });

    if (relatedPayments > 0) {
      return NextResponse.json(
        { error: 'Tidak dapat menghapus produk yang memiliki riwayat pembayaran' },
        { status: 400 }
      );
    }

    // Delete all related rentals first
    await prisma.rental.deleteMany({
      where: { productId: id }
    });

    // Now delete the product
    await prisma.product.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: "Produk berhasil dihapus"
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      { error: 'Gagal menghapus produk' },
      { status: 500 }
    );
  }
}
