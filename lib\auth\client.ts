"use client";

import { createAuthClient } from "better-auth/react";
import type { Session, User } from "./config";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
});

export const {
  signIn,
  signUp,
  signOut,
  useSession,
} = authClient;

// Google Sign In helper
export const signInWithGoogle = () => {
  return authClient.signIn.social({
    provider: "google",
    callbackURL: "/user/dashboard",
  });
};

export type { Session, User };
