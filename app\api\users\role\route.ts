import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { revalidatePath } from "next/cache";

export async function PUT(request: Request) {
    try {
        const session = await getSession();
        if (!session?.user || session.user.role !== "ADMIN") {
            return new NextResponse("Unauthorized", { status: 401 });
        }

        const { userId, role } = await request.json();

        // Cek apakah target user adalah admin
        const targetUser = await prisma.user.findUnique({
            where: { id: userId },
            select: { role: true }
        });

        if (targetUser?.role === "ADMIN") {
            return new NextResponse(
                "Tidak dapat mengubah role admin lain", 
                { status: 403 }
            );
        }
        
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: { role },
            select: {
                id: true,
                name: true,
                email: true,
                role: true
            }
        });

        // Revalidate setelah update
        revalidatePath('/admin/dashboard');
        revalidatePath('/api/users');
        
        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error("Update role error:", error);
        return new NextResponse("Internal Server Error", { status: 500 });
    }
} 
