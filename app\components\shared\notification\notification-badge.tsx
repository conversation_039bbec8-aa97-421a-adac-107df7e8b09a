"use client";

import { useState, useEffect } from "react";
import { Bell, BellRing } from "lucide-react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { ScrollArea } from "@/app/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { useNotificationManager } from "@/lib/hooks/use-notification-sync";
import { NotificationStatus } from "./notification-status";
import { gsap } from "gsap";
import {
  NotificationDropdown,
  NotificationItem,
  NotificationBadge as AnimatedBadge,
  PulsingIcon,
  LoadingDots
} from "./notification-animations";
import { SolidBackground } from "./solid-background";

export function NotificationBadge() {
  const [isOpen, setIsOpen] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const {
    notifications,
    counts,
    isLoading,
    markAsRead,
    markAllAsRead,
    refresh
  } = useNotificationManager();

  // Fetch user role
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const response = await fetch('/api/users/me');
        if (response.ok) {
          const userData = await response.json();
          setUserRole(userData.role);
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
      }
    };

    fetchUserRole();
  }, []);

  // Detect user role
  const isAdmin = userRole === 'ADMIN';
  const notificationsPath = isAdmin ? '/admin/notifications' : '/user/notifications';

  // Handle dropdown open change
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Refresh notifications when dropdown opens
      refresh();
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'NEW_RENTAL':
      case 'RENTAL_CONFIRMED':
        return '🚚';
      case 'NEW_PAYMENT':
      case 'PAYMENT_SUCCESS':
        return '💰';
      case 'PAYMENT_FAILED':
        return '❌';
      case 'LOW_STOCK':
        return '⚠️';
      case 'OVERTIME_DETECTED':
        return '⏰';
      case 'OPERATION_STARTED':
      case 'OPERATION_COMPLETED':
        return '🔧';
      default:
        return '📢';
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Baru saja';
    if (diffInMinutes < 60) return `${diffInMinutes} menit lalu`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} jam lalu`;
    return `${Math.floor(diffInMinutes / 1440)} hari lalu`;
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-9 w-9 rounded-xl hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200 hover:scale-105 active:scale-95"
        >
          <PulsingIcon isPulsing={counts.total > 0}>
            {counts.total > 0 ? (
              <BellRing className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            ) : (
              <Bell className="h-5 w-5 text-slate-600 dark:text-slate-400" />
            )}
          </PulsingIcon>
          <AnimatedBadge count={counts.total} isVisible={counts.total > 0} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-80 max-h-96 p-0 border-0 shadow-none bg-transparent"
        sideOffset={8}
      >
        <SolidBackground isVisible={isOpen} variant="dropdown" className="w-full">
          <div className="p-0">
            <DropdownMenuLabel className="flex items-center justify-between">
              <span>Notifikasi</span>
              {counts.total > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="h-6 px-2 text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400"
                >
                  Tandai Semua Dibaca
                </Button>
              )}
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            {isLoading ? (
              <div className="p-4 text-center text-sm text-slate-500">
                <LoadingDots className="mb-2" />
                <div>Memuat notifikasi...</div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-4 text-center text-sm text-slate-500">
                <div className="text-4xl mb-2 opacity-50">🔔</div>
                <div>Tidak ada notifikasi</div>
              </div>
            ) : (
              <ScrollArea className="max-h-64">
                {notifications.map((notification, index) => (
                  <NotificationItem
                    key={notification.id}
                    isRead={notification.isRead}
                    index={index}
                  >
                    <DropdownMenuItem
                      className={cn(
                        "flex items-start gap-3 p-3 cursor-pointer",
                        !notification.isRead && "bg-blue-50 dark:bg-blue-900/20"
                      )}
                      onClick={() => {
                        if (!notification.isRead) {
                          markAsRead(notification.id);
                        }
                      }}
                    >
                      <div className="text-lg flex-shrink-0 mt-0.5">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <p className={cn(
                            "text-sm font-medium truncate",
                            !notification.isRead && "text-blue-900 dark:text-blue-100"
                          )}>
                            {notification.title}
                          </p>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 ml-2 animate-pulse" />
                          )}
                        </div>
                        <p className="text-xs text-slate-600 dark:text-slate-400 line-clamp-2">
                          {notification.message}
                        </p>
                        <p className="text-xs text-slate-500 dark:text-slate-500 mt-1">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                      </div>
                    </DropdownMenuItem>
                  </NotificationItem>
                ))}
              </ScrollArea>
            )}

            {/* Footer dengan Status dan Lihat Semua */}
            <div className="border-t border-slate-100 dark:border-slate-700">
              <div className="px-3 py-2 flex items-center justify-between">
                <NotificationStatus />
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs text-violet-600 hover:text-violet-700 dark:text-violet-400"
                  onClick={() => {
                    // Navigate to notifications page based on role
                    window.location.href = notificationsPath;
                  }}
                >
                  Lihat Semua
                </Button>
              </div>
            </div>
          </div>
        </SolidBackground>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}