'use client';

import {
    Lu<PERSON>ayoutDashboard as LuDashboard,
    LuShoppingCart,
    LuPackage,
    LuBell,
} from 'react-icons/lu';
import { BottomNavigation } from './bottom-navigation';

const menuItems = [
    {
        href: '/user/dashboard',
        title: '<PERSON><PERSON><PERSON>',
        icon: LuDashboard
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: LuPackage
    },
    {
        href: '/user/rentals',
        title: 'Rental',
        icon: LuShoppingCart
    },
    {
        href: '/user/notifications',
        title: 'Notifikasi',
        icon: LuBell
    }
];

export function MobileBottomNavigation() {
    return <BottomNavigation menuItems={menuItems} variant="user" />;
}
