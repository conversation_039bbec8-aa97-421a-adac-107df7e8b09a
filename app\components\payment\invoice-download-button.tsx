"use client";

import { SoundButton } from "@/app/components/ui/sound-button";
import { LuDownload } from "react-icons/lu";
import { useToast } from "@/lib/hooks/use-toast";

interface InvoiceDownloadButtonProps {
  invoiceId: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  label?: string;
  fullWidth?: boolean;
}

export function InvoiceDownloadButton({
  invoiceId,
  variant = "outline",
  label = "Download Invoice",
  fullWidth = false
}: InvoiceDownloadButtonProps) {
  const { showError } = useToast();

  const handleDownload = () => {
    try {
      // Buka invoice PDF di tab baru untuk download
      window.open(`/api/invoices/${invoiceId}/pdf`, '_blank');
    } catch (error) {
      console.error("Error downloading invoice:", error);
      showError("Gagal Download Invoice. Terjadi kesalahan saat mengunduh invoice. Silakan coba lagi nanti.");
    }
  };

  return (
    <SoundButton
      variant={variant}
      size="mobile"
      onClick={handleDownload}
      className={`${fullWidth ? "w-full" : ""}`}
      soundType="click"
    >
      <LuDownload className="mr-2 h-4 w-4" />
      {label}
    </SoundButton>
  );
}
