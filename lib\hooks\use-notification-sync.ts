"use client";

import { useEffect, useCallback } from "react";
import { useNotifications } from "@/app/components/providers/notification-provider";

/**
 * Hook untuk sinkronisasi notifikasi real-time
 * Menyediakan fungsi untuk refresh otomatis dan manual
 */
export function useNotificationSync() {
  const { refreshAll, counts, notifications } = useNotifications();

  // Auto refresh saat window focus
  const handleWindowFocus = useCallback(() => {
    refreshAll();
  }, [refreshAll]);

  // Auto refresh saat online kembali
  const handleOnline = useCallback(() => {
    refreshAll();
  }, [refreshAll]);

  useEffect(() => {
    // Listen untuk window focus
    window.addEventListener('focus', handleWindowFocus);
    
    // Listen untuk online status
    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
      window.removeEventListener('online', handleOnline);
    };
  }, [handleWindowFocus, handleOnline]);

  return {
    refreshNotifications: refreshAll,
    unreadCount: counts.total,
    hasUnread: counts.total > 0,
    notifications,
    counts
  };
}

/**
 * Hook untuk mengelola notifikasi dengan auto-sync
 */
export function useNotificationManager() {
  const { 
    markAsRead, 
    markAllAsRead, 
    refreshAll,
    counts,
    notifications,
    isLoading 
  } = useNotifications();

  // Mark as read dengan auto refresh
  const handleMarkAsRead = useCallback(async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
      // Refresh setelah 500ms untuk memastikan sinkronisasi
      setTimeout(() => {
        refreshAll();
      }, 500);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, [markAsRead, refreshAll]);

  // Mark all as read dengan auto refresh
  const handleMarkAllAsRead = useCallback(async () => {
    try {
      await markAllAsRead();
      // Refresh setelah 500ms untuk memastikan sinkronisasi
      setTimeout(() => {
        refreshAll();
      }, 500);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, [markAllAsRead, refreshAll]);

  return {
    notifications,
    counts,
    isLoading,
    markAsRead: handleMarkAsRead,
    markAllAsRead: handleMarkAllAsRead,
    refresh: refreshAll,
    hasUnread: counts.total > 0,
    unreadCount: counts.total
  };
}
