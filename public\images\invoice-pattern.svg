<svg width="800" height="800" viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#3B82F6" stroke-width="0.5" opacity="0.1" />
        </pattern>
        <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
            <rect width="100" height="100" fill="url(#smallGrid)" />
            <path d="M 100 0 L 0 0 0 100" fill="none" stroke="#3B82F6" stroke-width="1" opacity="0.1" />
        </pattern>
    </defs>

    <rect width="800" height="800" fill="url(#grid)" />

    <!-- Invoice Icons -->
    <g transform="translate(150, 150)" opacity="0.15">
        <circle cx="0" cy="0" r="30" fill="#3B82F6" />
        <rect x="-15" y="-10" width="30" height="20" rx="2" fill="white" />
        <line x1="-10" y1="-5" x2="10" y2="-5" stroke="#3B82F6" stroke-width="2" />
        <line x1="-10" y1="0" x2="10" y2="0" stroke="#3B82F6" stroke-width="2" />
        <line x1="-10" y1="5" x2="5" y2="5" stroke="#3B82F6" stroke-width="2" />
    </g>

    <!-- Receipt Icon -->
    <g transform="translate(350, 250)" opacity="0.15">
        <circle cx="0" cy="0" r="25" fill="#3B82F6" />
        <rect x="-8" y="-15" width="16" height="30" rx="2" fill="white" />
        <line x1="-5" y1="-10" x2="5" y2="-10" stroke="#3B82F6" stroke-width="1.5" />
        <line x1="-5" y1="-5" x2="5" y2="-5" stroke="#3B82F6" stroke-width="1.5" />
        <line x1="-5" y1="0" x2="5" y2="0" stroke="#3B82F6" stroke-width="1.5" />
        <line x1="-5" y1="5" x2="3" y2="5" stroke="#3B82F6" stroke-width="1.5" />
    </g>

    <!-- Money Icon -->
    <g transform="translate(550, 150)" opacity="0.15">
        <circle cx="0" cy="0" r="35" fill="#3B82F6" />
        <rect x="-15" y="-8" width="30" height="16" rx="3" fill="white" />
        <circle cx="0" cy="0" r="5" fill="#3B82F6" />
        <text x="0" y="2" text-anchor="middle" font-size="8" fill="white" font-weight="bold">$</text>
    </g>

    <!-- Calculator Icon -->
    <g transform="translate(150, 550)" opacity="0.15">
        <circle cx="0" cy="0" r="30" fill="#3B82F6" />
        <rect x="-10" y="-15" width="20" height="30" rx="2" fill="white" />
        <rect x="-8" y="-12" width="16" height="6" fill="#3B82F6" />
        <circle cx="-4" cy="-2" r="1.5" fill="#3B82F6" />
        <circle cx="0" cy="-2" r="1.5" fill="#3B82F6" />
        <circle cx="4" cy="-2" r="1.5" fill="#3B82F6" />
        <circle cx="-4" cy="4" r="1.5" fill="#3B82F6" />
        <circle cx="0" cy="4" r="1.5" fill="#3B82F6" />
        <circle cx="4" cy="4" r="1.5" fill="#3B82F6" />
    </g>

    <!-- Chart Icon -->
    <g transform="translate(350, 650)" opacity="0.15">
        <circle cx="0" cy="0" r="25" fill="#3B82F6" />
        <rect x="-12" y="-12" width="24" height="24" fill="white" />
        <rect x="-8" y="2" width="3" height="8" fill="#3B82F6" />
        <rect x="-3" y="-2" width="3" height="12" fill="#3B82F6" />
        <rect x="2" y="5" width="3" height="5" fill="#3B82F6" />
        <rect x="7" y="0" width="3" height="10" fill="#3B82F6" />
    </g>

    <!-- Document Icon -->
    <g transform="translate(650, 450)" opacity="0.15">
        <circle cx="0" cy="0" r="35" fill="#3B82F6" />
        <rect x="-10" y="-15" width="20" height="30" rx="2" fill="white" />
        <polygon points="-10,-15 -10,-5 0,-15" fill="#3B82F6" />
        <line x1="-6" y1="-8" x2="6" y2="-8" stroke="#3B82F6" stroke-width="1" />
        <line x1="-6" y1="-3" x2="6" y2="-3" stroke="#3B82F6" stroke-width="1" />
        <line x1="-6" y1="2" x2="6" y2="2" stroke="#3B82F6" stroke-width="1" />
        <line x1="-6" y1="7" x2="3" y2="7" stroke="#3B82F6" stroke-width="1" />
    </g>

    <!-- Decorative elements -->
    <circle cx="200" cy="400" r="8" fill="#3B82F6" opacity="0.1" />
    <circle cx="600" cy="300" r="6" fill="#3B82F6" opacity="0.1" />
    <circle cx="500" cy="500" r="10" fill="#3B82F6" opacity="0.1" />
    <circle cx="300" cy="100" r="5" fill="#3B82F6" opacity="0.1" />
    <circle cx="700" cy="200" r="7" fill="#3B82F6" opacity="0.1" />
    <circle cx="100" cy="700" r="9" fill="#3B82F6" opacity="0.1" />
</svg>
