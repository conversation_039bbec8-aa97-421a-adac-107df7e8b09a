import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { getOperationStatus, getOperationStatusInfo } from "@/lib/utils/operation-status";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: rentalId } = await params;

    // Get rental data
    const rental = await prisma.rental.findUnique({
      where: { id: rentalId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            capacity: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!rental) {
      return NextResponse.json(
        { error: "Rental not found" },
        { status: 404 }
      );
    }

    // Check authorization - admin can see all, users can only see their own
    if (session.user.role !== "ADMIN" && rental.userId !== session.user.id) {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    // Get operation status using unified logic
    const operationStatus = getOperationStatus(rental);
    const statusInfo = getOperationStatusInfo(operationStatus);

    // Return comprehensive status information
    return NextResponse.json({
      success: true,
      data: {
        rentalId: rental.id,
        status: operationStatus,
        statusInfo: statusInfo,
        operationalStart: rental.operationalStart,
        operationalEnd: rental.operationalEnd,
        rentalStatus: rental.status,
        product: rental.product,
        user: rental.user,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("Error fetching operation status:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
