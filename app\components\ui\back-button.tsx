"use client";

import { useRouter } from 'next/navigation';
import { Button } from '@/app/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface BackButtonProps {
  href?: string;
  label?: string;
}

export function BackButton({
  href,
  label = "Kembali"
}: BackButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (href) {
      router.push(href);
    } else {
      router.back();
    }
  };

  return (
    <Button
      variant="ghost"
      size="mobile"
      onClick={handleClick}
      className="mb-4 gap-2"
    >
      <ArrowLeft size={16} className="text-violet-600 dark:text-violet-400" />
      {label}
    </Button>
  );
}
