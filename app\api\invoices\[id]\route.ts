import { NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { formatCurrency, formatDate } from "@/lib/utils/format";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const { id } = await params;

    const invoice = await prisma.rental.findUnique({
      where: { id },
      include: {
        user: true,
        product: true,
        payment: true
      }
    });

    if (!invoice || invoice.userId !== userId) {
      return NextResponse.json(
        { error: "Invoice not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(invoice);
  } catch (error) {
    console.error("Error fetching invoice:", error);
    return NextResponse.json(
      { error: "Failed to fetch invoice" },
      { status: 500 }
    );
  }
}

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = session.user.id;
    const { id } = await params;

    // Dapatkan data invoice
    const invoice = await prisma.rental.findUnique({
      where: { id },
      include: {
        user: true,
        product: true,
        payment: true
      }
    });

    if (!invoice || invoice.userId !== userId) {
      return NextResponse.json(
        { error: "Invoice not found" },
        { status: 404 }
      );
    }

    // Untuk implementasi awal, kita kirimkan data HTML sederhana
    // Di implementasi sebenarnya, ini akan menggunakan library PDF seperti jsPDF, PDFKit, atau html-pdf
    const depositAmount = Math.floor(invoice.amount * 0.5);
    const remainingAmount = invoice.amount - depositAmount;

    // Buat template HTML untuk invoice
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Invoice #${id.substring(0, 8)}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
            .invoice-box { max-width: 800px; margin: auto; padding: 30px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0, 0, 0, .15); }
            .invoice-box table { width: 100%; line-height: inherit; text-align: left; }
            .invoice-box table td { padding: 5px; vertical-align: top; }
            .invoice-box table tr.top td { padding-bottom: 20px; }
            .invoice-box table tr.heading td { background: #eee; border-bottom: 1px solid #ddd; font-weight: bold; }
            .invoice-box table tr.item td { border-bottom: 1px solid #eee; }
            .invoice-box table tr.total td:nth-child(2) { border-top: 2px solid #eee; font-weight: bold; }
            @media only print { .invoice-box { max-width: 100%; } }
            .header { text-align: center; margin-bottom: 20px; }
            .info { margin-bottom: 20px; }
            .info div { margin-bottom: 5px; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #999; }
          </style>
        </head>
        <body>
          <div class="invoice-box">
            <div class="header">
              <h1>INVOICE</h1>
              <h2>#${id.substring(0, 8)}</h2>
            </div>

            <div class="info">
              <div><strong>Pelanggan:</strong> ${invoice.user.name}</div>
              <div><strong>Tanggal:</strong> ${formatDate(invoice.createdAt)}</div>
              <div><strong>Jatuh Tempo:</strong> ${formatDate(new Date(invoice.startDate.getTime() - (7 * 24 * 60 * 60 * 1000)))}</div>
            </div>

            <table>
              <tr class="heading">
                <td>Item</td>
                <td style="text-align: right;">Harga</td>
              </tr>

              <tr class="item">
                <td>${invoice.product.name} (${invoice.product.capacity} kVA)<br>
                    Periode: ${formatDate(invoice.startDate)} - ${formatDate(invoice.endDate)}</td>
                <td style="text-align: right;">${formatCurrency(invoice.amount)}</td>
              </tr>

              <tr class="total">
                <td></td>
                <td style="text-align: right;">Total: ${formatCurrency(invoice.amount)}</td>
              </tr>
            </table>

            <div style="margin-top: 30px; border: 1px solid #ddd; padding: 15px; background-color: #f9f9f9;">
              <h3 style="margin-top: 0;">Jadwal Pembayaran</h3>
              <div style="display: flex; justify-content: space-between;">
                <span>Deposit (50%)</span>
                <span>${formatCurrency(depositAmount)}</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                <span>Sisa Pembayaran (50%)</span>
                <span>${formatCurrency(remainingAmount)}</span>
              </div>
              <div style="font-size: 12px; margin-top: 10px; color: #666;">
                * Sisa pembayaran dibayarkan setelah operasi selesai
              </div>
            </div>

            <div style="margin-top: 30px; border: 1px solid #ddd; padding: 15px; background-color: #f9f9f9;">
              <h3 style="margin-top: 0;">Instruksi Pembayaran</h3>
              <p>Mohon lakukan pembayaran deposit 50% terlebih dahulu sebelum tanggal jatuh tempo ke rekening:</p>
              <div style="margin-top: 10px;">
                <div><strong>Bank:</strong> Bank BCA</div>
                <div><strong>No. Rekening:</strong> **********</div>
                <div><strong>Atas Nama:</strong> PT Rental Genset</div>
              </div>
            </div>

            <div class="footer">
              <p>Invoice ini dibuat secara otomatis dan sah tanpa tanda tangan.</p>
              <p>Rental Genset Indonesia &copy; ${new Date().getFullYear()}</p>
            </div>
          </div>
        </body>
      </html>
    `;

    // Set response headers untuk file PDF
    // Simulasi PDF dengan HTML untuk tujuan demonstrasi
    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="invoice-${id.substring(0, 8)}.html"`,
      },
    });

  } catch (error) {
    console.error("Error generating invoice PDF:", error);
    return NextResponse.json(
      { error: "Failed to generate invoice PDF" },
      { status: 500 }
    );
  }
}