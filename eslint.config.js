/** @type {import('eslint').Linter.Config} */
module.exports = {
  root: true,
  extends: ["next/core-web-vitals"],
  ignorePatterns: [
    "**/node_modules/**",
    ".next/**",
    "out/**",
    "dist/**",
    "app/(dashboard)/admin/notifications-test/**",
    "app/(dashboard)/user/layout.tsx",
    "app/(dashboard)/user/payments/**",
    "app/(dashboard)/user/profile/**",
    "app/api/**",
    "app/components/**",
    "lib/**",
  ],
  rules: {
    "@typescript-eslint/no-unused-vars": "off",
    "react-hooks/exhaustive-deps": "off",
  },
};
