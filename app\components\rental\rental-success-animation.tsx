"use client";

import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Package, LuCalendar, LuMapPin } from "react-icons/lu";
import { useRouter } from "next/navigation";

interface RentalSuccessAnimationProps {
  isVisible: boolean;
  rentalData: {
    id: string;
    productName: string;
    startDate: string;
    endDate: string;
    totalAmount: number;
    deliveryAddress: string;
  };
  onClose: () => void;
}

export function RentalSuccessAnimation({
  isVisible,
  rentalData,
  onClose
}: RentalSuccessAnimationProps) {
  const router = useRouter();
  const [showButton, setShowButton] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const messageRef = useRef<HTMLParagraphElement>(null);
  const detailsRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);
  const circleRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isVisible || !containerRef.current) return;

    // Reset initial states
    gsap.set([iconRef.current, titleRef.current, messageRef.current, detailsRef.current, buttonRef.current], {
      opacity: 0,
      y: 30,
      scale: 0.8
    });

    gsap.set(circleRef.current, {
      scale: 0,
      rotation: -180
    });

    gsap.set(containerRef.current, {
      opacity: 0,
      scale: 0.9
    });

    // Create timeline
    const tl = gsap.timeline();

    // Container entrance
    tl.to(containerRef.current, {
      opacity: 1,
      scale: 1,
      duration: 0.5,
      ease: "back.out(1.7)"
    })
    // Circle animation
    .to(circleRef.current, {
      scale: 1,
      rotation: 0,
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "-=0.2")
    // Icon animation with bounce
    .to(iconRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      ease: "back.out(1.7)"
    }, "-=0.4")
    // Title animation
    .to(titleRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.3")
    // Message animation
    .to(messageRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    // Details animation
    .to(detailsRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.3");

    // Create particles
    const particles = [];
    for (let i = 0; i < 15; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 bg-green-400 rounded-full';
      particle.style.left = '50%';
      particle.style.top = '50%';
      particlesRef.current?.appendChild(particle);
      particles.push(particle);
    }

    // Particles animation
    const particleDistance = 100;
    tl.to(particles, {
      opacity: 1,
      duration: 0.1
    })
    .to(particles, {
      x: () => (Math.random() - 0.5) * particleDistance * 2,
      y: () => (Math.random() - 0.5) * particleDistance * 2,
      scale: () => Math.random() * 0.8 + 0.4,
      duration: 1.2,
      ease: "power2.out"
    }, "-=0.1")
    .to(particles, {
      opacity: 0,
      y: particleDistance * 2,
      duration: 1,
      ease: "power2.in"
    }, "-=0.5")
    // Show button after particles animation
    .call(() => setShowButton(true))
    .to(buttonRef.current, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "+=0.5");

    return () => {
      tl.kill();
      particles.forEach(particle => particle.remove());
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div
        ref={containerRef}
        className="relative bg-white dark:bg-gray-800 rounded-2xl p-6 sm:p-8 max-w-md w-full shadow-2xl border border-gray-100 dark:border-gray-700"
      >
        {/* Background Circle */}
        <div
          ref={circleRef}
          className="absolute inset-0 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl"
        />

        {/* Particles Container */}
        <div
          ref={particlesRef}
          className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl"
        />

        {/* Content */}
        <div className="relative text-center space-y-4">
          {/* Icon */}
          <div ref={iconRef} className="flex justify-center">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
              <LuCheck className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
            </div>
          </div>

          {/* Title */}
          <h2
            ref={titleRef}
            className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white"
          >
            Pesanan Berhasil!
          </h2>

          {/* Message */}
          <p
            ref={messageRef}
            className="text-gray-600 dark:text-gray-300 text-sm sm:text-base"
          >
            Terima kasih telah menyewa Genset. Kami akan menghubungi Anda segera untuk konfirmasi pesanan.
          </p>

          {/* Rental Details */}
          <div
            ref={detailsRef}
            className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 space-y-3 text-left"
          >
            <div className="flex items-center gap-3">
              <LuPackage className="h-5 w-5 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">Produk</p>
                <p className="font-medium text-gray-900 dark:text-white">{rentalData.productName}</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <LuCalendar className="h-5 w-5 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">Periode Sewa</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {rentalData.startDate} - {rentalData.endDate}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <LuMapPin className="h-5 w-5 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">Alamat Pengiriman</p>
                <p className="font-medium text-gray-900 dark:text-white text-sm">
                  {rentalData.deliveryAddress}
                </p>
              </div>
            </div>
          </div>

          {/* Action Button */}
          {showButton && (
            <div ref={buttonRef} className="mt-6">
              <button
                onClick={() => {
                  onClose();
                  router.push('/user/payments');
                }}
                className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              >
                <LuCheck className="h-5 w-5" />
                Lihat Detail Pembayaran
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
