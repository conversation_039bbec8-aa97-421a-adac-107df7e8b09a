import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { redirect } from "next/navigation";
import { notFound } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { formatDate, formatCurrency } from "@/lib/utils/format";
import { ArrowLeft, X, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { updateRentalStatus } from "@/lib/actions/rental";

export default async function CancelRentalPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getSession();

  // Redirect ke login jika tidak ada session atau bukan admin
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect('/login');
  }

  // Ambil ID rental dari parameter URL (dengan await)
  const { id } = await params;

  // Ambil data rental dari database
  const rental = await prisma.rental.findUnique({
    where: { id },
    include: {
      product: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true
        }
      },
      payment: true
    }
  });

  // Jika rental tidak ditemukan, tampilkan halaman 404
  if (!rental) {
    notFound();
  }

  // Jika rental bukan PENDING, redirect ke detail
  if (rental.status !== "PENDING") {
    redirect(`/admin/rentals/${id}`);
  }

  // Handle cancel action
  async function handleCancel() {
    "use server";

    try {
      await updateRentalStatus(id, "CANCELLED");
      redirect(`/admin/rentals/${id}`);
    } catch (error) {
      console.error("Error cancelling rental:", error);
      // Handle error appropriately
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge variant="outline" className="bg-yellow-500/20 text-yellow-700 border-yellow-500">Menunggu</Badge>;
      case "CONFIRMED":
        return <Badge variant="outline" className="bg-blue-500/20 text-blue-700 border-blue-500">Dikonfirmasi</Badge>;
      case "ACTIVE":
        return <Badge variant="outline" className="bg-green-500/20 text-green-700 border-green-500">Aktif</Badge>;
      case "COMPLETED":
        return <Badge variant="outline" className="bg-purple-500/20 text-purple-700 border-purple-500">Selesai</Badge>;
      case "CANCELLED":
        return <Badge variant="outline" className="bg-red-500/20 text-red-700 border-red-500">Dibatalkan</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/rentals">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Batalkan Penyewaan</h1>
      </div>

      <div className="grid gap-6">
        {/* Warning Card */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              Konfirmasi Pembatalan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">
              Apakah Anda yakin ingin membatalkan penyewaan ini? Tindakan ini tidak dapat dibatalkan.
            </p>
            <div className="flex gap-3">
              <form action={handleCancel}>
                <Button type="submit" variant="destructive" className="flex items-center gap-2">
                  <X className="h-4 w-4" />
                  Ya, Batalkan Penyewaan
                </Button>
              </form>
              <Link href={`/admin/rentals/${id}`}>
                <Button variant="outline">
                  Batal
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Rental Details */}
        <Card>
          <CardHeader>
            <CardTitle>Detail Penyewaan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">ID Penyewaan</label>
                <p className="font-mono text-sm">{rental.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <div className="mt-1">
                  {getStatusBadge(rental.status)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Produk</label>
                <p className="font-medium">{rental.product.name}</p>
                <p className="text-sm text-gray-500">{rental.product.capacity} KVA</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Pelanggan</label>
                <p className="font-medium">{rental.user.name}</p>
                <p className="text-sm text-gray-500">{rental.user.phone}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Tanggal Mulai</label>
                <p>{formatDate(rental.startDate)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Tanggal Selesai</label>
                <p>{formatDate(rental.endDate)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Total Biaya</label>
                <p className="font-medium">{formatCurrency(rental.amount)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Alamat</label>
                <p>{rental.address || rental.location}</p>
              </div>
            </div>

            {rental.notes && (
              <div>
                <label className="text-sm font-medium text-gray-500">Catatan</label>
                <p className="mt-1 text-sm bg-gray-50 p-3 rounded-md">{rental.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
