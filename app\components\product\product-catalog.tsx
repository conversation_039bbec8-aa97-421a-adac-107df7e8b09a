import { getProducts } from "@/lib/data/product";
import { ProductCard } from "@/app/components/product/product-card";
import { Product } from "@/lib/types/product";

interface ProductCatalogProps {
  query?: string;
}

export async function ProductCatalog({ query }: ProductCatalogProps) {
  const products = await getProducts();
  const availableProducts = products?.items?.filter((p: Product) => p.status === "AVAILABLE");

  if (!availableProducts?.length) {
    return (
      <div className="text-center p-6">
        <p className="text-gray-500">
          {query ? "Tidak ada produk yang ditemukan" : "Belum ada produk tersedia"}
        </p>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {availableProducts.map((product: Product) => (
        <ProductCard key={product.id} product={{...product, user: null}} />
      ))}
    </div>
  );
}
