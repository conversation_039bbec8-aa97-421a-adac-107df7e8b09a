"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";

interface SmoothToastProps {
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center";
}

export function SmoothToast({
  type,
  title,
  message,
  isVisible,
  onClose,
  duration = 5000,
  position = "top-right"
}: SmoothToastProps) {
  const toastRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5" />;
      case "error":
        return <AlertCircle className="w-5 h-5" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5" />;
      case "info":
        return <Info className="w-5 h-5" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case "success":
        return {
          bg: "bg-green-50/95 dark:bg-green-900/90",
          border: "border-green-200 dark:border-green-800",
          icon: "text-green-600 dark:text-green-400",
          title: "text-green-900 dark:text-green-100",
          message: "text-green-700 dark:text-green-300",
          progress: "bg-green-500"
        };
      case "error":
        return {
          bg: "bg-red-50/95 dark:bg-red-900/90",
          border: "border-red-200 dark:border-red-800",
          icon: "text-red-600 dark:text-red-400",
          title: "text-red-900 dark:text-red-100",
          message: "text-red-700 dark:text-red-300",
          progress: "bg-red-500"
        };
      case "warning":
        return {
          bg: "bg-yellow-50/95 dark:bg-yellow-900/90",
          border: "border-yellow-200 dark:border-yellow-800",
          icon: "text-yellow-600 dark:text-yellow-400",
          title: "text-yellow-900 dark:text-yellow-100",
          message: "text-yellow-700 dark:text-yellow-300",
          progress: "bg-yellow-500"
        };
      case "info":
        return {
          bg: "bg-blue-50/95 dark:bg-blue-900/90",
          border: "border-blue-200 dark:border-blue-800",
          icon: "text-blue-600 dark:text-blue-400",
          title: "text-blue-900 dark:text-blue-100",
          message: "text-blue-700 dark:text-blue-300",
          progress: "bg-blue-500"
        };
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case "top-right":
        return "top-4 right-4";
      case "top-left":
        return "top-4 left-4";
      case "bottom-right":
        return "bottom-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "top-center":
        return "top-4 left-1/2 transform -translate-x-1/2";
      case "bottom-center":
        return "bottom-4 left-1/2 transform -translate-x-1/2";
    }
  };

  useEffect(() => {
    if (!toastRef.current || !progressRef.current) return;

    if (isVisible) {
      // Animate in
      const isFromTop = position.includes("top");
      const isFromRight = position.includes("right");
      const isFromLeft = position.includes("left");

      let fromX = 0;
      let fromY = 0;

      if (isFromTop) fromY = -100;
      else fromY = 100;

      if (isFromRight) fromX = 100;
      else if (isFromLeft) fromX = -100;

      gsap.fromTo(toastRef.current,
        {
          opacity: 0,
          x: fromX,
          y: fromY,
          scale: 0.8,
          filter: "blur(4px)"
        },
        {
          opacity: 1,
          x: 0,
          y: 0,
          scale: 1,
          filter: "blur(0px)",
          duration: 0.5,
          ease: "back.out(1.7)"
        }
      );

      // Animate progress bar
      gsap.fromTo(progressRef.current,
        { width: "100%" },
        {
          width: "0%",
          duration: duration / 1000,
          ease: "none"
        }
      );

      // Auto close
      timeoutRef.current = setTimeout(() => {
        handleClose();
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isVisible, duration, position]);

  const handleClose = () => {
    if (!toastRef.current) return;

    // Animate out
    gsap.to(toastRef.current, {
      opacity: 0,
      scale: 0.8,
      x: position.includes("right") ? 100 : position.includes("left") ? -100 : 0,
      y: position.includes("top") ? -50 : 50,
      filter: "blur(4px)",
      duration: 0.3,
      ease: "power2.in",
      onComplete: onClose
    });
  };

  const colors = getColors();

  if (!isVisible) return null;

  return (
    <div
      ref={toastRef}
      className={cn(
        "fixed z-50 w-80 max-w-sm pointer-events-auto",
        getPositionClasses()
      )}
    >
      <div
        className={cn(
          "relative overflow-hidden rounded-xl border shadow-lg",
          colors.bg,
          colors.border
        )}
        style={{
          backdropFilter: 'blur(12px) saturate(180%)',
          WebkitBackdropFilter: 'blur(12px) saturate(180%)'
        }}
      >
        {/* Progress bar */}
        <div className="absolute bottom-0 left-0 h-1 bg-black/10 w-full">
          <div
            ref={progressRef}
            className={cn("h-full transition-all", colors.progress)}
          />
        </div>

        <div className="p-4">
          <div className="flex items-start gap-3">
            <div className={cn("flex-shrink-0 mt-0.5", colors.icon)}>
              {getIcon()}
            </div>

            <div className="flex-1 min-w-0">
              <h4 className={cn("text-sm font-semibold", colors.title)}>
                {title}
              </h4>
              {message && (
                <p className={cn("text-sm mt-1", colors.message)}>
                  {message}
                </p>
              )}
            </div>

            <button
              onClick={handleClose}
              className={cn(
                "flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors",
                colors.icon
              )}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Toast manager hook
export function useSmoothToast() {
  const showToast = (props: Omit<SmoothToastProps, "isVisible" | "onClose">) => {
    // This would integrate with a toast context/provider
    // For now, just log the toast
    console.log("Toast:", props);
  };

  return { showToast };
}
