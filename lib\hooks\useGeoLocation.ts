import { useState, useCallback } from 'react';

/**
 * Hook untuk mendapatkan lokasi pengguna saat ini menggunakan Geolocation API
 * dengan fitur retry dan validasi koordinat untuk akurasi yang lebih baik
 */
const useGeoLocation = () => {
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number; accuracy?: number } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [positionError, setPositionError] = useState<string | null>(null);

  const clearPositionError = useCallback(() => {
    setPositionError(null);
    setError(null);
  }, []);

  // Fungsi untuk memvalidasi apakah koordinat masuk akal untuk Indonesia
  const isValidIndonesianCoordinates = useCallback((lat: number, lng: number): boolean => {
    // Indonesia berada di sekitar:
    // Latitude: -11° hingga 6° (Utara-Selatan)
    // Longitude: 95° hingga 141° (Barat-Timur)
    return lat >= -11 && lat <= 6 && lng >= 95 && lng <= 141;
  }, []);

  // Fungsi untuk mendapatkan posisi dengan retry mechanism
  const getPositionWithRetry = useCallback(async (maxRetries: number = 3): Promise<GeolocationPosition> => {
    let lastError: Error | null = null;
    let bestPosition: GeolocationPosition | null = null;
    let bestAccuracy = Infinity;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`Percobaan mendapatkan lokasi ke-${attempt} dari ${maxRetries}`);

      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          const timeoutMs = attempt === 1 ? 15000 : 20000; // Timeout lebih lama untuk percobaan selanjutnya

          navigator.geolocation.getCurrentPosition(
            (pos) => {
              console.log(`Percobaan ke-${attempt} berhasil:`, {
                lat: pos.coords.latitude,
                lng: pos.coords.longitude,
                accuracy: pos.coords.accuracy
              });
              resolve(pos);
            },
            (err) => {
              console.error(`Percobaan ke-${attempt} gagal dengan error:`, {
                code: err.code,
                message: err.message,
                PERMISSION_DENIED: err.PERMISSION_DENIED,
                POSITION_UNAVAILABLE: err.POSITION_UNAVAILABLE,
                TIMEOUT: err.TIMEOUT
              });
              reject(err);
            },
            {
              enableHighAccuracy: true,
              timeout: timeoutMs,
              maximumAge: attempt === 1 ? 0 : 30000 // Izinkan cache untuk percobaan selanjutnya
            }
          );
        });

        const { latitude, longitude, accuracy } = position.coords;

        // Validasi koordinat
        if (!isValidIndonesianCoordinates(latitude, longitude)) {
          console.warn(`Koordinat tidak valid untuk Indonesia: ${latitude}, ${longitude}`);
          const validationError = new Error("Koordinat tidak valid untuk Indonesia");
          lastError = validationError;
          continue;
        }

        // Jika ini adalah posisi pertama yang valid, atau lebih akurat dari sebelumnya
        if (!bestPosition || (accuracy && accuracy < bestAccuracy)) {
          bestPosition = position;
          bestAccuracy = accuracy || Infinity;
          console.log(`Posisi terbaik diperbarui: akurasi ${accuracy || 'tidak diketahui'}m`);
        }

        // Jika akurasi sudah cukup baik (< 100m), gunakan posisi ini
        if (accuracy && accuracy < 100) {
          console.log(`Akurasi cukup baik (${accuracy}m), menggunakan posisi ini`);
          return position;
        }

        // Jika ini percobaan terakhir, gunakan posisi terbaik yang ada
        if (attempt === maxRetries && bestPosition) {
          console.log(`Menggunakan posisi terbaik dengan akurasi ${bestAccuracy === Infinity ? 'tidak diketahui' : bestAccuracy}m`);
          return bestPosition;
        }

      } catch (error) {
        // Improved error logging
        if (error && typeof error === 'object' && 'code' in error) {
          const geoError = error as GeolocationPositionError;
          console.error(`Percobaan ke-${attempt} gagal dengan GeolocationPositionError:`, {
            code: geoError.code,
            message: geoError.message,
            errorType: geoError.code === 1 ? 'PERMISSION_DENIED' :
                      geoError.code === 2 ? 'POSITION_UNAVAILABLE' :
                      geoError.code === 3 ? 'TIMEOUT' : 'UNKNOWN'
          });
          lastError = new Error(geoError.message || `Geolocation error code: ${geoError.code}`);
        } else {
          console.error(`Percobaan ke-${attempt} gagal dengan error:`, error);
          lastError = error instanceof Error ? error : new Error(String(error));
        }

        // Jika ini bukan percobaan terakhir, tunggu sebentar sebelum retry
        if (attempt < maxRetries) {
          console.log(`Menunggu 1 detik sebelum percobaan ke-${attempt + 1}...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // Jika ada posisi terbaik, gunakan itu
    if (bestPosition) {
      console.log(`Menggunakan posisi terbaik yang tersedia dengan akurasi ${bestAccuracy === Infinity ? 'tidak diketahui' : bestAccuracy}m`);
      return bestPosition;
    }

    // Jika semua percobaan gagal, throw error terakhir
    const finalError = lastError || new Error("Gagal mendapatkan lokasi setelah beberapa percobaan");
    console.error("Semua percobaan gagal, throwing error:", finalError);
    throw finalError;
  }, [isValidIndonesianCoordinates]);

  // Fallback function untuk mendapatkan lokasi dengan konfigurasi sederhana
  const getPositionSimple = useCallback((): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          console.log("Fallback berhasil mendapatkan lokasi:", {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            accuracy: position.coords.accuracy
          });
          resolve(position);
        },
        (error) => {
          console.error("Fallback gagal:", error);
          reject(error);
        },
        {
          enableHighAccuracy: false, // Gunakan akurasi rendah untuk fallback
          timeout: 10000,
          maximumAge: 60000 // Izinkan cache yang lebih lama
        }
      );
    });
  }, []);

  const getCurrentPosition = useCallback(() => {
    if (!navigator.geolocation) {
      const errorMsg = "Geolokasi tidak didukung di browser Anda.";
      setError(errorMsg);
      setPositionError(errorMsg);
      return Promise.reject(new Error(errorMsg));
    }

    setIsLoading(true);
    setError(null);
    setPositionError(null);

    // Coba dengan retry mechanism terlebih dahulu
    return getPositionWithRetry(3)
      .catch((retryError) => {
        console.warn("Retry mechanism gagal, mencoba fallback sederhana:", retryError);
        // Jika retry gagal, coba dengan konfigurasi sederhana
        return getPositionSimple();
      })
      .then((position) => {
        const { latitude, longitude, accuracy } = position.coords;

        // Validasi koordinat untuk fallback juga
        if (!isValidIndonesianCoordinates(latitude, longitude)) {
          throw new Error("Koordinat yang didapat tidak valid untuk Indonesia. Pastikan Anda berada di wilayah Indonesia.");
        }

        const newCoords = {
          lat: latitude,
          lng: longitude,
          accuracy: accuracy || undefined
        };
        setCoordinates(newCoords);
        setIsLoading(false);
        console.log(`Lokasi berhasil didapatkan: ${latitude}, ${longitude} (akurasi: ${accuracy || 'tidak diketahui'}m)`);
        return position;
      })
      .catch((error) => {
        console.error("Error mendapatkan geolokasi (semua metode gagal):", error);
        let errorMessage = "Terjadi kesalahan saat mendapatkan lokasi Anda.";

        // Handle GeolocationPositionError
        if (error && typeof error === 'object' && 'code' in error) {
          const geoError = error as GeolocationPositionError;
          switch(geoError.code) {
            case 1: // PERMISSION_DENIED
              errorMessage = "Akses lokasi ditolak. Silakan izinkan akses lokasi di browser Anda dan refresh halaman.";
              break;
            case 2: // POSITION_UNAVAILABLE
              errorMessage = "Informasi lokasi tidak tersedia. Pastikan GPS aktif dan Anda berada di area dengan sinyal yang baik.";
              break;
            case 3: // TIMEOUT
              errorMessage = "Waktu permintaan lokasi habis. Coba lagi atau pastikan GPS aktif.";
              break;
            default:
              errorMessage = geoError.message || "Error geolokasi tidak dikenal.";
          }
        } else if (error instanceof Error && error.message) {
          errorMessage = error.message;
        }

        setError(errorMessage);
        setPositionError(errorMessage);
        setIsLoading(false);
        throw new Error(errorMessage);
      });
  }, [getPositionWithRetry, getPositionSimple, isValidIndonesianCoordinates]);

  return {
    coordinates,
    isLoading,
    error,
    positionError,
    getCurrentPosition,
    clearPositionError,
    setCoordinates
  };
};

export default useGeoLocation; 
