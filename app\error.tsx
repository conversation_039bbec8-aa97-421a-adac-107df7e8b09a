"use client";

import { useEffect } from 'react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          <PERSON><PERSON><PERSON><PERSON>
        </h2>
        <p className="text-gray-600 mb-4">
          <PERSON><PERSON> maaf, terjadi kesalahan dalam memproses permintaan Anda.
        </p>
        <button
          onClick={reset}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          <PERSON><PERSON>
        </button>
      </div>
    </div>
  );
}
