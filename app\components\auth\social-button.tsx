"use client";
import { Io<PERSON>ogoGoogle } from "react-icons/io5";
import { signIn } from "@/lib/auth/client";
import { useState } from "react";
import { LuLoader } from "react-icons/lu";

export function GoogleButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    try {
      setIsLoading(true);

      const result = await signIn.social({
        provider: "google",
        callbackURL: "/user/dashboard",
      });

      if (result.error) {
        console.error("Login error:", {
          error: result.error,
          message: result.error.message || "Unknown error",
          code: result.error.code || "UNKNOWN_ERROR"
        });
        alert("Gagal login dengan Google. Silakan coba lagi.");
        setIsLoading(false);
      } else {
        // Login berhasil, redirect akan dilakukan otomatis oleh Better Auth
        console.log("Login successful");
      }
    } catch (error) {
      console.error("Login error:", {
        error,
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined
      });
      alert("Terjadi kesalahan saat login. <PERSON>lakan coba lagi.");
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800/60 p-5 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
      <div className="space-y-4">
        <div className="flex items-start gap-3">
          <div className="h-8 w-8 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center flex-shrink-0">
            <IoLogoGoogle className="h-4 w-4 text-red-500 dark:text-red-400" />
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
            <span className="font-semibold text-gray-800 dark:text-gray-200">Penting:</span> Jika Anda sebelumnya
            mendaftar menggunakan Google, gunakan tombol di bawah ini untuk login.
          </p>
        </div>

        <button
          onClick={handleSignIn}
          disabled={isLoading}
          className="flex items-center justify-center gap-3 w-full bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-red-200 dark:hover:border-red-800/50 hover:bg-red-50/50 dark:hover:bg-red-900/10 shadow-sm hover:shadow transition-all duration-200 disabled:opacity-70 group"
          aria-label="Login dengan Google"
        >
          {isLoading ? (
            <>
              <LuLoader className="h-5 w-5 text-red-500 dark:text-red-400 animate-spin" />
              <span>Menghubungkan...</span>
            </>
          ) : (
            <>
              <div className="bg-white p-1.5 rounded-full shadow-sm">
                <IoLogoGoogle className="text-xl text-red-500" />
              </div>
              <span className="group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">Lanjutkan dengan Google</span>
            </>
          )}
        </button>

        <p className="text-xs text-gray-500 dark:text-gray-400 text-center flex items-center justify-center gap-1.5">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-3 h-3 text-red-400 dark:text-red-500">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clipRule="evenodd" />
          </svg>
          Login ini aman dan terhubung dengan akun Google Anda
        </p>
      </div>
    </div>
  );
}
