import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { PasswordUpdateSchema } from "@/lib/validations/user/schema";
import * as bcrypt from "bcrypt-ts";

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, message: "Anda harus login terlebih dahulu" },
        { status: 401 }
      );
    }

    const body = await request.json();
    console.log("Change password request for user:", session.user.id);

    // Validasi data menggunakan schema Zod
    const validatedData = PasswordUpdateSchema.safeParse(body);
    if (!validatedData.success) {
      console.log("Validation errors:", validatedData.error.errors);
      return NextResponse.json({
        success: false,
        message: "Data tidak valid",
        errors: validatedData.error.errors
      }, { status: 400 });
    }

    // Cek user di database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        email: true,
        password: true
      },
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        message: "User tidak ditemukan"
      }, { status: 404 });
    }

    // Cek password di tabel user atau account (Better Auth)
    let currentPassword = user.password;
    let updateTarget: 'user' | 'account' = 'user';

    if (!currentPassword) {
      console.log("No password in user table, checking account table...");
      
      // Cek di tabel account untuk Better Auth
      const account = await prisma.account.findFirst({
        where: {
          userId: session.user.id,
          providerId: "credential"
        },
        select: {
          id: true,
          password: true
        },
      });

      if (account?.password) {
        currentPassword = account.password;
        updateTarget = 'account';
      }
    }

    if (!currentPassword) {
      return NextResponse.json({
        success: false,
        message: "Password tidak ditemukan untuk user ini"
      }, { status: 400 });
    }

    console.log("Using password from:", updateTarget, "table");
    console.log("Current password hash length:", currentPassword.length);

    // Verifikasi password lama
    let isPasswordValid = false;
    
    try {
      // Cek apakah hash dalam format bcrypt yang benar
      if (currentPassword.length === 60 && currentPassword.startsWith('$2')) {
        isPasswordValid = await bcrypt.compare(
          validatedData.data.currentPassword,
          currentPassword
        );
      } else {
        console.log("Password hash format tidak standar, panjang:", currentPassword.length);
        // Untuk Better Auth yang mungkin menggunakan format hash berbeda
        // Coba beberapa metode verifikasi
        
        // Method 1: Direct comparison (untuk plain text - HANYA DEVELOPMENT)
        if (process.env.NODE_ENV === 'development') {
          isPasswordValid = validatedData.data.currentPassword === currentPassword;
        }
        
        // Method 2: Coba hash dengan bcrypt dan bandingkan
        if (!isPasswordValid) {
          try {
            const testHash = await bcrypt.hash(validatedData.data.currentPassword, 10);
            // Ini tidak akan bekerja untuk verifikasi, tapi kita coba pendekatan lain
            
            // Coba hash password yang diinput dan bandingkan dengan yang ada
            // Ini hanya untuk testing - di production harus menggunakan proper verification
            console.log("Attempting alternative verification method");
          } catch (err) {
            console.log("Alternative verification failed:", err);
          }
        }
      }
    } catch (error) {
      console.error("Error verifying password:", error);
      return NextResponse.json({
        success: false,
        message: "Error saat memverifikasi password"
      }, { status: 500 });
    }

    if (!isPasswordValid) {
      return NextResponse.json({
        success: false,
        message: "Password saat ini tidak valid",
        errors: [{ path: ["currentPassword"], message: "Password saat ini salah" }]
      }, { status: 400 });
    }

    // Hash password baru dengan bcrypt
    const hashedPassword = await bcrypt.hash(validatedData.data.newPassword, 10);
    console.log("New password hashed, length:", hashedPassword.length);

    // Update password di database sesuai target
    if (updateTarget === 'user') {
      await prisma.user.update({
        where: { id: session.user.id },
        data: { password: hashedPassword },
      });
    } else {
      // Update di tabel account
      await prisma.account.updateMany({
        where: {
          userId: session.user.id,
          providerId: "credential"
        },
        data: { password: hashedPassword },
      });
    }

    console.log("Password updated successfully for user:", user.id);

    return NextResponse.json({
      success: true,
      message: "Password berhasil diperbarui"
    });

  } catch (error) {
    console.error("Error in change password API:", error);
    return NextResponse.json({
      success: false,
      message: "Gagal memperbarui password: " + (error as Error).message
    }, { status: 500 });
  }
}
