import { Product, ProductStatus } from '@prisma/client';
import { db } from '../db';

export class ProductRepository {
  async findById(id: string): Promise<Product | null> {
    return db.product.findUnique({
      where: { id }
    });
  }

  async findAll(options?: {
    status?: ProductStatus,
    userId?: string,
    category?: string,
    limit?: number,
    offset?: number
  }): Promise<Product[]> {
    return db.product.findMany({
      where: {
        status: options?.status,
        userId: options?.userId,
        category: options?.category
      },
      take: options?.limit,
      skip: options?.offset,
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async create(data: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    return db.product.create({
      data: {
        ...data,
        status: data.status || ProductStatus.AVAILABLE
      }
    });
  }

  async update(id: string, data: Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Product> {
    return db.product.update({
      where: { id },
      data
    });
  }

  async delete(id: string): Promise<Product> {
    return db.product.delete({
      where: { id }
    });
  }

  async updateStatus(id: string, status: ProductStatus): Promise<Product> {
    return db.product.update({
      where: { id },
      data: { status }
    });
  }

  async countByCategory(): Promise<Array<{ category: string, count: number }>> {
    const results = await db.product.groupBy({
      by: ['category'],
      _count: {
        id: true
      },
      where: {
        category: {
          not: null
        }
      }
    });

    return results.map(item => ({
      category: item.category || 'Tidak Terkategori',
      count: item._count.id
    }));
  }
} 
