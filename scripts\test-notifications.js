/**
 * Script untuk testing sistem notifikasi
 * Jalankan di browser console untuk automated testing
 */

class NotificationTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(`%c${logMessage}`, 
      type === 'success' ? 'color: green' : 
      type === 'error' ? 'color: red' : 
      type === 'warning' ? 'color: orange' : 'color: blue'
    );
    
    this.testResults.push({ timestamp, message, type });
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testBasicFunctionality() {
    this.log('🧪 Testing Basic Functionality...', 'info');
    
    try {
      // Test notification badge exists
      const badge = document.querySelector('[data-testid="notification-badge"]') || 
                   document.querySelector('button[aria-label*="notification"]') ||
                   document.querySelector('button:has(svg)');
      
      if (badge) {
        this.log('✅ Notification badge found', 'success');
      } else {
        this.log('❌ Notification badge not found', 'error');
      }

      // Test API endpoints
      const countsResponse = await fetch('/api/notifications/counts');
      if (countsResponse.ok) {
        const data = await countsResponse.json();
        this.log(`✅ Counts API working - Total: ${data.total}`, 'success');
      } else {
        this.log('❌ Counts API failed', 'error');
      }

      const notificationsResponse = await fetch('/api/notifications');
      if (notificationsResponse.ok) {
        const data = await notificationsResponse.json();
        this.log(`✅ Notifications API working - Count: ${data.notifications?.length || 0}`, 'success');
      } else {
        this.log('❌ Notifications API failed', 'error');
      }

    } catch (error) {
      this.log(`❌ Basic functionality test failed: ${error.message}`, 'error');
    }
  }

  async testCreateNotification() {
    this.log('🧪 Testing Create Notification...', 'info');
    
    try {
      const response = await fetch('/api/notifications/create-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Automated Test Notification',
          message: `Test created at ${new Date().toLocaleString()}`,
          type: 'NEW_RENTAL'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.log(`✅ Test notification created: ${data.notification.id}`, 'success');
        
        // Wait for UI to update
        await this.sleep(1000);
        
        // Trigger manual refresh
        if (window.dispatchEvent) {
          window.dispatchEvent(new CustomEvent('notification-updated'));
          this.log('✅ Manual refresh triggered', 'success');
        }
        
        return data.notification.id;
      } else {
        this.log('❌ Failed to create test notification', 'error');
      }
    } catch (error) {
      this.log(`❌ Create notification test failed: ${error.message}`, 'error');
    }
    
    return null;
  }

  async testMarkAsRead(notificationId) {
    if (!notificationId) return;
    
    this.log('🧪 Testing Mark as Read...', 'info');
    
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PATCH',
      });

      if (response.ok) {
        this.log('✅ Mark as read successful', 'success');
        
        // Wait for UI to update
        await this.sleep(1000);
        
        // Trigger refresh
        if (window.dispatchEvent) {
          window.dispatchEvent(new CustomEvent('notification-updated'));
        }
      } else {
        this.log('❌ Mark as read failed', 'error');
      }
    } catch (error) {
      this.log(`❌ Mark as read test failed: ${error.message}`, 'error');
    }
  }

  async testCrossTabSync() {
    this.log('🧪 Testing Cross-tab Sync...', 'info');
    
    try {
      // Simulate localStorage event
      localStorage.setItem('notification_update', Date.now().toString());
      this.log('✅ localStorage event triggered', 'success');
      
      // Simulate custom event
      if (window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('notification-updated'));
        this.log('✅ Custom event dispatched', 'success');
      }
      
      this.log('ℹ️ Open multiple tabs to fully test cross-tab sync', 'warning');
    } catch (error) {
      this.log(`❌ Cross-tab sync test failed: ${error.message}`, 'error');
    }
  }

  async testNetworkEvents() {
    this.log('🧪 Testing Network Events...', 'info');
    
    try {
      // Simulate online event
      window.dispatchEvent(new Event('online'));
      this.log('✅ Online event simulated', 'success');
      
      // Simulate focus event
      window.dispatchEvent(new Event('focus'));
      this.log('✅ Focus event simulated', 'success');
      
      // Simulate visibility change
      Object.defineProperty(document, 'hidden', { value: false, writable: true });
      document.dispatchEvent(new Event('visibilitychange'));
      this.log('✅ Visibility change simulated', 'success');
      
    } catch (error) {
      this.log(`❌ Network events test failed: ${error.message}`, 'error');
    }
  }

  generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    this.log(`\n📊 Test Report (Duration: ${duration}ms)`, 'info');
    this.log('='.repeat(50), 'info');
    
    const successCount = this.testResults.filter(r => r.type === 'success').length;
    const errorCount = this.testResults.filter(r => r.type === 'error').length;
    const warningCount = this.testResults.filter(r => r.type === 'warning').length;
    
    this.log(`✅ Successful: ${successCount}`, 'success');
    this.log(`❌ Errors: ${errorCount}`, 'error');
    this.log(`⚠️ Warnings: ${warningCount}`, 'warning');
    
    if (errorCount === 0) {
      this.log('🎉 All tests passed!', 'success');
    } else {
      this.log('🔧 Some tests failed - check the logs above', 'warning');
    }
    
    return {
      duration,
      successCount,
      errorCount,
      warningCount,
      results: this.testResults
    };
  }

  async runAllTests() {
    this.log('🚀 Starting Notification System Tests...', 'info');
    
    await this.testBasicFunctionality();
    await this.sleep(1000);
    
    const notificationId = await this.testCreateNotification();
    await this.sleep(2000);
    
    await this.testMarkAsRead(notificationId);
    await this.sleep(1000);
    
    await this.testCrossTabSync();
    await this.sleep(1000);
    
    await this.testNetworkEvents();
    await this.sleep(1000);
    
    return this.generateReport();
  }
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  window.NotificationTester = NotificationTester;
  
  // Provide easy access
  window.testNotifications = async () => {
    const tester = new NotificationTester();
    return await tester.runAllTests();
  };
  
  console.log('🧪 Notification Tester loaded!');
  console.log('Run: testNotifications() to start testing');
}
