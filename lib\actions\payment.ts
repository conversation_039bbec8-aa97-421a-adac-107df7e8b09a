"use server";

import { prisma } from "@/lib/config/prisma";
import { PaymentResult } from "@/lib/types/payment";
import { calculateDeposit } from "@/lib/utils/calculate";
import { getUserStatus } from "./user";
import { notifyAllAdmins } from "@/lib/notifications";
import MidtransService from "@/lib/services/midtrans";

export async function createPayment(
    prevState: unknown,
    formData: FormData
): Promise<PaymentResult> {
    try {
        const rentalId = formData.get("rentalId") as string;
        const amount = Number(formData.get("amount"));

        const rental = await prisma.rental.findUnique({
            where: { id: rentalId },
            include: {
                user: true,
                product: true,
            },
        });

        if (!rental) {
            return {
                success: false,
                message: "Data rental tidak ditemukan"
            };
        }

        // Cek status user (dikenal atau tidak)
        const { isKnownUser } = await getUserStatus();
        
        // Hitung deposit berdasarkan status user
        const depositAmount = calculateDeposit(amount, isKnownUser);
        
        // Jika user dikenal, pembayaran penuh dilakukan nanti (invoice)
        // Jika user tidak dikenal, buat payment untuk deposit
        if (!isKnownUser) {
            const payment = await MidtransService.createPayment({
                orderId: rental.id,
                amount: depositAmount,
                name: rental.user.name || "",
                email: rental.user.email || "",
                productName: `Deposit ${rental.product.name}`
            });

            if (!payment.token) {
                return {
                    success: false,
                    message: "Gagal mendapatkan token pembayaran"
                };
            }

            // Kirim notifikasi pembayaran baru ke admin
            await notifyAllAdmins({
                title: "Pembayaran Baru",
                message: `Pembayaran baru sebesar Rp ${depositAmount.toLocaleString()} untuk rental ${rental.product.name} telah dibuat.`,
                type: "new_payment"
            });

            return {
                success: true,
                message: "Success",
                snapToken: payment.token,
            };
        } else {
            // Untuk user dikenal, cukup buat invoice
            // Buat entri payment dengan status "invoice_issued"
            await prisma.payment.create({
                data: {
                    rentalId: rental.id,
                    userId: rental.userId,
                    amount: amount,
                    deposit: 0,  // Tidak ada deposit untuk user dikenal
                    remaining: amount,
                    status: "INVOICE_ISSUED",
                }
            });

            // Kirim notifikasi pembayaran baru ke admin
            await notifyAllAdmins({
                title: "Pembayaran Baru",
                message: `Invoice telah dibuat untuk rental ${rental.product.name} sebesar Rp ${amount.toLocaleString()}`,
                type: "new_invoice"
            });

            return {
                success: true,
                message: "Invoice telah dibuat",
                invoiceId: rental.id,
            };
        }
    } catch (error) {
        console.error("Payment error:", error);
        return {
            success: false,
            message: "Terjadi kesalahan sistem"
        };
    }
}

export async function processPayment(snapToken: string) {
  try {
    if (!snapToken) {
      throw new Error('Token pembayaran tidak valid');
    }
    
    return { success: true, snapToken: snapToken as string };
  } catch (error) {
    console.error('Payment processing error:', error);
    return { success: false, error: 'Gagal memproses pembayaran', snapToken: '' };
  }
}
