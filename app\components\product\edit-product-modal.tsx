"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { toast } from "sonner";
import { Card } from "@/app/components/ui/card";
import { Package } from "lucide-react";
import { Product } from "@/lib/types/product";
import { ProductFormFields } from "./product-form-fields";
import { ProductStatus } from "@prisma/client";
import UploadForm from '../shared/upload-form';

interface EditProductModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export function EditProductModal({ product, isOpen, onClose, onUpdate }: EditProductModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(product.imageUrl);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      const formDataObj = Object.fromEntries(formData);
      
      const response = await fetch(`/api/products/${product.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formDataObj.name,
          description: formDataObj.description,
          price: parseInt(formDataObj.price as string),
          stock: parseInt(formDataObj.stock as string),
          capacity: parseInt(formDataObj.capacity as string),
          status: formDataObj.status as ProductStatus,
          overtimeRate: formDataObj.overtimeRate ? parseInt(formDataObj.overtimeRate as string) : null,
          imageUrl: imageUrl, // Gunakan imageUrl dari state
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(error || "Gagal mengupdate produk");
      }

      toast.success("Produk berhasil diupdate");
      onUpdate();
      onClose();
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error(error instanceof Error ? error.message : "Gagal mengupdate produk");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[450px] max-h-[90vh] overflow-y-auto dark:bg-gray-900 dark:border-gray-800">
        <DialogHeader>
          <div className="flex flex-col items-center space-y-3 pb-4">
            <div className="h-14 w-14 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center">
              <Package className="h-7 w-7 text-primary dark:text-primary-foreground" />
            </div>
            <div className="text-center">
              <DialogTitle className="text-xl font-bold text-primary dark:text-primary-foreground">
                Edit Produk
              </DialogTitle>
              <DialogDescription className="text-xs text-muted-foreground dark:text-gray-400">
                ID: {product.id}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Card className="border border-border/50 shadow-sm dark:bg-gray-800 dark:border-gray-700">
          <form onSubmit={handleSubmit} className="space-y-4 p-4">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Gambar Produk
              </label>
              <UploadForm
                onImageUploaded={(url) => setImageUrl(url)}
                currentImageUrl={imageUrl}
              />
            </div>
            
            <ProductFormFields product={product} />

            <div className="flex justify-end gap-2 pt-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                className="dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                Batal
              </Button>
              <Button 
                type="submit" 
                disabled={isLoading}
                className="dark:text-gray-100"
              >
                {isLoading ? "Menyimpan..." : "Simpan"}
              </Button>
            </div>
          </form>
        </Card>
      </DialogContent>
    </Dialog>
  );
} 
