'use client';

import { But<PERSON> } from "@/app/components/ui/button";

interface ErrorBoundaryProps {
  error: Error;
  reset: () => void;
  message?: string;
}

export function ErrorBoundary({ 
  error, 
  reset,
  message = "Terja<PERSON> kesalahan"
}: ErrorBoundaryProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">
          {message}
        </h2>
        <p className="text-gray-600 mb-4">
          {error.message || 'Ter<PERSON><PERSON> kesalahan yang tidak diketahui'}
        </p>
        <Button
          onClick={reset}
          variant="default"
        >
          Coba Lagi
        </Button>
      </div>
    </div>
  );
} 
