import { Badge } from "@/app/components/ui/badge";
import { CheckCircle, Clock, Power, XCircle } from "lucide-react";
import { getOperationStatus, getOperationStatusInfo, type OperationStatus } from "@/lib/utils/operation-status";

type StatusType = OperationStatus | string;

interface OperationStatusBadgeProps {
  status?: StatusType;
  rental?: {
    operationalStart?: Date | string | null;
    operationalEnd?: Date | string | null;
    status?: string;
  };
  size?: "default" | "sm";
}

export function OperationStatusBadge({ status, rental, size = "default" }: OperationStatusBadgeProps) {
  // Determine status from rental data if not provided directly
  let operationStatus: OperationStatus;

  if (rental) {
    operationStatus = getOperationStatus(rental);
  } else if (status && ["pending", "running", "completed", "cancelled"].includes(status)) {
    operationStatus = status as OperationStatus;
  } else {
    // Fallback for unknown status
    operationStatus = "pending";
  }

  const statusInfo = getOperationStatusInfo(operationStatus);

  // Icon mapping
  const iconMap = {
    pending: <Clock className="w-3.5 h-3.5 mr-1 text-yellow-300" />,
    running: <Power className="w-3.5 h-3.5 mr-1 text-green-300" />,
    completed: <CheckCircle className="w-3.5 h-3.5 mr-1 text-blue-300" />,
    cancelled: <XCircle className="w-3.5 h-3.5 mr-1 text-red-300" />
  };

  // Custom styling for consistent appearance
  const customClassMap = {
    pending: "bg-yellow-500 hover:bg-yellow-600 text-white dark:bg-yellow-600 dark:hover:bg-yellow-700",
    running: "bg-green-500 hover:bg-green-600 text-white dark:bg-green-600 dark:hover:bg-green-700",
    completed: "bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700",
    cancelled: "bg-red-500 hover:bg-red-600 text-white dark:bg-red-600 dark:hover:bg-red-700"
  };

  // Size classes
  const sizeClass = size === "sm"
    ? "text-xs py-0 px-2 h-5"
    : "text-xs py-1";

  return (
    <Badge
      variant="default"
      className={`flex items-center font-medium ${customClassMap[operationStatus]} ${sizeClass}`}
    >
      {iconMap[operationStatus]}
      {statusInfo.label}
    </Badge>
  );
}
