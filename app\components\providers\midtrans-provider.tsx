"use client";

import <PERSON><PERSON><PERSON> from "next/script";
import { useEffect, useState } from "react";

// Nonaktifkan console.log di lingkungan produksi
const logger = {
  log: (...args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      console.log(...args);
    }
  },
  error: (...args: unknown[]) => console.error(...args),
  warn: (...args: unknown[]) => console.warn(...args)
};

export function MidtransProvider() {
  const [isClientKeyAvailable, setIsClientKeyAvailable] = useState(true);
  const clientKey = process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY || "";
  
  useEffect(() => {
    // Log warning if client key is missing
    if (!clientKey) {
      setIsClientKeyAvailable(false);
      console.error("Midtrans client key tidak tersedia. Pastikan NEXT_PUBLIC_MIDTRANS_CLIENT_KEY ada di file .env.local");
    }

    // Memastikan snap sudah terpasang dengan benar
    const checkSnapAvailability = () => {
      if (window.snap) {
        logger.log("Midtrans Snap is available");
      } else {
        console.warn("Midtrans Snap is not available yet. Retrying...");
        // Batasi percobaan ulang ke 5 kali saja untuk menghindari infinite loop
        let retryCount = 0;
        const maxRetries = 5;

        const retryCheck = () => {
          if (retryCount < maxRetries) {
            retryCount++;
            setTimeout(checkSnapAvailability, 1000);
          } else {
            console.error("Midtrans Snap failed to load after multiple retries");
          }
        };

        retryCheck();
      }
    };

    // Jalankan pengecekan setelah halaman dimuat
    if (typeof window !== 'undefined') {
      // Gunakan setTimeout daripada event listener untuk memastikan kode dijalankan
      setTimeout(checkSnapAvailability, 2000);
    }
  }, [clientKey]);

  if (!isClientKeyAvailable) {
    return null; // Don't render the script if client key is missing
  }

  return (
    <>
      <Script
        src="https://app.sandbox.midtrans.com/snap/snap.js"
        data-client-key={clientKey}
        strategy="afterInteractive"
        onError={(e) => {
          console.error("Error loading Midtrans Snap script:", e);
          alert("Terjadi kesalahan saat memuat script Midtrans. Pastikan konfigurasi Midtrans sudah benar di file .env.local");
        }}
        onLoad={() => logger.log("Midtrans Snap script loaded successfully")}
      />
      <Script id="midtrans-setup" strategy="afterInteractive">
        {`
          window.snapExecute = function(token) {
            ${process.env.NODE_ENV !== 'production' ? 'console.log("Executing snap with token:", token);' : ''}
            if (window.snap) {
              try {
                window.snap.pay(token, {
                  onSuccess: function(result) {
                    ${process.env.NODE_ENV !== 'production' ? 'console.log("success", result);' : ''}
                    fetch('/api/payments/' + result.order_id + '/status', {
                      method: 'PATCH',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ status: 'paid' })
                    }).then(() => {
                      // Don't auto-reload, let GSAP animation handle the flow
                      console.log("Payment status updated successfully");
                    })
                    .catch(err => {
                      console.error("Error updating payment status:", err);
                      alert("Pembayaran berhasil, tapi gagal memperbarui status. Silakan refresh halaman.");
                    });
                  },
                  onPending: function(result) {
                    ${process.env.NODE_ENV !== 'production' ? 'console.log("pending", result);' : ''}
                    // Don't auto-reload for pending, let user handle it manually
                    console.log("Payment pending, no auto-reload");
                  },
                  onError: function(result) {
                    console.error('error', result);
                    alert('Pembayaran gagal: ' + (result.message || 'Terjadi kesalahan'));
                  },
                  onClose: function() {
                    ${process.env.NODE_ENV !== 'production' ? 'console.log("customer closed the popup");' : ''}
                  }
                });
              } catch (error) {
                console.error("Error executing snap.pay:", error);
                alert("Terjadi kesalahan saat menjalankan pembayaran. Silakan coba lagi.");
              }
            } else {
              console.error("Snap not initialized. Please refresh the page and try again.");
              alert("Sistem pembayaran belum siap. Mohon refresh halaman dan coba lagi.");
            }
          }
        `}
      </Script>
    </>
  );
} 
