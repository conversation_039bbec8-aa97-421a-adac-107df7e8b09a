"use server";

import { prisma } from "@/lib/config/prisma";

// Enum values sebagai string literals
const NotificationTypes = {
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  NEW_RENTAL: 'NEW_RENTAL',
  RENTAL_CONFIRMED: 'RENTAL_CONFIRMED',
  OPERATION_STARTED: 'OPERATION_STARTED',
  OPERATION_COMPLETED: 'OPERATION_COMPLETED',
  LOW_STOCK: 'LOW_STOCK',
  OVERTIME_DETECTED: 'OVERTIME_DETECTED',
  NEW_PAYMENT: 'NEW_PAYMENT',
  NEW_INVOICE: 'NEW_INVOICE'
} as const;

type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];

/**
 * Menambahkan notifikasi ke database untuk user tertentu
 */
export async function addNotification({
  userId,
  title,
  message,
  type
}: {
  userId: string;
  title: string;
  message: string;
  type: string;
}) {
  try {
    // Validasi tipe notifikasi
    const validatedType = validateNotificationType(type);
    if (!validatedType.success) {
      return { success: false, error: validatedType.error || "Tipe notifikasi tidak valid" };
    }

    // Pada titik ini, type sudah pasti ada
    const notificationType = validatedType.type!;

    const notification = await prisma.notification.create({
      data: {
        userId,
        title,
        message,
        type: notificationType,
        isRead: false
      }
    });
    
    return { success: true, notification };
  } catch (error) {
    console.error("[ADD_NOTIFICATION_ERROR]", error);
    return { success: false, error: "Gagal menambahkan notifikasi" };
  }
}

/**
 * Memvalidasi dan mengkonversi string ke NotificationType
 */
function validateNotificationType(type: string): { success: boolean, type?: NotificationType, error?: string } {
  const normalizedType = type.toUpperCase();
  
  if (Object.values(NotificationTypes).includes(normalizedType as NotificationType)) {
    return { success: true, type: normalizedType as NotificationType };
  }
  
  // Handle kasus khusus untuk backward compatibility
  switch (type.toLowerCase()) {
    case 'payment_success':
      return { success: true, type: NotificationTypes.PAYMENT_SUCCESS };
    case 'payment_failed':
    case 'payment_failure':
      return { success: true, type: NotificationTypes.PAYMENT_FAILED };
    case 'new_rental':
      return { success: true, type: NotificationTypes.NEW_RENTAL };
    case 'rental_confirmed':
    case 'confirm_rental':
      return { success: true, type: NotificationTypes.RENTAL_CONFIRMED };
    case 'operation_started':
      return { success: true, type: NotificationTypes.OPERATION_STARTED };
    case 'operation_completed':
    case 'operation_done':
      return { success: true, type: NotificationTypes.OPERATION_COMPLETED };
    case 'low_stock':
      return { success: true, type: NotificationTypes.LOW_STOCK };
    case 'overtime_detected':
    case 'overtime':
      return { success: true, type: NotificationTypes.OVERTIME_DETECTED };
    case 'payment_received':
    case 'new_payment':
      return { success: true, type: NotificationTypes.NEW_PAYMENT };
    case 'new_invoice':
    case 'invoice_created':
      return { success: true, type: NotificationTypes.NEW_INVOICE };
    default:
      return { success: false, error: `Tipe notifikasi '${type}' tidak valid` };
  }
}

/**
 * Menambahkan notifikasi ke semua admin
 */
export async function notifyAllAdmins({
  title,
  message,
  type
}: {
  title: string;
  message: string;
  type: string;
}) {
  try {
    // Validasi tipe notifikasi
    const validatedType = validateNotificationType(type);
    if (!validatedType.success) {
      return { success: false, error: validatedType.error || "Tipe notifikasi tidak valid" };
    }

    // Pada titik ini, type sudah pasti ada
    const notificationType = validatedType.type!;

    // Cari semua admin
    const admins = await prisma.user.findMany({
      where: {
        role: "ADMIN"
      },
      select: {
        id: true
      }
    });
    
    // Buat notifikasi untuk setiap admin
    const notifications = await Promise.all(
      admins.map(admin => 
        prisma.notification.create({
          data: {
            userId: admin.id,
            title,
            message,
            type: notificationType,
            isRead: false
          }
        })
      )
    );
    
    return { success: true, count: notifications.length };
  } catch (error) {
    console.error("[NOTIFY_ALL_ADMINS_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi ke admin" };
  }
}

/**
 * Notifikasi untuk pemberitahuan stok hampir habis
 */
export async function notifyLowStock(productId: string, currentStock: number) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { 
        name: true,
        userId: true 
      }
    });
    
    if (!product) return { success: false, error: "Produk tidak ditemukan" };
    
    // Kirim notifikasi ke pemilik produk (admin)
    await addNotification({
      userId: product.userId,
      title: "Stok Produk Hampir Habis",
      message: `Produk "${product.name}" hanya tersisa ${currentStock} unit. Harap segera tambahkan stok produk.`,
      type: NotificationTypes.LOW_STOCK
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_LOW_STOCK_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi stok hampir habis" };
  }
}

/**
 * Notifikasi pembayaran berhasil untuk user
 */
export async function notifyPaymentSuccess(userId: string, paymentId: string, amount: number, rentalInfo: string) {
  try {
    await addNotification({
      userId,
      title: "Pembayaran Berhasil",
      message: `Pembayaran sebesar Rp ${amount.toLocaleString()} untuk ${rentalInfo} telah berhasil diproses.`,
      type: NotificationTypes.PAYMENT_SUCCESS
    });
    
    // Kirim notifikasi ke admin
    await notifyAllAdmins({
      title: "Pembayaran Diterima",
      message: `Pembayaran ID: ${paymentId} sebesar Rp ${amount.toLocaleString()} telah berhasil diterima.`,
      type: NotificationTypes.NEW_PAYMENT
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_PAYMENT_SUCCESS_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi pembayaran berhasil" };
  }
}

/**
 * Notifikasi pembayaran gagal untuk user
 */
export async function notifyPaymentFailed(userId: string, paymentId: string, amount: number, rentalInfo: string) {
  try {
    await addNotification({
      userId,
      title: "Pembayaran Gagal",
      message: `Pembayaran sebesar Rp ${amount.toLocaleString()} untuk ${rentalInfo} gagal diproses. Silakan coba lagi atau hubungi layanan pelanggan.`,
      type: NotificationTypes.PAYMENT_FAILED
    });
    
    // Kirim notifikasi ke admin
    await notifyAllAdmins({
      title: "Pembayaran Gagal",
      message: `Pembayaran ID: ${paymentId} sebesar Rp ${amount.toLocaleString()} gagal diproses.`,
      type: NotificationTypes.PAYMENT_FAILED
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_PAYMENT_FAILED_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi pembayaran gagal" };
  }
}

/**
 * Notifikasi overtime untuk user
 */
export async function notifyOvertime(userId: string, rentalId: string, productName: string, overtimeFee: number) {
  try {
    await addNotification({
      userId,
      title: "Biaya Overtime Terdeteksi",
      message: `Penyewaan ${productName} Anda telah melebihi waktu yang ditentukan. Biaya overtime sebesar Rp ${overtimeFee.toLocaleString()} telah ditambahkan.`,
      type: NotificationTypes.OVERTIME_DETECTED
    });
    
    // Kirim notifikasi ke admin
    await notifyAllAdmins({
      title: "Overtime Terdeteksi",
      message: `Penyewaan ID: ${rentalId} untuk ${productName} telah melebihi waktu. Biaya overtime: Rp ${overtimeFee.toLocaleString()}.`,
      type: NotificationTypes.OVERTIME_DETECTED
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_OVERTIME_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi overtime" };
  }
}

/**
 * Notifikasi operasi dimulai untuk user
 */
export async function notifyOperationStarted(userId: string, rentalId: string, productName: string) {
  try {
    await addNotification({
      userId,
      title: "Operasi Dimulai",
      message: `Operasi untuk penyewaan ${productName} telah dimulai. Tim kami sudah berada di lokasi.`,
      type: NotificationTypes.OPERATION_STARTED
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_OPERATION_STARTED_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi operasi dimulai" };
  }
}

/**
 * Notifikasi operasi selesai untuk user
 */
export async function notifyOperationCompleted(userId: string, rentalId: string, productName: string) {
  try {
    await addNotification({
      userId,
      title: "Operasi Selesai",
      message: `Operasi untuk penyewaan ${productName} telah selesai. Terima kasih atas kepercayaan Anda menggunakan layanan kami.`,
      type: NotificationTypes.OPERATION_COMPLETED
    });
    
    return { success: true };
  } catch (error) {
    console.error("[NOTIFY_OPERATION_COMPLETED_ERROR]", error);
    return { success: false, error: "Gagal mengirim notifikasi operasi selesai" };
  }
} 
