import { 
  NotificationRepository, 
  NotificationTypes 
} from "../data/repositories/notification-repository";
import { Notification } from "@prisma/client";

export class NotificationService {
  private notificationRepo: NotificationRepository;

  constructor() {
    this.notificationRepo = new NotificationRepository();
  }

  async createPaymentSuccessNotification(userId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Pembayaran Berhasil",
      message: `Pembayaran rental ID: ${rentalId} telah berhasil diproses`,
      type: NotificationTypes.PAYMENT_SUCCESS
    });
  }

  async createPaymentFailedNotification(userId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Pembayaran Gagal",
      message: `Pembayaran rental ID: ${rentalId} tidak berhasil diproses`,
      type: NotificationTypes.PAYMENT_FAILED
    });
  }

  async createNewRentalNotification(userId: string, adminId: string, rentalId: string): Promise<void> {
    // Notifikasi untuk admin
    await this.notificationRepo.create({
      userId: adminId,
      title: "Permintaan Rental Baru",
      message: `Permintaan rental baru (ID: ${rentalId}) memerlukan persetujuan Anda`,
      type: NotificationTypes.NEW_RENTAL
    });

    // Notifikasi untuk pengguna
    await this.notificationRepo.create({
      userId,
      title: "Permintaan Rental Dikirim",
      message: `Permintaan rental (ID: ${rentalId}) Anda sedang menunggu persetujuan admin`,
      type: NotificationTypes.NEW_RENTAL
    });
  }

  async createRentalConfirmedNotification(userId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Rental Dikonfirmasi",
      message: `Permintaan rental (ID: ${rentalId}) Anda telah dikonfirmasi oleh admin`,
      type: NotificationTypes.RENTAL_CONFIRMED
    });
  }

  async createOperationStartedNotification(userId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Operasi Dimulai",
      message: `Operasi untuk rental (ID: ${rentalId}) telah dimulai`,
      type: NotificationTypes.OPERATION_STARTED
    });
  }

  async createOperationCompletedNotification(userId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Operasi Selesai",
      message: `Operasi untuk rental (ID: ${rentalId}) telah selesai`,
      type: NotificationTypes.OPERATION_COMPLETED
    });
  }

  async createLowStockNotification(adminId: string, productId: string, stock: number): Promise<void> {
    await this.notificationRepo.create({
      userId: adminId,
      title: "Stok Hampir Habis",
      message: `Produk (ID: ${productId}) memiliki stok yang rendah: ${stock} unit tersisa`,
      type: NotificationTypes.LOW_STOCK
    });
  }

  async createOvertimeNotification(userId: string, rentalId: string, overtimeHours: number): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Waktu Rental Terlewat",
      message: `Rental Anda (ID: ${rentalId}) telah melewati batas waktu sebanyak ${overtimeHours} jam`,
      type: NotificationTypes.OVERTIME_DETECTED
    });
  }

  async createNewPaymentNotification(adminId: string, paymentId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId: adminId,
      title: "Pembayaran Baru",
      message: `Pembayaran baru untuk rental (ID: ${rentalId}) memerlukan perhatian Anda`,
      type: NotificationTypes.NEW_PAYMENT
    });
  }

  async createNewInvoiceNotification(userId: string, paymentId: string, rentalId: string): Promise<void> {
    await this.notificationRepo.create({
      userId,
      title: "Invoice Baru",
      message: `Invoice baru untuk rental (ID: ${rentalId}) telah dibuat`,
      type: NotificationTypes.NEW_INVOICE
    });
  }

  async getUserNotifications(userId: string, limit?: number): Promise<{ items: Notification[], unreadCount: number }> {
    const notifications = await this.notificationRepo.findByUserId(userId, { limit });
    const unreadCount = await this.notificationRepo.getUnreadCount(userId);
    
    return {
      items: notifications,
      unreadCount
    };
  }

  async markAsRead(userId: string, notificationId: string): Promise<Notification> {
    const notification = await this.notificationRepo.findById(notificationId);
    
    if (!notification || notification.userId !== userId) {
      throw new Error("Notifikasi tidak ditemukan atau tidak diizinkan");
    }
    
    return this.notificationRepo.markAsRead(notificationId);
  }

  async markAllAsRead(userId: string): Promise<{ count: number }> {
    return this.notificationRepo.markAllAsRead(userId);
  }
} 
