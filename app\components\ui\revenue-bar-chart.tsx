"use client"

import * as React from "react"
import { 
  Bar, 
  BarChart, 
  ResponsiveContainer, 
  XAxis, 
  YAxis, 
  Tooltip,
  TooltipProps
} from "recharts"

interface RevenueBarChartProps {
  data: { month: string; revenue: number }[]
}

export function RevenueBarChart({ data }: RevenueBarChartProps) {
  const formatter = new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    maximumFractionDigits: 0
  });

  // Kustomisasi tooltip untuk menampilkan nilai lengkap
  const CustomTooltip = ({ 
    active, 
    payload, 
    label 
  }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm dark:border-gray-700">
          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                Bulan
              </span>
              <span className="font-bold text-foreground">{label}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                Pendapatan
              </span>
              <span className="font-bold text-blue-600 dark:text-blue-400">
                {formatter.format(payload[0].value as number)}
              </span>
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  // Deteksi tema saat ini
  const [theme, setTheme] = React.useState<'light' | 'dark'>('light');
  
  // Efek untuk memantau perubahan tema
  React.useEffect(() => {
    // Deteksi tema saat ini
    const isDark = document.documentElement.classList.contains('dark');
    setTheme(isDark ? 'dark' : 'light');
    
    // Pantau perubahan tema
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'class') {
          const isDarkNow = document.documentElement.classList.contains('dark');
          setTheme(isDarkNow ? 'dark' : 'light');
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);

  // Sesuaikan warna berdasarkan tema
  const axisColor = theme === 'dark' ? '#9ca3af' : '#888888';
  const cursorColor = theme === 'dark' ? 'rgba(45, 55, 72, 0.6)' : 'rgba(0, 0, 0, 0.05)';
  const barColor = theme === 'dark' ? '#3b82f6' : '#4f46e5';

  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <XAxis
          dataKey="month"
          stroke={axisColor}
          fontSize={12}
          tickLine={false}
          axisLine={false}
          style={{ fill: theme === 'dark' ? '#d1d5db' : '#6b7280' }}
        />
        <YAxis
          stroke={axisColor}
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => formatter.format(value)}
          style={{ fill: theme === 'dark' ? '#d1d5db' : '#6b7280' }}
        />
        <Tooltip 
          content={<CustomTooltip />}
          cursor={{ fill: cursorColor }}
        />
        <Bar
          dataKey="revenue"
          fill={barColor}
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
} 
