"use client";

import { cn } from "@/lib/utils";
import {
  DesktopNavigationSkeleton,
  TabletNavigationSkeleton,
  MobileNavigationSkeleton,
  MobileBottomNavigationSkeleton,
  UserProfileSkeleton
} from "./navigation-skeleton";

// Complete layout loading component
export function UserLayoutLoading({ className }: { className?: string }) {
  return (
    <div className={cn("flex min-h-screen overflow-hidden", className)}>
      {/* Desktop Sidebar Skeleton (≥1024px) */}
      <aside className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0 z-50">
        <div className="flex flex-col flex-1 min-h-0 bg-white/95 backdrop-blur-xl border-r border-violet-200/60 shadow-xl dark:bg-slate-900/95 dark:border-violet-700/60">
          {/* Header Skeleton */}
          <div className="flex items-center h-20 px-6 border-b border-slate-200/60 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 dark:from-violet-800 dark:via-purple-800 dark:to-blue-800">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-2xl bg-white/25 animate-pulse" />
              <div className="flex flex-col gap-1">
                <div className="h-5 w-32 bg-white/25 rounded animate-pulse" />
                <div className="h-3 w-28 bg-white/20 rounded animate-pulse" />
              </div>
            </div>
          </div>

          {/* Search Bar Skeleton */}
          <div className="px-4 py-6">
            <div className="h-12 w-full rounded-xl bg-violet-100/80 dark:bg-violet-900/30 animate-pulse" />
          </div>

          {/* Navigation Menu Skeleton */}
          <div className="px-4 flex-1">
            <div className="mb-6">
              <div className="h-4 w-20 mb-4 bg-violet-100/60 dark:bg-violet-900/20 rounded animate-pulse" />
              <DesktopNavigationSkeleton />
            </div>
          </div>

          {/* User Profile Skeleton */}
          <UserProfileSkeleton />
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 lg:pl-80">
        {/* Header Skeleton */}
        <header className="sticky top-0 z-40 bg-white/98 backdrop-blur-xl border-b border-violet-200/60 dark:bg-slate-900/98 dark:border-violet-700/60 shadow-lg">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button Skeleton */}
              <MobileNavigationSkeleton />
              
              {/* Tablet Navigation Skeleton */}
              <TabletNavigationSkeleton />
              
              {/* Title Skeleton */}
              <div className="hidden lg:flex flex-col gap-1">
                <div className="h-5 w-28 bg-gray-200 dark:bg-gray-600 rounded animate-pulse" />
                <div className="h-3 w-20 bg-violet-100 dark:bg-violet-900/30 rounded animate-pulse" />
              </div>
            </div>

            {/* Header Actions Skeleton */}
            <div className="flex items-center gap-3">
              {/* Desktop Actions */}
              <div className="hidden lg:flex items-center gap-2">
                <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 animate-pulse" />
                <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 animate-pulse" />
              </div>

              {/* Mobile User Info Skeleton */}
              <div className="lg:hidden flex items-center gap-2">
                <div className="w-8 h-8 rounded-xl bg-violet-100 dark:bg-violet-900/30 animate-pulse" />
                <div className="flex items-center gap-1">
                  <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 animate-pulse" />
                  <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-600 animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Skeleton */}
        <main className="flex-1 overflow-y-auto pb-24 md:pb-10">
          <div className="px-4 sm:px-6 lg:px-8 py-6 max-w-7xl mx-auto">
            {/* Page Content Skeleton */}
            <div className="space-y-6">
              {/* Header Section */}
              <div className="bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-800">
                <div className="h-8 w-64 mb-3 bg-violet-100/80 dark:bg-violet-900/30 rounded animate-pulse" />
                <div className="h-4 w-96 mb-2 bg-violet-50/80 dark:bg-violet-950/20 rounded animate-pulse" />
                <div className="h-4 w-80 bg-violet-50/80 dark:bg-violet-950/20 rounded animate-pulse" />
              </div>

              {/* Content Cards */}
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-600 animate-pulse" />
                      <div className="h-6 w-16 rounded-full bg-gray-100 dark:bg-gray-700 animate-pulse" />
                    </div>
                    <div className="h-6 w-24 mb-2 bg-gray-200 dark:bg-gray-600 rounded animate-pulse" />
                    <div className="h-4 w-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>

        {/* Mobile Bottom Navigation Skeleton */}
        <MobileBottomNavigationSkeleton />
      </div>
    </div>
  );
}

// Simplified loading component for quick use
export function NavigationLoadingSimple({ className }: { className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      {/* Desktop */}
      <DesktopNavigationSkeleton className="hidden lg:block" />
      
      {/* Tablet */}
      <TabletNavigationSkeleton className="hidden md:block lg:hidden" />
      
      {/* Mobile */}
      <MobileNavigationSkeleton className="md:hidden" />
    </div>
  );
}

// Loading component with shimmer effects
export function NavigationLoadingShimmer({ className }: { className?: string }) {
  return (
    <div className={cn("w-full", className)}>
      <style jsx>{`
        @keyframes shimmer {
          0% { background-position: -200px 0; }
          100% { background-position: calc(200px + 100%) 0; }
        }
      `}</style>
      
      <div className="space-y-2">
        {[
          { name: "Dashboard", width: "w-20" },
          { name: "Katalog", width: "w-16" },
          { name: "Rental Saya", width: "w-28" }, // Longest
          { name: "Profil", width: "w-16" }
        ].map((item, index) => (
          <div key={index} className="flex items-center px-3 py-3 rounded-lg">
            <div className="mr-3 rounded-md p-2 bg-gray-100 dark:bg-gray-800">
              <div className="h-5 w-5 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%] animate-[shimmer_1.5s_ease-in-out_infinite] rounded" />
            </div>
            <div className="flex-1">
              <div 
                className={cn(
                  "h-4 mb-1 bg-gradient-to-r rounded",
                  item.width,
                  item.name === "Rental Saya" 
                    ? "from-violet-200 via-violet-300 to-violet-200 dark:from-violet-800 dark:via-violet-700 dark:to-violet-800" 
                    : "from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700",
                  "bg-[length:200%_100%] animate-[shimmer_1.5s_ease-in-out_infinite]"
                )}
                style={{ animationDelay: `${index * 100}ms` }}
              />
              <div className="h-3 w-32 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 bg-[length:200%_100%] animate-[shimmer_1.5s_ease-in-out_infinite] rounded" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
