"use client";

import { useState } from "react";
import Link from "next/link";
import {
    LuLayoutDashboard,
    LuUsers,
    LuBox,
    LuCreditCard
} from "react-icons/lu";

const menuItems = [
    {
        href: '/admin/dashboard',
        title: 'Dashboard',
        icon: LuLayoutDashboard
    },
    {
        href: '/admin/users',
        title: 'Users',
        icon: LuUsers
    },
    {
        href: '/admin/products',
        title: 'Products',
        icon: LuBox
    },
    {
        href: '/admin/payments',
        title: 'Payments',
        icon: LuCreditCard
    }
];

export function MobileMenu() {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="md:hidden">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="p-2 text-gray-600 hover:text-gray-900"
            >
                <span className="sr-only">Open menu</span>
                {/* Menu icon */}
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>

            <div className={`${isOpen ? 'block' : 'hidden'} fixed inset-0 z-50`}>
                <div className="bg-white h-full">
                    <nav className="mt-16 p-4">
                        {menuItems.map((item) => {
                            const Icon = item.icon;
                            return (
                                <Link
                                    key={item.href}
                                    href={item.href}
                                    className="flex items-center p-4 text-gray-600 hover:bg-gray-50 rounded-lg"
                                    onClick={() => setIsOpen(false)}
                                >
                                    <Icon className="w-5 h-5 mr-3" />
                                    <span>{item.title}</span>
                                </Link>
                            );
                        })}
                    </nav>
                </div>
            </div>
        </div>
    );
} 
