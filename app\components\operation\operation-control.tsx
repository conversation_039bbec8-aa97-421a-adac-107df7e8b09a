"use client";

import { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { CountdownTimer } from "@/app/components/operation/countdown-timer";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";

// Interface yang lebih spesifik untuk komponen ini
export interface OperationRental {
  id: string;
  status: string;
  startDate: Date | string;
  operationalStart?: Date | string | null;
  operationalEnd?: Date | string | null;
  duration: number | string;
  payment?: {
    deposit: number;
    remaining: number;
    overtime: number | null;
    status: string;
  } | null;
}

interface OperationControlProps {
  rental: OperationRental;
  isAdmin?: boolean;
}

export function OperationControl({ rental, isAdmin = false }: OperationControlProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { showSuccess, showError } = useToast();

  // Fungsi untuk mendeteksi apakah operasi sudah dimulai
  const hasStarted = !!rental.operationalStart;
                    
  // Fungsi untuk mendeteksi apakah operasi sudah selesai
  const isCompleted = !!rental.operationalEnd;
  
  // Fungsi untuk memulai operasi
  const handleStartOperation = async () => {
    try {
      setIsLoading(true);
      
      // Gunakan waktu saat ini sebagai waktu mulai
      const currentTime = new Date();
      const startTimeISO = currentTime.toISOString();
      
      const response = await fetch(`/api/operations/${rental.id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          action: "start",
          startTime: startTimeISO
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || error.error || "Gagal memulai operasi");
      }

      showSuccess("Operasi dimulai. Operasi genset berhasil dimulai");

      router.refresh();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal memulai operasi";
      showError(`Terjadi kesalahan. ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fungsi untuk menghentikan operasi
  const handleStopOperation = async () => {
    try {
      setIsLoading(true);
      
      // Simpan status jeda timer ke server
      try {
        await fetch(`/api/operations/pause-timer`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            rentalId: rental.id,
            isPaused: true, // Paksa status jeda untuk menghentikan timer
            pauseOffset: 0,
            pauseStartTime: Date.now()
          }),
        });
      } catch (timerError) {
        console.error("Error stopping timer:", timerError);
        // Lanjutkan proses meskipun timer gagal dihentikan
      }

      // Gunakan waktu saat ini sebagai waktu selesai
      const currentTime = new Date();
      const endTimeISO = currentTime.toISOString();
      
      const response = await fetch(`/api/operations/${rental.id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          action: "end",
          endTime: endTimeISO
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || error.error || "Gagal menghentikan operasi");
      }

      await response.json();
      
      showSuccess("Operasi dihentikan. Operasi genset telah selesai");

      // Redirect ke halaman kelola pembayaran (harus sesuai dengan struktur rute di aplikasi)
      router.push(`/admin/payments?rental=${rental.id}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Gagal menghentikan operasi";
      showError(`Terjadi kesalahan. ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Kontrol Operasi</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {hasStarted ? (
          <div className="space-y-4">
            {!isCompleted ? (
              <>
                <div className="text-center p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                  <CountdownTimer
                    operationalStart={new Date(rental.operationalStart || rental.startDate)}
                    duration={rental.duration.toString()}
                    rentalId={rental.id}
                    showPauseButton={isAdmin}
                  />
                </div>
                  
                  {isAdmin && (
                  <div className="flex justify-center">
                    <Button 
                      variant="destructive"
                      onClick={handleStopOperation}
                      disabled={isLoading}
                    >
                      {isLoading ? "Menghentikan..." : "Hentikan Operasi"}
                    </Button>
                  </div>
                  )}
              </>
            ) : (
              <div className="text-center p-6 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-lg text-green-700 font-medium mb-2">Operasi telah selesai</p>
                <p className="text-sm text-green-600">
                  {rental.operationalEnd ? 
                    `Selesai pada: ${new Date(rental.operationalEnd).toLocaleString()}` :
                    "Operasi telah dihentikan"
                  }
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center p-6">
            <p className="text-lg text-gray-500 mb-4">
              Operasi belum dimulai
            </p>
            
            {isAdmin && (
              <Button 
                onClick={handleStartOperation}
                disabled={isLoading}
              >
                {isLoading ? "Memulai..." : "Mulai Operasi"}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
