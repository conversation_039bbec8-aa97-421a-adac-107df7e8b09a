import { Rental } from '@prisma/client';
import { db } from '../db';

// Define rental status literals
type RentalStatus = 'PENDING' | 'CONFIRMED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';

export class RentalRepository {
  async findById(id: string, include?: { product?: boolean, user?: boolean, payment?: boolean }): Promise<Rental | null> {
    return db.rental.findUnique({
      where: { id },
      include: {
        product: include?.product || false,
        user: include?.user || false,
        payment: include?.payment || false
      }
    });
  }

  async findAll(options?: {
    status?: RentalStatus,
    userId?: string,
    productId?: string,
    limit?: number,
    offset?: number,
    include?: { product?: boolean, user?: boolean, payment?: boolean }
  }): Promise<Rental[]> {
    return db.rental.findMany({
      where: {
        status: options?.status,
        userId: options?.userId,
        productId: options?.productId
      },
      take: options?.limit,
      skip: options?.offset,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        product: options?.include?.product || false,
        user: options?.include?.user || false,
        payment: options?.include?.payment || false
      }
    });
  }

  async create(data: Omit<Rental, 'id' | 'createdAt' | 'updatedAt'>): Promise<Rental> {
    return db.rental.create({
      data: {
        ...data,
        status: data.status || 'PENDING'
      }
    });
  }

  async update(id: string, data: Partial<Omit<Rental, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Rental> {
    return db.rental.update({
      where: { id },
      data
    });
  }

  async delete(id: string): Promise<Rental> {
    return db.rental.delete({
      where: { id }
    });
  }

  async updateStatus(id: string, status: RentalStatus): Promise<Rental> {
    return db.rental.update({
      where: { id },
      data: { status }
    });
  }

  async getRentalsByDateRange(startDate: Date, endDate: Date): Promise<Rental[]> {
    return db.rental.findMany({
      where: {
        OR: [
          {
            startDate: {
              gte: startDate,
              lte: endDate
            }
          },
          {
            endDate: {
              gte: startDate,
              lte: endDate
            }
          }
        ]
      },
      include: {
        product: true,
        payment: true
      }
    });
  }

  async getTotalRevenueByMonth(year: number): Promise<Array<{ month: number, revenue: number }>> {
    const rentals = await db.rental.findMany({
      where: {
        createdAt: {
          gte: new Date(`${year}-01-01`),
          lt: new Date(`${year + 1}-01-01`),
        },
        status: {
          in: ['COMPLETED', 'ACTIVE']
        }
      },
      select: {
        amount: true,
        createdAt: true
      }
    });

    const monthlyRevenue = Array(12).fill(0).map((_, i) => ({
      month: i + 1,
      revenue: 0
    }));

    rentals.forEach(rental => {
      const month = rental.createdAt.getMonth();
      monthlyRevenue[month].revenue += rental.amount;
    });

    return monthlyRevenue;
  }
} 
