import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth/server";

export async function GET(request: NextRequest) {
  try {
    const session = await getSession();
    
    return NextResponse.json({
      authenticated: !!session?.user,
      role: session?.user?.role || null,
      userId: session?.user?.id || null
    });
  } catch (error) {
    console.error("Auth check error:", error);
    return NextResponse.json({
      authenticated: false,
      role: null,
      userId: null
    });
  }
}
