"use client";

import { But<PERSON> } from "@/app/components/ui/button";
import { useAppSounds } from "@/lib/hooks/use-app-sounds";
import { ButtonHTMLAttributes, forwardRef } from "react";
import { cn } from "@/lib/utils";

interface SoundButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "gradient" | "violet" | "success";
  size?: "default" | "sm" | "lg" | "icon" | "mobile";
  soundType?: "click" | "success" | "error" | "none";
  loading?: boolean;
  children: React.ReactNode;
}

const SoundButton = forwardRef<HTMLButtonElement, SoundButtonProps>(
  ({ className, variant = "default", size = "default", soundType = "click", loading = false, onClick, children, ...props }, ref) => {
    const { sounds } = useAppSounds();

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      // Play sound based on type
      switch (soundType) {
        case "click":
          sounds.onButtonClick();
          break;
        case "success":
          sounds.onSuccess();
          break;
        case "error":
          sounds.onError();
          break;
        case "none":
          // No sound
          break;
        default:
          sounds.onButtonClick();
      }

      // Call original onClick if provided
      if (onClick) {
        onClick(event);
      }
    };

    return (
      <Button
        className={cn(className)}
        variant={variant}
        size={size}
        loading={loading}
        ref={ref}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Button>
    );
  }
);

SoundButton.displayName = "SoundButton";

export default SoundButton;
export { SoundButton };
