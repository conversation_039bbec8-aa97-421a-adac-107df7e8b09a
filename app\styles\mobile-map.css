/* Mobile Map Optimization CSS */

/* Optimasi untuk kontrol peta di mobile */
@media (max-width: 640px) {

  /* Perbesar area touch untuk kontrol zoom */
  .maplibregl-ctrl-group button {
    width: 44px !important;
    height: 44px !important;
    font-size: 18px !important;
    line-height: 44px !important;
    touch-action: manipulation !important;
  }

  /* Posisi kontrol yang lebih baik untuk mobile */
  .maplibregl-ctrl-top-right {
    top: 10px !important;
    right: 10px !important;
  }

  /* Styling untuk marker yang lebih baik di mobile */
  .custom-marker {
    z-index: 1000 !important;
    touch-action: manipulation !important;
  }

  /* Optimasi canvas peta untuk mobile */
  .maplibregl-canvas {
    touch-action: pan-x pan-y !important;
  }

  /* Sembunyikan atribusi di mobile untuk menghemat ruang */
  .maplibregl-ctrl-attrib {
    display: none !important;
  }

  /* Optimasi container peta */
  .maplibregl-map {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }
}

/* Optimasi untuk tablet */
@media (min-width: 641px) and (max-width: 1023px) {
  .maplibregl-ctrl-group button {
    width: 42px !important;
    height: 42px !important;
    font-size: 17px !important;
    line-height: 42px !important;
  }
}

/* Animasi untuk marker pulse */
@keyframes marker-pulse {
  0% {
    transform: translate(-50%, -50%) rotate(45deg) scale(0.8);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) rotate(45deg) scale(1.2);
    opacity: 0;
  }

  100% {
    transform: translate(-50%, -50%) rotate(45deg) scale(1.2);
    opacity: 0;
  }
}

/* Optimasi performa untuk animasi */
.marker-pulse {
  will-change: transform, opacity !important;
  animation: marker-pulse 2s infinite !important;
}

/* Optimasi touch untuk marker */
.custom-marker {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* Optimasi untuk loading state */
.map-loading-overlay {
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
}

/* Responsive text untuk koordinat */
@media (max-width: 480px) {
  .coordinate-display {
    font-size: 10px !important;
    padding: 6px 8px !important;
  }
}

/* Optimasi untuk dark mode */
@media (prefers-color-scheme: dark) {
  .maplibregl-ctrl-group button {
    background-color: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
  }

  .maplibregl-ctrl-group button:hover {
    background-color: #4b5563 !important;
  }
}

/* Optimasi untuk high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .custom-marker .marker-pin {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
  }
}

/* Accessibility improvements */
.maplibregl-ctrl-group button:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* Optimasi untuk landscape mode di mobile */
@media (max-width: 640px) and (orientation: landscape) {
  .map-container {
    height: 250px !important;
  }

  .maplibregl-ctrl-top-right {
    top: 5px !important;
    right: 5px !important;
  }
}

/* Mobile Modal Optimizations */
@media (max-width: 640px) {

  /* Full screen modal on mobile */
  .map-picker-modal {
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  /* Ensure modal content doesn't overflow */
  .map-picker-content {
    height: 100% !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Optimize map container in modal */
  .map-picker-map-container {
    flex: 1 !important;
    min-height: 200px !important;
    max-height: none !important;
  }

  /* Compact controls in modal */
  .map-picker-controls {
    padding: 8px !important;
    gap: 8px !important;
  }

  /* Compact buttons in modal */
  .map-picker-button {
    height: 36px !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
  }

  /* Prevent body scroll when modal is open */
  body.modal-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
  }
}

/* Tablet optimizations */
@media (min-width: 641px) and (max-width: 1023px) {
  .map-picker-modal {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    box-sizing: border-box !important;
    overflow: auto !important;
    min-height: 100vh !important;
  }

  /* Tablet-specific modal content - optimal height tanpa terpotong */
  .map-picker-content {
    width: 85vw !important;
    max-width: 800px !important;
    height: 85vh !important;
    max-height: calc(100vh - 40px) !important;
    min-height: 600px !important;
    display: flex !important;
    flex-direction: column !important;
    margin: auto !important;
    position: relative !important;
    overflow: hidden !important;
  }

  /* Larger touch targets for tablet */
  .maplibregl-ctrl-group button {
    width: 42px !important;
    height: 42px !important;
    font-size: 17px !important;
    line-height: 42px !important;
  }

  /* Better positioning for tablet */
  .maplibregl-ctrl-top-right {
    top: 12px !important;
    right: 12px !important;
  }

  /* Tablet-optimized map container - lebih tinggi */
  .map-picker-map-container {
    flex: 1 !important;
    min-height: 600px !important;
    height: 100% !important;
  }

  /* Tablet button styling */
  .map-picker-button {
    height: 52px !important;
    font-size: 17px !important;
    padding: 14px 24px !important;
    min-height: 52px !important;
  }

  /* Tablet controls spacing */
  .map-picker-controls {
    padding: 20px !important;
    gap: 16px !important;
    flex: 1 !important;
    overflow: hidden !important;
  }

  /* Tablet-specific AddressPicker layout optimizations */
  .map-picker-controls .flex.flex-col.h-full {
    height: 100% !important;
    overflow: hidden !important;
    gap: 12px !important;
  }

  /* Override space-y-3 pada tablet */
  .map-picker-controls .space-y-3>*+* {
    margin-top: 8px !important;
  }

  /* Search section - fixed height */
  .map-picker-controls .flex-shrink-0 {
    flex-shrink: 0 !important;
    min-height: auto !important;
  }

  /* Tablet search input styling */
  .map-picker-controls .flex-shrink-0.rounded-md {
    margin-bottom: 12px !important;
  }

  .map-picker-controls input[type="text"] {
    height: 44px !important;
    font-size: 16px !important;
    padding: 12px 16px !important;
  }

  .map-picker-controls .flex.items-center.space-x-2 {
    gap: 8px !important;
  }

  /* Tablet search button styling */
  .map-picker-controls .flex.items-center button {
    height: 44px !important;
    font-size: 14px !important;
    padding: 12px 16px !important;
    min-width: 80px !important;
  }

  /* Map card - takes remaining space */
  .map-picker-controls .flex-1.overflow-hidden {
    flex: 1 !important;
    min-height: 300px !important;
    overflow: hidden !important;
  }

  /* Controls section below map - compact */
  .map-picker-controls .flex-shrink-0.space-y-3 {
    flex-shrink: 0 !important;
    gap: 8px !important;
    max-height: 150px !important;
    overflow: visible !important;
  }

  /* Tablet current location button */
  .map-picker-controls .flex.justify-center {
    margin: 8px 0 !important;
  }

  /* Tablet address display - compact */
  .map-picker-controls .bg-violet-50 {
    padding: 8px 12px !important;
    margin: 8px 0 !important;
    font-size: 12px !important;
    line-height: 1.3 !important;
  }

  .map-picker-controls .bg-violet-50 .text-xs {
    font-size: 11px !important;
  }

  .map-picker-controls .bg-violet-50 .text-xs.sm\\:text-sm {
    font-size: 12px !important;
  }

  /* Ensure modal is perfectly centered */
  .map-picker-modal {
    align-items: center !important;
    justify-content: center !important;
  }

  .map-picker-modal>div {
    position: static !important;
    transform: none !important;
    margin: 0 auto !important;
  }

  /* Override any conflicting styles for tablet centering */
  .map-picker-content {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
  }
}

/* Tablet dengan tinggi layar kecil - mencegah modal terpotong */
@media (min-width: 641px) and (max-width: 1023px) and (max-height: 800px) {
  .map-picker-content {
    height: 80vh !important;
    max-height: calc(100vh - 60px) !important;
    min-height: 500px !important;
  }

  .map-picker-modal {
    padding: 30px 20px !important;
  }

  .map-picker-map-container {
    min-height: 400px !important;
  }

  /* Compact layout untuk tinggi kecil */
  .map-picker-controls .flex-shrink-0.space-y-3 {
    max-height: 120px !important;
  }

  .map-picker-controls .bg-violet-50 {
    padding: 6px 10px !important;
    font-size: 11px !important;
  }
}

/* Tablet dengan tinggi layar sangat kecil - prioritas mencegah terpotong */
@media (min-width: 641px) and (max-width: 1023px) and (max-height: 700px) {
  .map-picker-content {
    height: 75vh !important;
    max-height: calc(100vh - 80px) !important;
    min-height: 450px !important;
  }

  .map-picker-modal {
    padding: 40px 20px !important;
  }

  .map-picker-map-container {
    min-height: 350px !important;
  }

  .map-picker-button {
    height: 48px !important;
    font-size: 16px !important;
  }

  /* Extra compact layout untuk tinggi sangat kecil */
  .map-picker-controls .flex-shrink-0.space-y-3 {
    max-height: 100px !important;
    gap: 4px !important;
  }

  .map-picker-controls .bg-violet-50 {
    padding: 4px 8px !important;
    font-size: 10px !important;
    line-height: 1.2 !important;
  }

  .map-picker-controls .flex.justify-center {
    margin: 4px 0 !important;
  }
}

/* Fallback untuk tablet dengan tinggi layar ekstrem kecil */
@media (min-width: 641px) and (max-width: 1023px) and (max-height: 600px) {
  .map-picker-modal {
    align-items: flex-start !important;
    padding: 10px !important;
    overflow-y: auto !important;
  }

  .map-picker-content {
    height: auto !important;
    max-height: calc(100vh - 20px) !important;
    min-height: 400px !important;
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }

  .map-picker-map-container {
    min-height: 300px !important;
    flex: 1 !important;
  }

  /* Minimal layout untuk ekstrem kecil */
  .map-picker-controls .flex-shrink-0.space-y-3 {
    max-height: 80px !important;
    gap: 2px !important;
  }

  .map-picker-controls .bg-violet-50 {
    padding: 2px 6px !important;
    font-size: 9px !important;
    line-height: 1.1 !important;
    max-height: 30px !important;
    overflow: hidden !important;
  }

  .map-picker-controls .flex.justify-center {
    margin: 2px 0 !important;
  }

  .map-picker-controls input[type="text"] {
    height: 36px !important;
    font-size: 14px !important;
    padding: 8px 12px !important;
  }
}

/* Tablet landscape specific optimizations */
@media (min-width: 641px) and (max-width: 1023px) and (orientation: landscape) {
  .map-picker-content {
    height: 95vh !important;
    max-height: 98vh !important;
    min-height: 85vh !important;
  }

  .map-picker-map-container {
    min-height: 70vh !important;
  }

  .map-picker-modal {
    padding: 5px !important;
  }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 640px) {

  input[type="text"],
  input[type="search"],
  textarea {
    font-size: 16px !important;
  }
}

/* Mobile Address Display Optimizations */
@media (max-width: 640px) {

  /* Address display container */
  .mobile-address-display {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
    line-height: 1.3 !important;
  }

  /* Ensure text doesn't overflow container */
  .mobile-address-text {
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  /* Multi-line address support for very long addresses */
  .mobile-address-multiline {
    white-space: normal !important;
    max-height: 2.6em !important;
    /* ~2 lines */
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
  }

  /* Loading state styling */
  .mobile-address-loading {
    animation: pulse 1.5s ease-in-out infinite !important;
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.6;
    }
  }
}

/* Address Input and Map Button Layout Optimizations */
@media (max-width: 640px) {

  /* Full-width map button on mobile for better touch targets */
  .address-map-button {
    width: 100% !important;
    min-height: 44px !important;
    /* iOS recommended minimum touch target */
    font-size: 14px !important;
    padding: 12px 16px !important;
  }

  /* Address textarea field mobile optimizations */
  .address-input-field {
    min-height: 100px !important;
    /* Better height for textarea on mobile */
    font-size: 16px !important;
    /* Prevents zoom on iOS */
    line-height: 1.4 !important;
    /* Better readability for multi-line text */
  }

  /* Improved spacing for vertical layout */
  .address-section-spacing {
    gap: 12px !important;
  }

  /* Coordinates display enhancement */
  .coordinates-display-mobile {
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
  }

  /* Textarea-specific mobile enhancements */
  .address-textarea-container {
    position: relative !important;
  }

  .address-textarea-icon {
    top: 12px !important;
    /* Better positioning for textarea */
    z-index: 10 !important;
  }

  /* Placeholder styling for better UX */
  .address-input-field::placeholder {
    color: #9CA3AF !important;
    /* Gray-400 */
    opacity: 0.8 !important;
    line-height: 1.4 !important;
  }
}

@media (min-width: 641px) {

  /* Desktop/tablet optimizations */
  .address-map-button {
    width: auto !important;
    min-width: 140px !important;
  }

  /* Better alignment on larger screens */
  .address-button-container {
    display: flex !important;
    justify-content: flex-start !important;
  }

  /* Desktop textarea optimizations */
  .address-input-field {
    min-height: 120px !important;
    /* Slightly taller on desktop for better visibility */
    font-size: 14px !important;
    /* Standard desktop font size */
    line-height: 1.5 !important;
    /* Better line spacing for desktop */
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  @media (max-width: 640px) {
    .map-picker-modal {
      padding-top: max(8px, env(safe-area-inset-top)) !important;
      padding-bottom: max(8px, env(safe-area-inset-bottom)) !important;
      padding-left: max(8px, env(safe-area-inset-left)) !important;
      padding-right: max(8px, env(safe-area-inset-right)) !important;
    }
  }
}