/**
 * Utilitas untuk optimasi gambar
 * File ini berisi fungsi-fungsi untuk membantu mengoptimalkan loading gambar
 */

/**
 * Memuat gambar dengan lazy loading
 * @param src URL gambar
 * @returns Promise yang resolve dengan elemen gambar
 */
export function preloadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Mendapatkan ukuran gambar yang optimal berdasarkan viewport
 * @param originalWidth Lebar asli gambar
 * @param originalHeight Tinggi asli gambar
 * @param maxWidth Lebar maksimal yang diizinkan
 * @param maxHeight Tinggi maksimal yang diizinkan
 * @returns Ukuran gambar yang dioptimalkan
 */
export function getOptimalImageDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  // Pertahankan rasio aspek
  const aspectRatio = originalWidth / originalHeight;

  let width = originalWidth;
  let height = originalHeight;

  // Skala gambar jika lebih besar dari max dimensions
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }

  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  return { 
    width: Math.round(width), 
    height: Math.round(height) 
  };
}

/**
 * Mendapatkan URL gambar dengan ukuran yang sesuai dari Cloudinary
 * @param url URL gambar Cloudinary asli
 * @param width Lebar yang diinginkan
 * @param height Tinggi yang diinginkan (opsional)
 * @returns URL gambar yang dioptimasi
 */
export function getOptimizedImageUrl(
  url: string,
  width: number,
  height?: number
): string {
  // Hanya mendukung URL Cloudinary
  if (!url || !url.includes('cloudinary.com')) {
    return url;
  }
  
  // Ekstrak base URL dan path
  const [baseUrl, params] = url.split('/upload/');
  
  // Buat parameter transformasi
  const transformParams = [`w_${width}`];
  
  // Tambahkan parameter tinggi jika ada
  if (height) {
    transformParams.push(`h_${height}`);
  }
  
  // Tambahkan parameter format dan kualitas untuk mengoptimasi lebih lanjut
  transformParams.push('q_auto', 'f_auto');
  
  // Gabungkan URL
  return `${baseUrl}/upload/${transformParams.join(',')}/` + 
    (params ? params : '');
}

/**
 * Mendapatkan ukuran gambar yang sesuai untuk srcset
 * @param baseWidth Lebar dasar untuk set ukuran
 * @returns Array ukuran untuk srcset
 */
export function getImageSrcSetSizes(baseWidth: number): number[] {
  // Buat srcset dengan variasi ukuran untuk berbagai perangkat
  return [
    Math.round(baseWidth * 0.5),  // 0.5x
    baseWidth,                     // 1x
    Math.round(baseWidth * 1.5),   // 1.5x
    Math.round(baseWidth * 2),     // 2x
    Math.round(baseWidth * 3)      // 3x untuk layar retina
  ];
}

/**
 * Membuat string srcset untuk elemen gambar
 * @param url URL gambar dasar
 * @param sizes Array ukuran lebar untuk srcset
 * @returns String srcset
 */
export function createSrcSet(url: string, sizes: number[]): string {
  return sizes
    .map((size) => `${getOptimizedImageUrl(url, size)} ${size}w`)
    .join(', ');
}

/**
 * Cek apakah Native Lazy Loading didukung browser
 * @returns boolean
 */
export function supportsNativeLazyLoading(): boolean {
  return 'loading' in HTMLImageElement.prototype;
} 
