import { Skeleton } from "@/app/components/ui/skeleton";

export default function DashboardLoading() {
  return (
    <>
      {/* Header Section Skeleton */}
      <div className="relative bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-950/40 dark:to-indigo-950/40 rounded-xl mb-8 p-6 shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="absolute right-0 top-0 bottom-0 w-1/3 opacity-10 bg-gradient-to-r from-violet-200 to-indigo-300 dark:from-violet-800 dark:to-indigo-700"></div>
        <div className="relative">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex-1">
              <Skeleton className="h-8 w-80 mb-3 animate-pulse" />
              <Skeleton className="h-4 w-96 mb-2 animate-pulse" />
              <Skeleton className="h-4 w-72 animate-pulse" />
            </div>
            <div className="flex flex-wrap items-center gap-3 mt-2 md:mt-0">
              <Skeleton className="h-10 w-32 hidden sm:block animate-pulse" />
              <Skeleton className="h-10 w-36 animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards Skeleton */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Card 1 - Rental Aktif */}
        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700">
          <div className="absolute top-0 right-0 h-16 w-16 bg-green-100 dark:bg-green-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-green-200 dark:bg-green-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-12 w-12 rounded-full animate-pulse" />
              <Skeleton className="h-6 w-16 rounded-full animate-pulse" />
            </div>
            <Skeleton className="h-6 w-24 mb-4 animate-pulse" />
            <div className="flex items-end justify-between">
              <div>
                <Skeleton className="h-9 w-12 mb-2 animate-pulse" />
                <Skeleton className="h-4 w-40 animate-pulse" />
              </div>
              <Skeleton className="h-4 w-16 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Card 2 - Pembayaran Tertunda */}
        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700">
          <div className="absolute top-0 right-0 h-16 w-16 bg-orange-100 dark:bg-orange-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-orange-200 dark:bg-orange-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-12 w-12 rounded-full animate-pulse" />
              <Skeleton className="h-6 w-20 rounded-full animate-pulse" />
            </div>
            <Skeleton className="h-6 w-36 mb-4 animate-pulse" />
            <div className="flex items-end justify-between">
              <div>
                <Skeleton className="h-9 w-12 mb-2 animate-pulse" />
                <Skeleton className="h-4 w-44 animate-pulse" />
              </div>
              <Skeleton className="h-4 w-16 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Card 3 - Produk Tersedia */}
        <div className="relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700">
          <div className="absolute top-0 right-0 h-16 w-16 bg-blue-100 dark:bg-blue-900/30 -mt-6 -mr-6 rounded-full" />
          <div className="absolute top-0 right-0 h-8 w-8 bg-blue-200 dark:bg-blue-800/40 -mt-2 -mr-10 rounded-full" />
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-12 w-12 rounded-full animate-pulse" />
              <Skeleton className="h-6 w-18 rounded-full animate-pulse" />
            </div>
            <Skeleton className="h-6 w-32 mb-4 animate-pulse" />
            <div className="flex items-end justify-between">
              <div>
                <Skeleton className="h-9 w-12 mb-2 animate-pulse" />
                <Skeleton className="h-4 w-48 animate-pulse" />
              </div>
              <Skeleton className="h-4 w-16 animate-pulse" />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section Skeleton */}
      <div className="grid gap-8 md:grid-cols-2 mt-10">
        {/* Chart Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
                <Skeleton className="h-4 w-56 animate-pulse" />
              </div>
              <Skeleton className="h-6 w-40 rounded-md animate-pulse" />
            </div>
          </div>
          <div className="p-6">
            {/* Chart Skeleton */}
            <div className="space-y-4">
              <div className="flex items-end justify-between h-40 gap-1">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="flex flex-col items-center gap-2 flex-1">
                    <Skeleton
                      className="w-full max-w-6 bg-violet-200 dark:bg-violet-800 animate-pulse"
                      style={{ height: `${Math.random() * 100 + 30}px` }}
                    />
                    <Skeleton className="h-3 w-6 animate-pulse" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Rentals Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
          <div className="p-6 border-b border-gray-100 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-5 gap-2">
              <div>
                <Skeleton className="h-6 w-32 mb-2 animate-pulse" />
                <Skeleton className="h-4 w-40 animate-pulse" />
              </div>
              <Skeleton className="h-4 w-24 animate-pulse" />
            </div>
          </div>

          {/* Recent Rental Items Skeleton */}
          <div className="divide-y divide-gray-100 dark:divide-gray-700">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="h-10 w-10 rounded-full animate-pulse" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between gap-2 mb-2">
                      <Skeleton className="h-4 w-32 animate-pulse" />
                      <Skeleton className="h-5 w-16 rounded-full animate-pulse" />
                    </div>
                    <Skeleton className="h-3 w-28 animate-pulse" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
