import { prisma } from "@/lib/config/prisma";
import { Product, ProductWithUser } from "@/lib/types/product";

export interface ProductsResponse {
  items: ProductWithUser[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductFilters {
  search?: string;
  category?: string;
  capacity?: string;
  minPrice?: number;
  maxPrice?: number;
  availability?: string;
  page?: number;
  limit?: number;
}

export async function searchProducts(query: string): Promise<Product[]> {
    try {
        return await prisma.product.findMany({
            where: {
                OR: [
                    { name: { contains: query, mode: 'insensitive' } },
                    { description: { contains: query, mode: 'insensitive' } },
                ],
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    },
                },
            },
        });
    } catch (error) {
        console.error('Error searching products:', error);
        throw new Error('Gagal mencari produk');
    }
}

export async function getProductById(id: string): Promise<Product | null> {
    try {
        console.log('<PERSON>cari produk dengan ID:', id);

        const product = await prisma.product.findUnique({
            where: { id },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    },
                },
            },
        });

        if (!product) {
            console.log('Produk tidak ditemukan untuk ID:', id);
            return null;
        }

        console.log('Produk ditemukan:', product.name);
        return product;
    } catch (error) {
        console.error('Error fetching product:', error);
        return null;
    }
}

export async function getProductByUser(): Promise<Product[]> {
    try {
        return await prisma.product.findMany({
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
    } catch (error) {
        console.error("Error fetching products:", error);
        throw new Error("Gagal mengambil data produk");
    }
}

export async function getProducts(filters: ProductFilters = {}): Promise<ProductsResponse> {
    try {
        if (typeof window !== 'undefined') {
            // Client-side: menggunakan API endpoint
            const url = new URL('/api/products', window.location.origin);

            // Tambahkan filter ke URL jika ada
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    url.searchParams.append(key, String(value));
                }
            });

            // Memanggil API
            const response = await fetch(url.toString(), {
                next: { revalidate: 60 } // Cache selama 60 detik
            });

            if (!response.ok) {
                throw new Error('Gagal mendapatkan data produk');
            }

            const data = await response.json();
            return data;
        } else {
            // Server-side: dapatkan semua produk dengan pagination dasar
            const page = filters.page || 1;
            const limit = filters.limit || 9;
            const skip = (page - 1) * limit;

            // Ambil produk dan total jumlah
            const [products, total] = await Promise.all([
                prisma.product.findMany({
                    skip,
                    take: limit,
                    orderBy: { createdAt: 'desc' },
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                phone: true,
                                role: true,
                                createdAt: true,
                                updatedAt: true
                            }
                        }
                    }
                }),
                prisma.product.count()
            ]);

            return {
                items: products as unknown as ProductWithUser[],
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
    } catch (error) {
        console.error("Error fetching products:", error);
        return {
            items: [],
            total: 0,
            page: 1,
            limit: 9,
            totalPages: 0
        };
    }
}

export async function getHighlightedProducts(): Promise<Product[]> {
    try {
        return await prisma.product.findMany({
            where: {
                status: "AVAILABLE"
            },
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        phone: true,
                        role: true,
                        createdAt: true,
                        updatedAt: true
                    }
                }
            },
            take: 3,
            orderBy: {
                createdAt: "desc"
            },
        });
    } catch (error) {
        console.error("Error fetching highlighted products:", error);
        throw new Error("Gagal mengambil produk unggulan");
    }
}
