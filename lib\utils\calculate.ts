import { RENTAL_DURATIONS, type RentalDuration } from '@/lib/utils/constants';
import { getDurationHours } from '@/lib/utils/duration';

/**
 * Menghitung harga rental berdasarkan durasi dalam jam
 * @param hours Jumlah jam rental
 * @param pricePerShift Harga per shift 8 jam
 * @param quantity Jumlah unit
 * @returns Total harga
 */
export function calculateRentalPriceByHours(
  hours: number,
  pricePerShift: number, 
  quantity: number = 1
): number {
  // Calculate how many 8-hour shifts are needed, rounding up
  const shifts = Math.ceil(hours / 8);
  return pricePerShift * shifts * quantity;
}

/**
 * Menghitung harga rental berdasarkan durasi yang dipilih
 * @param duration Durasi rental
 * @param pricePerShift Harga per shift 8 jam
 * @param quantity Jumlah unit
 * @returns Total harga
 */
export function calculateRentalPrice(
  duration: RentalDuration,
  pricePerShift: number, 
  quantity: number = 1
): number {
  const hours = RENTAL_DURATIONS[duration].hours;
  const shifts = Math.ceil(hours / 8); 
  // Hitung total harga: harga per shift × jumlah shift × jumlah unit
  return pricePerShift * shifts * quantity;
}

export function calculateShifts(duration: RentalDuration): number {
  return Math.ceil(RENTAL_DURATIONS[duration].hours / 8);
}

export function formatDuration(duration: RentalDuration): string {
  return RENTAL_DURATIONS[duration].label;
}

export function getDurationDescription(duration: RentalDuration): string {
  return RENTAL_DURATIONS[duration].description;
}

/**
 * Get booked hours from duration string (e.g., '8_JAM' -> 8 hours, '1x8_HOURS' -> 8 hours)
 * @param duration Duration string in new format ('8_JAM') or old format ('1x8_HOURS')
 * @returns Number of hours
 */
export function getBookedHours(duration: string): number {
  return getDurationHours(duration);
}

export function calculateDeposit(totalPrice: number, isKnownUser: boolean = false): number {
  // Jika user dikenal, tidak perlu deposit (deposit 0%)
  // Jika user tidak dikenal, deposit 50%
  return isKnownUser ? 0 : Math.floor(totalPrice * 0.5);
}

export function calculateOvertimeCost(overtimeHours: number, basePrice: number): number {
  // Hitung biaya overtime per jam (1.5x dari harga normal per jam)
  const pricePerHour = basePrice / 8; // Asumsi harga normal untuk 8 jam
  const overtimeRate = 1.5;
  return Math.floor(overtimeHours * pricePerHour * overtimeRate);
}

// Fungsi untuk menghitung tarif overtime berdasarkan kapasitas genset (KVA)
export function calculateOvertimeRate(capacity: number, customRate?: number | null): number {
  // Jika ada tarif khusus yang ditentukan di produk, gunakan itu
  if (customRate && customRate > 0) {
    return customRate;
  }
  
  // Jika tidak, gunakan perhitungan otomatis berdasarkan kapasitas
  // Basis tarif: Rp 100.000/jam untuk 10 KVA
  const baseRate = 100000;
  const baseCapacity = 10;
  
  // Untuk genset dengan kapasitas berbeda, hitung secara proporsional
  // dengan pembulatan ke atas ke 10.000 terdekat
  const rate = Math.ceil((capacity / baseCapacity) * baseRate / 10000) * 10000;
  
  // Minimal rate adalah 50.000/jam
  return Math.max(50000, rate);
}

// Fungsi untuk menghitung biaya overtime
export function calculateOvertimeCharge(overtime: number, capacity: number, customRate?: number | null): number {
  const hourlyRate = calculateOvertimeRate(capacity, customRate);
  return overtime * hourlyRate;
}

// Fungsi untuk menghitung sisa waktu dalam format yang mudah dibaca
export function calculateRemainingTime(endDate: Date): {
  expired: boolean;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
} {
  const now = new Date();
  const end = new Date(endDate);
  
  // Selisih waktu dalam milidetik
  const diff = end.getTime() - now.getTime();
  
  // Jika sudah lewat, tandai sebagai expired
  if (diff <= 0) {
    return {
      expired: true,
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalSeconds: 0
    };
  }
  
  // Hitung selisih dalam hari, jam, menit, dan detik
  const totalSeconds = Math.floor(diff / 1000);
  const days = Math.floor(totalSeconds / (24 * 60 * 60));
  const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  
  return {
    expired: false,
    days,
    hours,
    minutes,
    seconds,
    totalSeconds
  };
}

// Fungsi untuk menghitung jumlah jam overtime
export function calculateOvertime(operationalEnd: Date, rentalEndDate: Date): number {
  // Jika waktu operasional selesai sebelum batas waktu sewa, tidak ada overtime
  if (operationalEnd <= rentalEndDate) {
    return 0;
  }
  
  // Hitung selisih dalam jam
  const diffMs = operationalEnd.getTime() - rentalEndDate.getTime();
  // Konversi milidetik ke jam dan bulatkan ke atas
  return Math.ceil(diffMs / (1000 * 60 * 60));
}

/**
 * Menghitung overtime berdasarkan durasi operasi dan waktu jeda
 * @param operationalStart Waktu mulai operasi
 * @param operationalEnd Waktu selesai operasi
 * @param durationHours Durasi yang dipesan dalam jam
 * @param pauseOffset Total waktu jeda dalam milidetik
 * @returns Jumlah jam overtime
 */
export function calculateOperationalOvertime(
  operationalStart: Date,
  operationalEnd: Date,
  durationHours: number,
  pauseOffset: number = 0
): number {
  // Hitung waktu berakhir yang direncanakan: waktu mulai + durasi
  const startTimeMs = operationalStart.getTime();
  const durationMs = durationHours * 60 * 60 * 1000;
  const plannedEndTimeMs = startTimeMs + durationMs;
  
  // Sesuaikan waktu berakhir dengan total waktu jeda
  const adjustedPlannedEndTimeMs = plannedEndTimeMs + pauseOffset;
  
  // Jika waktu operasional selesai sebelum waktu yang direncanakan, tidak ada overtime
  const actualEndTimeMs = operationalEnd.getTime();
  if (actualEndTimeMs <= adjustedPlannedEndTimeMs) {
    return 0;
  }
  
  // Hitung selisih dalam jam dan bulatkan ke atas
  const overtimeMs = actualEndTimeMs - adjustedPlannedEndTimeMs;
  return Math.ceil(overtimeMs / (1000 * 60 * 60));
}

/**
 * Mendapatkan status operasi berdasarkan waktu mulai dan akhir operasional
 */
export function getOperationStatus(operationalStart: Date | null, operationalEnd: Date | null) {
  if (!operationalStart) return "PENDING";
  if (!operationalEnd) return "RUNNING";
  return "COMPLETED";
}

/**
 * Validasi apakah waktu operasi masih dalam durasi booking
 */
export function validateOperationTime(
  operationalStart: Date | null, 
  duration: string
) {
  if (!operationalStart) return true;

  const startTime = new Date(operationalStart);
  const currentTime = new Date();
  const bookedHours = getBookedHours(duration);
  const elapsedHours = (currentTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);

  return elapsedHours <= bookedHours;
}
