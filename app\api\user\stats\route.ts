import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";
import { format, subMonths } from "date-fns";
import { Rental, PaymentStatus, RentalStatus, ProductStatus } from "@prisma/client";

export async function GET() {
  try {
    const session = await getSession();
    if (!session?.user?.id) {
      return new NextResponse(
        JSON.stringify({ message: "Unauthorized" }),
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Mendapatkan jumlah rental aktif untuk pengguna
    const activeRentals = await prisma.rental.count({
      where: {
        userId: userId,
        status: RentalStatus.ACTIVE,
      },
    });

    // Mendapatkan jumlah pembayaran tertunda
    const pendingPayments = await prisma.payment.count({
      where: {
        rental: {
          userId: userId,
        },
        status: PaymentStatus.DEPOSIT_PENDING,
      },
    });

    // Mendapatkan rekomendasi produk (5 produk terbaru)
    const recommendedProducts = await prisma.product.count({
      where: {
        status: ProductStatus.AVAILABLE,
      },
      take: 5,
    });

    // Mendapatkan data rental bulanan untuk 12 bulan terakhir
    const monthlyRentals = [];

    for (let i = 0; i < 12; i++) {
      const currentMonth = subMonths(new Date(), 11 - i);
      const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
      
      const count = await prisma.rental.count({
        where: {
          userId: userId,
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      });

      monthlyRentals.push({
        month: format(currentMonth, "MMM"),
        count,
      });
    }

    // Mendapatkan 3 rental terbaru
    const recentRentals = await prisma.rental.findMany({
      where: {
        userId: userId,
      },
      include: {
        product: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 3,
    });

    const formattedRecentRentals = recentRentals.map((rental: Rental & { product: { name: string } }) => ({
      id: rental.id,
      productName: rental.product.name,
      date: rental.createdAt,
      status: rental.status,
    }));

    return NextResponse.json({
      activeRentals,
      pendingPayments,
      recommendedProducts,
      monthlyRentals,
      recentRentals: formattedRecentRentals,
    });
  } catch (error) {
    console.error("Error fetching user dashboard stats:", error);
    return new NextResponse(
      JSON.stringify({ message: "Internal Server Error", error }),
      { status: 500 }
    );
  }
}
