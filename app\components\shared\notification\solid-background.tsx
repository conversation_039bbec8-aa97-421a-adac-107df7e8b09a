"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { cn } from "@/lib/utils";

interface SolidBackgroundProps {
  children: React.ReactNode;
  isVisible: boolean;
  className?: string;
  variant?: "dropdown" | "modal" | "toast";
}

export function SolidBackground({ 
  children, 
  isVisible, 
  className = "",
  variant = "dropdown" 
}: SolidBackgroundProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    if (isVisible) {
      // Animate in
      gsap.fromTo(containerRef.current,
        {
          opacity: 0,
          scale: 0.95,
          y: variant === "dropdown" ? -10 : 0,
          filter: "blur(4px)"
        },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          filter: "blur(0px)",
          duration: 0.3,
          ease: "back.out(1.7)"
        }
      );
    }
  }, [isVisible, variant]);

  const getVariantStyles = () => {
    switch (variant) {
      case "dropdown":
        return {
          backgroundColor: 'hsl(var(--background) / 0.98)',
          backdropFilter: 'blur(16px) saturate(180%)',
          WebkitBackdropFilter: 'blur(16px) saturate(180%)',
          border: '1px solid hsl(var(--border))',
          borderRadius: '0.5rem',
          boxShadow: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
        };
      case "modal":
        return {
          backgroundColor: 'hsl(var(--background) / 0.95)',
          backdropFilter: 'blur(20px) saturate(180%)',
          WebkitBackdropFilter: 'blur(20px) saturate(180%)',
          border: '1px solid hsl(var(--border))',
          borderRadius: '0.75rem',
          boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)'
        };
      case "toast":
        return {
          backgroundColor: 'hsl(var(--background) / 0.95)',
          backdropFilter: 'blur(12px) saturate(180%)',
          WebkitBackdropFilter: 'blur(12px) saturate(180%)',
          border: '1px solid hsl(var(--border))',
          borderRadius: '0.75rem',
          boxShadow: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)'
        };
      default:
        return {};
    }
  };

  if (!isVisible) return null;

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden",
        variant === "dropdown" && "origin-top-right",
        className
      )}
      style={getVariantStyles()}
    >
      {children}
    </div>
  );
}

interface GlassBackgroundProps {
  children: React.ReactNode;
  className?: string;
  intensity?: "light" | "medium" | "heavy";
}

export function GlassBackground({ 
  children, 
  className = "",
  intensity = "medium" 
}: GlassBackgroundProps) {
  const getIntensityStyles = () => {
    switch (intensity) {
      case "light":
        return {
          backgroundColor: 'hsl(var(--background) / 0.8)',
          backdropFilter: 'blur(8px) saturate(150%)',
          WebkitBackdropFilter: 'blur(8px) saturate(150%)'
        };
      case "medium":
        return {
          backgroundColor: 'hsl(var(--background) / 0.9)',
          backdropFilter: 'blur(12px) saturate(180%)',
          WebkitBackdropFilter: 'blur(12px) saturate(180%)'
        };
      case "heavy":
        return {
          backgroundColor: 'hsl(var(--background) / 0.95)',
          backdropFilter: 'blur(20px) saturate(200%)',
          WebkitBackdropFilter: 'blur(20px) saturate(200%)'
        };
      default:
        return {};
    }
  };

  return (
    <div
      className={cn(
        "relative border border-white/20 dark:border-white/10 rounded-lg shadow-xl",
        className
      )}
      style={getIntensityStyles()}
    >
      {children}
    </div>
  );
}

interface BlurOverlayProps {
  isVisible: boolean;
  onClick?: () => void;
  className?: string;
}

export function BlurOverlay({ isVisible, onClick, className = "" }: BlurOverlayProps) {
  const overlayRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!overlayRef.current) return;

    if (isVisible) {
      gsap.fromTo(overlayRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.2, ease: "power2.out" }
      );
    } else {
      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.2,
        ease: "power2.in"
      });
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div
      ref={overlayRef}
      className={cn(
        "fixed inset-0 z-40 bg-black/20 dark:bg-black/40 backdrop-blur-sm",
        className
      )}
      onClick={onClick}
      style={{
        backdropFilter: 'blur(4px)',
        WebkitBackdropFilter: 'blur(4px)'
      }}
    />
  );
}
