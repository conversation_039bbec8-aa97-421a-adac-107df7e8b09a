"use client";

import { useState, useCallback } from "react";
import { SoundButton } from "@/app/components/ui/sound-button";
import { Card, CardContent } from "@/app/components/ui/card";
import { <PERSON><PERSON>rosshair, LuInfo, LuRotateCw } from "react-icons/lu";
import { AddressPickerProps, LocationData, SearchResult } from "@/lib/types/map";
import { MapDisplay } from "@/app/components/map/MapDisplay";
import { MapSearch } from "@/app/components/map/MapSearch";
import useGeoLocation from "@/lib/hooks/useGeoLocation";
import { useReverseGeocode } from "@/lib/hooks/useReverseGeocode";

// Koordinat default untuk Nusa Tenggara Barat (Pusat Kota Mataram)
const DEFAULT_LATITUDE = -8.5833;
const DEFAULT_LONGITUDE = 116.1167;

export function AddressPicker({ onSelectLocation, defaultAddress = "" }: AddressPickerProps) {
  // State untuk koordinat yang dipilih
  const [coordinates, setCoordinates] = useState({
    lat: DEFAULT_LATITUDE,
    lng: DEFAULT_LONGITUDE,
  });

  // State untuk alamat yang dipilih
  const [selectedAddress, setSelectedAddress] = useState<string>(defaultAddress);

  // State untuk loading dan error handling
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Handler untuk pencarian (kita hanya perlu fungsi ini sebagai prop, tidak perlu implementasi)
  const handleSearchResults = useCallback(() => {
    // Tidak perlu implementasi
  }, []);

  // Gunakan hook untuk mendapatkan lokasi pengguna dan reverse geocoding
  const { getCurrentPosition, positionError, clearPositionError } = useGeoLocation();
  const { getAddressFromCoordinates, isLoading: isGeocodingLoading, error: geocodingError } = useReverseGeocode();

  // Handler saat marker di drag
  const handleMarkerDrag = useCallback((location: LocationData) => {
    console.log("Marker digeser ke:", location);
    setCoordinates({ lat: location.lat, lng: location.lng });
    setSelectedAddress(location.address);

    // Kirim lokasi terbaru ke parent komponen
    onSelectLocation(location);
  }, [onSelectLocation]);

  // State untuk menyimpan informasi akurasi lokasi
  const [locationAccuracy, setLocationAccuracy] = useState<number | null>(null);

  // Fungsi untuk memeriksa apakah browser mendukung geolokasi dengan baik
  const checkGeolocationSupport = useCallback(() => {
    if (!navigator.geolocation) {
      return { supported: false, message: "Geolokasi tidak didukung di browser Anda." };
    }

    // Periksa apakah menggunakan HTTPS (diperlukan untuk akurasi tinggi)
    if (typeof window !== 'undefined' && window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
      return {
        supported: true,
        warning: "Untuk akurasi lokasi yang optimal, gunakan HTTPS. Akurasi mungkin terbatas pada HTTP."
      };
    }

    return { supported: true };
  }, []);

  // Fungsi debug untuk menampilkan informasi sistem
  const debugGeolocation = useCallback(() => {
    console.log("🔧 Debug Informasi Geolokasi:");
    console.log("- Navigator geolocation:", !!navigator.geolocation);
    console.log("- Protocol:", window.location.protocol);
    console.log("- Hostname:", window.location.hostname);
    console.log("- User Agent:", navigator.userAgent);
    console.log("- Device Info:", /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'Mobile' : 'Desktop');

    if (navigator.permissions) {
      navigator.permissions.query({ name: 'geolocation' }).then((result) => {
        console.log("- Permission state:", result.state);
      }).catch((err) => {
        console.log("- Permission query error:", err);
      });
    } else {
      console.log("- Permissions API tidak tersedia");
    }
  }, []);

  // Handler untuk menggunakan lokasi saat ini dengan reverse geocoding
  const handleUseCurrentLocation = useCallback(async () => {
    try {
      setIsGettingLocation(true);
      setLocationError(null);
      setLocationAccuracy(null);
      clearPositionError();

      console.log("Memulai proses mendapatkan lokasi pengguna...");

      // Periksa dukungan geolokasi
      const geoSupport = checkGeolocationSupport();
      if (!geoSupport.supported) {
        throw new Error(geoSupport.message);
      }

      if (geoSupport.warning) {
        console.warn(geoSupport.warning);
      }

      // Dapatkan koordinat GPS pengguna
      console.log("Memanggil getCurrentPosition...");
      const position = await getCurrentPosition();

      if (position) {
        const { latitude, longitude, accuracy } = position.coords;
        console.log("✅ Koordinat pengguna berhasil ditemukan:", {
          latitude,
          longitude,
          accuracy,
          timestamp: new Date().toISOString()
        });

        // Simpan informasi akurasi
        setLocationAccuracy(accuracy || null);

        // Update koordinat terlebih dahulu
        setCoordinates({
          lat: latitude,
          lng: longitude
        });

        // Lakukan reverse geocoding untuk mendapatkan alamat yang dapat dibaca
        console.log("🔍 Memulai reverse geocoding untuk koordinat:", latitude, longitude);

        try {
          const humanReadableAddress = await getAddressFromCoordinates(latitude, longitude);

          if (humanReadableAddress) {
            console.log("✅ Alamat berhasil ditemukan:", humanReadableAddress);

            // Update alamat yang dipilih
            setSelectedAddress(humanReadableAddress);

            // Kirim lokasi lengkap ke parent komponen
            onSelectLocation({
              lat: latitude,
              lng: longitude,
              address: humanReadableAddress
            });
          } else {
            console.warn("⚠️ Reverse geocoding tidak mengembalikan alamat, menggunakan fallback");
            // Jika reverse geocoding gagal, gunakan alamat fallback
            const fallbackAddress = `Lokasi di koordinat ${latitude.toFixed(6)}, ${longitude.toFixed(6)}, Nusa Tenggara Barat, Indonesia`;

            // Update alamat yang dipilih
            setSelectedAddress(fallbackAddress);

            onSelectLocation({
              lat: latitude,
              lng: longitude,
              address: fallbackAddress
            });
          }
        } catch (geocodeError) {
          console.error("❌ Error saat reverse geocoding:", geocodeError);
          // Gunakan alamat fallback jika reverse geocoding error
          const fallbackAddress = `Lokasi di koordinat ${latitude.toFixed(6)}, ${longitude.toFixed(6)}, Nusa Tenggara Barat, Indonesia`;
          setSelectedAddress(fallbackAddress);
          onSelectLocation({
            lat: latitude,
            lng: longitude,
            address: fallbackAddress
          });
        }
      } else {
        console.error("❌ getCurrentPosition tidak mengembalikan posisi");
        throw new Error("Tidak dapat mendapatkan posisi dari GPS");
      }
    } catch (error) {
      console.error("❌ Error dalam handleUseCurrentLocation:", {
        error,
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      const errorMessage = error instanceof Error ? error.message : "Gagal mendapatkan lokasi Anda";
      setLocationError(errorMessage);
      setLocationAccuracy(null);
    } finally {
      console.log("🏁 Proses mendapatkan lokasi selesai");
      setIsGettingLocation(false);
    }
  }, [getCurrentPosition, clearPositionError, getAddressFromCoordinates, onSelectLocation, checkGeolocationSupport]);

  // Fungsi untuk mendapatkan warna dan teks berdasarkan akurasi


  // Handler saat memilih hasil pencarian
  const handleSelectSearchResult = useCallback((result: SearchResult) => {
    console.log("Hasil pencarian dipilih:", result);

    // Update koordinat dan alamat
    setCoordinates({
      lat: result.lat,
      lng: result.lon
    });

    // Update alamat yang dipilih
    setSelectedAddress(result.display_name);

    // Kirim lokasi terbaru ke parent komponen
    onSelectLocation({
      lat: result.lat,
      lng: result.lon,
      address: result.display_name
    });
  }, [onSelectLocation]);

  return (
    <div className="flex flex-col h-full space-y-3">
      {/* Pencarian lokasi */}
      <div className="flex-shrink-0 rounded-md shadow-sm">
        <MapSearch
          onSearchResult={handleSearchResults}
          onSelectResult={handleSelectSearchResult}
          placeholder="Cari lokasi di NTB..."
          className="w-full"
        />
      </div>

      {/* Peta - Menggunakan ruang maksimal */}
      <Card className="flex-1 overflow-hidden">
        <CardContent className="p-2 sm:p-3 h-full flex flex-col">
          {/* Container peta dengan tinggi maksimal - Responsif untuk mobile */}
          <div className="relative w-full rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex-1 mb-3 min-h-[40vh] sm:min-h-[50vh]">
            <div className="h-full w-full">
              <MapDisplay
                latitude={coordinates.lat}
                longitude={coordinates.lng}
                zoom={15}
                markerDraggable={true}
                onMarkerDrag={handleMarkerDrag}
                className="h-full w-full min-h-[250px] sm:min-h-[300px]"
              />
            </div>

            {/* Overlay untuk loading state */}
            {(isGettingLocation || isGeocodingLoading) && (
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center z-10">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex items-center gap-3">
                  <LuRotateCw className="h-5 w-5 animate-spin text-blue-600" />
                  <span className="text-base font-medium text-gray-700 dark:text-gray-300">
                    {isGettingLocation ? "Mendapatkan Lokasi..." : "Mencari Alamat..."}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Kontrol peta - di bawah peta */}
          <div className="flex-shrink-0 space-y-3 mt-3 sm:mt-4">
            {/* Tombol lokasi saat ini */}
            <div className="flex justify-center gap-2 px-2 sm:px-0">
              <SoundButton
                type="button"
                variant="outline"
                onClick={handleUseCurrentLocation}
                disabled={isGettingLocation || isGeocodingLoading}
                className="flex items-center justify-center gap-2 h-10 sm:h-11 text-xs sm:text-sm font-medium px-4 sm:px-6"
                soundType={isGettingLocation || isGeocodingLoading ? "none" : "click"}
              >
                {isGettingLocation || isGeocodingLoading ? (
                  <LuRotateCw className="h-4 w-4 animate-spin flex-shrink-0" />
                ) : (
                  <LuCrosshair className="h-4 w-4 flex-shrink-0" />
                )}
                <span className="truncate">
                  {isGettingLocation
                    ? "Mendapatkan Lokasi..."
                    : isGeocodingLoading
                      ? "Mencari Alamat..."
                      : "Gunakan Lokasi Saat Ini"
                  }
                </span>
              </SoundButton>

              {/* Tombol coba lagi jika akurasi kurang baik */}
              {locationAccuracy && locationAccuracy > 100 && !isGettingLocation && !isGeocodingLoading && (
                <SoundButton
                  type="button"
                  variant="ghost"
                  onClick={handleUseCurrentLocation}
                  className="flex items-center justify-center gap-1 h-10 sm:h-11 text-xs font-medium px-3"
                  soundType="click"
                >
                  <LuRotateCw className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">Coba Lagi</span>
                </SoundButton>
              )}

              {/* Tombol debug (hanya untuk development) */}
              {process.env.NODE_ENV === 'development' && (
                <SoundButton
                  type="button"
                  variant="ghost"
                  onClick={debugGeolocation}
                  className="flex items-center justify-center gap-1 h-10 sm:h-11 text-xs font-medium px-2"
                  soundType="click"
                  title="Debug info (lihat console)"
                >
                  <LuInfo className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">Debug</span>
                </SoundButton>
              )}
            </div>

            {/* Alamat yang dipilih - Responsif untuk mobile */}
            {selectedAddress && (
              <div className="px-3 py-2 sm:px-4 sm:py-3 bg-violet-50 dark:bg-violet-950/30 border border-violet-200 dark:border-violet-800 rounded-lg mx-2 sm:mx-0">
                <div className="text-xs font-medium text-violet-700 dark:text-violet-300 mb-1">
                  Alamat Terpilih:
                </div>
                <div className="text-xs sm:text-sm text-violet-900 dark:text-violet-100 leading-relaxed break-words">
                  {selectedAddress}
                </div>
              </div>
            )}
          </div>

          {/* Error handling */}
          {(locationError || positionError || geocodingError) && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-start gap-3">
                <LuInfo className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-red-700 dark:text-red-300 leading-relaxed mb-2">
                    {locationError || positionError || geocodingError}
                  </p>
                  {(locationError || positionError) && (
                    <div className="text-xs text-red-600 dark:text-red-400 space-y-1">
                      <div className="font-medium">Tips untuk mendapatkan lokasi yang akurat:</div>
                      <ul className="list-disc list-inside space-y-0.5 ml-2">
                        <li>Pastikan GPS/Location Services aktif di perangkat Anda</li>
                        <li>Izinkan akses lokasi di browser (klik ikon kunci di address bar)</li>
                        <li>Berada di area terbuka dengan sinyal GPS yang baik</li>
                        <li>Hindari area dalam ruangan atau di bawah atap</li>
                        <li>Refresh halaman dan coba lagi</li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Info tips untuk akurasi lokasi yang lebih baik */}
          {!locationError && !positionError && !geocodingError && !selectedAddress && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start gap-2">
                <LuInfo className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  <div className="font-medium mb-1">Tips untuk lokasi yang akurat:</div>
                  <div>Pastikan GPS aktif dan Anda berada di area terbuka untuk hasil terbaik</div>
                </div>
              </div>
            </div>
          )}


        </CardContent>
      </Card>
    </div>
  );
}

export default AddressPicker;
