"use client";

import { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ReactNode } from "react";

interface NotificationDropdownProps {
  children: ReactNode;
  isOpen: boolean;
}

export function NotificationDropdown({ children, isOpen }: NotificationDropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!dropdownRef.current) return;

    if (isOpen) {
      // Animate in
      gsap.fromTo(dropdownRef.current,
        {
          opacity: 0,
          scale: 0.95,
          y: -10,
          filter: "blur(4px)"
        },
        {
          opacity: 1,
          scale: 1,
          y: 0,
          filter: "blur(0px)",
          duration: 0.3,
          ease: "back.out(1.7)"
        }
      );
    } else {
      // Animate out
      gsap.to(dropdownRef.current, {
        opacity: 0,
        scale: 0.95,
        y: -10,
        filter: "blur(4px)",
        duration: 0.2,
        ease: "power2.in"
      });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      ref={dropdownRef}
      className="origin-top-right bg-white/98 dark:bg-slate-900/98 backdrop-blur-lg rounded-lg shadow-xl border border-slate-200 dark:border-slate-700"
      style={{
        backgroundColor: 'hsl(var(--background) / 0.98)',
        backdropFilter: 'blur(16px) saturate(180%)',
        WebkitBackdropFilter: 'blur(16px) saturate(180%)'
      }}
    >
      {children}
    </div>
  );
}

interface NotificationItemProps {
  children: ReactNode;
  isRead: boolean;
  index: number;
}

export function NotificationItem({ children, isRead, index }: NotificationItemProps) {
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!itemRef.current) return;

    // Animate in with stagger
    gsap.fromTo(itemRef.current,
      {
        opacity: 0,
        x: -20,
        scale: 0.95
      },
      {
        opacity: 1,
        x: 0,
        scale: 1,
        duration: 0.4,
        delay: index * 0.05,
        ease: "back.out(1.7)"
      }
    );

    // Hover animations
    const handleMouseEnter = () => {
      gsap.to(itemRef.current, {
        scale: 1.02,
        duration: 0.2,
        ease: "power2.out"
      });
    };

    const handleMouseLeave = () => {
      gsap.to(itemRef.current, {
        scale: 1,
        duration: 0.2,
        ease: "power2.out"
      });
    };

    const handleMouseDown = () => {
      gsap.to(itemRef.current, {
        scale: 0.98,
        duration: 0.1,
        ease: "power2.out"
      });
    };

    const handleMouseUp = () => {
      gsap.to(itemRef.current, {
        scale: 1.02,
        duration: 0.1,
        ease: "power2.out"
      });
    };

    const element = itemRef.current;
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    element.addEventListener('mousedown', handleMouseDown);
    element.addEventListener('mouseup', handleMouseUp);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      element.removeEventListener('mousedown', handleMouseDown);
      element.removeEventListener('mouseup', handleMouseUp);
    };
  }, [index]);

  return (
    <div
      ref={itemRef}
      className={`relative overflow-hidden cursor-pointer ${!isRead ? 'before:absolute before:left-0 before:top-0 before:h-full before:w-1 before:bg-gradient-to-b before:from-violet-500 before:to-purple-600' : ''}`}
    >
      {children}
    </div>
  );
}

interface NotificationBadgeProps {
  count: number;
  isVisible: boolean;
}

export function NotificationBadge({ count, isVisible }: NotificationBadgeProps) {
  const badgeRef = useRef<HTMLDivElement>(null);
  const countRef = useRef<HTMLSpanElement>(null);
  const prevCount = useRef(count);

  useEffect(() => {
    if (!badgeRef.current) return;

    if (isVisible) {
      // Animate badge in
      gsap.fromTo(badgeRef.current,
        {
          scale: 0,
          opacity: 0,
          rotation: -180
        },
        {
          scale: 1,
          opacity: 1,
          rotation: 0,
          duration: 0.5,
          ease: "elastic.out(1, 0.5)"
        }
      );
    } else {
      // Animate badge out
      gsap.to(badgeRef.current, {
        scale: 0,
        opacity: 0,
        rotation: 180,
        duration: 0.3,
        ease: "back.in(1.7)"
      });
    }
  }, [isVisible]);

  useEffect(() => {
    if (!countRef.current || !isVisible) return;

    // Animate count change
    if (prevCount.current !== count) {
      gsap.fromTo(countRef.current,
        {
          scale: 1.5,
          opacity: 0
        },
        {
          scale: 1,
          opacity: 1,
          duration: 0.3,
          ease: "back.out(1.7)"
        }
      );
      prevCount.current = count;
    }
  }, [count, isVisible]);

  if (!isVisible) return null;

  return (
    <div
      ref={badgeRef}
      className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold bg-red-500 text-white rounded-full"
    >
      <span ref={countRef}>
        {count > 99 ? '99+' : count}
      </span>
    </div>
  );
}

interface PulsingIconProps {
  children: ReactNode;
  isPulsing: boolean;
}

export function PulsingIcon({ children, isPulsing }: PulsingIconProps) {
  const iconRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!iconRef.current) return;

    if (isPulsing) {
      // Create pulsing animation
      const tl = gsap.timeline({ repeat: -1 });
      tl.to(iconRef.current, {
        scale: 1.1,
        rotation: 5,
        duration: 1,
        ease: "power2.inOut"
      })
        .to(iconRef.current, {
          scale: 1,
          rotation: -5,
          duration: 1,
          ease: "power2.inOut"
        })
        .to(iconRef.current, {
          scale: 1,
          rotation: 0,
          duration: 1,
          ease: "power2.inOut"
        });

      return () => {
        tl.kill();
      };
    } else {
      // Reset to normal state
      gsap.to(iconRef.current, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    }
  }, [isPulsing]);

  return (
    <div ref={iconRef}>
      {children}
    </div>
  );
}

interface LoadingDotsProps {
  className?: string;
}

export function LoadingDots({ className = "" }: LoadingDotsProps) {
  const dotsRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    dotsRef.current.forEach((dot, index) => {
      if (!dot) return;

      const tl = gsap.timeline({ repeat: -1 });
      tl.to(dot, {
        scale: 1.5,
        opacity: 1,
        duration: 0.5,
        delay: index * 0.2,
        ease: "power2.inOut"
      })
        .to(dot, {
          scale: 1,
          opacity: 0.5,
          duration: 0.5,
          ease: "power2.inOut"
        });
    });

    return () => {
      dotsRef.current.forEach((dot) => {
        if (dot) gsap.killTweensOf(dot);
      });
    };
  }, []);

  return (
    <div className={`flex items-center justify-center gap-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          ref={(el) => {
            if (el) dotsRef.current[index] = el;
          }}
          className="w-2 h-2 bg-violet-500 rounded-full opacity-50"
        />
      ))}
    </div>
  );
}

interface SlideInContainerProps {
  children: ReactNode;
  direction?: "left" | "right" | "up" | "down";
  delay?: number;
}

export function SlideInContainer({
  children,
  direction = "up",
  delay = 0
}: SlideInContainerProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const getInitialPosition = () => {
      switch (direction) {
        case "left": return { x: -50, y: 0 };
        case "right": return { x: 50, y: 0 };
        case "up": return { x: 0, y: 50 };
        case "down": return { x: 0, y: -50 };
        default: return { x: 0, y: 50 };
      }
    };

    const initialPos = getInitialPosition();

    gsap.fromTo(containerRef.current,
      {
        opacity: 0,
        x: initialPos.x,
        y: initialPos.y
      },
      {
        opacity: 1,
        x: 0,
        y: 0,
        duration: 0.6,
        delay,
        ease: "back.out(1.7)"
      }
    );
  }, [direction, delay]);

  return (
    <div ref={containerRef}>
      {children}
    </div>
  );
}

interface FadeInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
}

export function FadeIn({ children, delay = 0, duration = 0.3 }: FadeInProps) {
  const fadeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!fadeRef.current) return;

    gsap.fromTo(fadeRef.current,
      { opacity: 0 },
      {
        opacity: 1,
        duration,
        delay,
        ease: "power2.out"
      }
    );
  }, [delay, duration]);

  return (
    <div ref={fadeRef}>
      {children}
    </div>
  );
}

interface ScaleInProps {
  children: ReactNode;
  delay?: number;
}

export function ScaleIn({ children, delay = 0 }: ScaleInProps) {
  const scaleRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!scaleRef.current) return;

    gsap.fromTo(scaleRef.current,
      {
        scale: 0,
        opacity: 0
      },
      {
        scale: 1,
        opacity: 1,
        duration: 0.5,
        delay,
        ease: "elastic.out(1, 0.5)"
      }
    );
  }, [delay]);

  return (
    <div ref={scaleRef}>
      {children}
    </div>
  );
}
