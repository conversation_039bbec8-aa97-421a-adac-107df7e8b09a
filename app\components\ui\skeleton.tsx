import { cn } from "@/lib/utils"

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "violet" | "purple" | "navigation" | "mobile";
  size?: "sm" | "md" | "lg" | "mobile" | "tablet";
  shape?: "rectangle" | "circle" | "rounded";
}

function Skeleton({
  className,
  variant = "default",
  size = "md",
  shape = "rectangle",
  ...props
}: SkeletonProps) {
  const variantClasses = {
    default: "bg-muted",
    violet: "bg-violet-100/80 dark:bg-violet-900/30",
    purple: "bg-purple-100/80 dark:bg-purple-900/30",
    navigation: "bg-gradient-to-r from-violet-100/60 to-purple-100/60 dark:from-violet-900/20 dark:to-purple-900/20",
    mobile: "bg-violet-50/90 dark:bg-violet-950/40"
  };

  const sizeClasses = {
    sm: "h-3",
    md: "h-4",
    lg: "h-6",
    mobile: "min-h-[44px] min-w-[44px]",
    tablet: "min-h-[42px] min-w-[42px]"
  };

  const shapeClasses = {
    rectangle: "rounded-md",
    circle: "rounded-full",
    rounded: "rounded-xl"
  };

  return (
    <div
      className={cn(
        "w-full max-w-full",
        variantClasses[variant],
        sizeClasses[size],
        shapeClasses[shape],
        "motion-safe:animate-pulse motion-reduce:animate-none",
        "transition-all duration-200",
        className
      )}
      {...props}
    />
  )
}

export { Skeleton }
