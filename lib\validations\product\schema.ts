import { z } from "zod";

/**
 * Schema validasi untuk produk
 */
export const ProductSchema = z.object({
  name: z.string({
    required_error: "Nama produk harus diisi",
    invalid_type_error: "Nama produk harus berupa teks",
  }).min(1, "Nama produk harus diisi"),

  price: z.number({
    required_error: "Harga harus diisi",
    invalid_type_error: "Harga harus berupa angka",
  }).min(1, "Harga harus diisi"),

  capacity: z.number({
    required_error: "Kapasitas harus diisi",
    invalid_type_error: "Kapasitas harus berupa angka",
  }).min(1, "Kapasitas harus diisi"),

  stock: z.number({
    required_error: "Stok harus diisi",
    invalid_type_error: "Stok harus berupa angka",
  }).min(0, "Stok tidak boleh negatif"),

  overtimeRate: z.number({
    required_error: "Tarif overtime harus diisi",
    invalid_type_error: "Tarif overtime harus berupa angka",
  }).min(0, "Tarif overtime tidak boleh negatif").max(10000000, "Tarif overtime maksimal 10 juta rupiah"),

  description: z.string().nullable(),
  category: z.string().nullable(),
  imageUrl: z.string().nullable(),

  status: z.enum(["AVAILABLE", "NOT_AVAILABLE", "MAINTENANCE"], {
    required_error: "Status harus diisi",
    invalid_type_error: "Status tidak valid",
  }).default("AVAILABLE"),
});

/**
 * Schema validasi untuk review produk
 */
export const ReviewSchema = z.object({
  productId: z.string().min(1, { message: "Produk harus dipilih" }),
  rentalId: z.string().min(1, { message: "Rental harus dipilih" }),
  rating: z.number().min(1).max(5, { message: "Rating harus antara 1-5" }),
  comment: z.string()
    .max(500, { message: "Komentar terlalu panjang" })
    .regex(/^[^<>{}]*$/, { message: "Komentar mengandung karakter yang tidak diizinkan" })
    .nullable(),
});

/**
 * Schema validasi untuk maintenance produk
 */
export const MaintenanceSchema = z.object({
  productId: z.string().min(1, { message: "Produk harus dipilih" }),
  date: z.date()
    .min(new Date(), { message: "Tanggal tidak boleh di masa lalu" })
    .max(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), { message: "Tanggal maksimal 1 tahun ke depan" }),
  type: z.enum(["routine", "repair"], {
    message: "Tipe maintenance tidak valid"
  }),
  notes: z.string()
    .max(500, { message: "Catatan terlalu panjang" })
    .regex(/^[^<>{}]*$/, { message: "Catatan mengandung karakter yang tidak diizinkan" })
    .nullable(),
  cost: z.number()
    .min(0, { message: "Biaya tidak boleh negatif" })
    .max(50_000_000, { message: "Biaya maksimal 50 juta rupiah" })
    .nullable(),
});

// Tipe-tipe yang diekspor
export type ProductInput = z.infer<typeof ProductSchema>;
export type ReviewInput = z.infer<typeof ReviewSchema>;
export type MaintenanceInput = z.infer<typeof MaintenanceSchema>;

// Membuat objek schemas untuk ekspor
const productSchemas = {
  ProductSchema,
  ReviewSchema,
  MaintenanceSchema
};

export default productSchemas;
