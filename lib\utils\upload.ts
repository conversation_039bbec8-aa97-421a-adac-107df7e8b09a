export async function uploadToBlob(file: File) {
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/admin/products/image', {
            method: 'POST',
            body: formData,
        });

        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Upload failed');
            }

            return {
                url: data.url,
                success: true
            };
        } else {
            throw new Error('Invalid response format');
        }
    } catch (error) {
        console.error('Error uploading to blob:', error);
        return {
            url: null,
            success: false,
            error: error instanceof Error ? error.message : 'Gagal mengupload gambar'
        };
    }
}

export async function uploadImage(file: File) {
    try {
        const result = await uploadToBlob(file);
        
        if (!result.success || !result.url) {
            throw new Error(result.error || "Failed to upload image");
        }
        return result.url;
    } catch (error) {
        console.error("Error uploading image:", error);
        throw new Error("Failed to upload image");
    }
}
