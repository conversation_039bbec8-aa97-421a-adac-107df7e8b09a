"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { cn } from "@/lib/utils";
import { Check, CheckCheck } from "lucide-react";
import { useNotificationManager } from "@/lib/hooks/use-notification-sync";
import {
  SlideInContainer,
  FadeIn,
  LoadingDots,
  ScaleIn
} from "@/app/components/shared/notification/notification-animations";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  createdAt: Date;
}

interface NotificationsListProps {
  notifications: Notification[];
  unreadCount: number;
}

export function NotificationsList({ notifications: initialNotifications, unreadCount: initialUnreadCount }: NotificationsListProps) {
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);
  const {
    notifications: contextNotifications,
    counts,
    markAsRead: contextMarkAsRead,
    markAllAsRead: contextMarkAllAsRead,
    refresh,
    isLoading
  } = useNotificationManager();

  // Use context notifications if available, otherwise use initial props
  const notifications = contextNotifications.length > 0 ? contextNotifications : initialNotifications;
  const unreadCount = counts.total > 0 ? counts.total : initialUnreadCount;

  // Refresh notifications when component mounts
  useEffect(() => {
    refresh();
  }, [refresh]);

  // Mark notification as read using context
  const markAsRead = async (notificationId: string) => {
    await contextMarkAsRead(notificationId);
  };

  // Mark all as read using context
  const markAllAsRead = async () => {
    try {
      setIsMarkingAllRead(true);
      await contextMarkAllAsRead();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'NEW_RENTAL':
      case 'RENTAL_CONFIRMED':
        return '🚚';
      case 'NEW_PAYMENT':
      case 'PAYMENT_SUCCESS':
        return '💰';
      case 'PAYMENT_FAILED':
        return '❌';
      case 'LOW_STOCK':
        return '⚠️';
      case 'OVERTIME_DETECTED':
        return '⏰';
      case 'OPERATION_STARTED':
      case 'OPERATION_COMPLETED':
        return '🔧';
      case 'NEW_INVOICE':
        return '📄';
      default:
        return '📢';
    }
  };

  // Get notification color based on type
  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'NEW_RENTAL':
      case 'RENTAL_CONFIRMED':
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
      case 'NEW_PAYMENT':
      case 'PAYMENT_SUCCESS':
        return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      case 'PAYMENT_FAILED':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'LOW_STOCK':
        return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      case 'OVERTIME_DETECTED':
        return 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800';
      case 'OPERATION_STARTED':
      case 'OPERATION_COMPLETED':
        return 'bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800';
      default:
        return 'bg-slate-50 border-slate-200 dark:bg-slate-900/20 dark:border-slate-800';
    }
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Baru saja';
    if (diffInMinutes < 60) return `${diffInMinutes} menit lalu`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} jam lalu`;
    return `${Math.floor(diffInMinutes / 1440)} hari lalu`;
  };

  if (notifications.length === 0) {
    return (
      <FadeIn delay={0.2}>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <ScaleIn delay={0.3}>
              <div className="text-6xl mb-4">🔔</div>
            </ScaleIn>
            <SlideInContainer direction="up" delay={0.5}>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-2">
                Belum Ada Notifikasi
              </h3>
              <p className="text-slate-600 dark:text-slate-400 text-center max-w-md">
                Notifikasi tentang penyewaan, pembayaran, dan update lainnya akan muncul di sini.
              </p>
            </SlideInContainer>
          </CardContent>
        </Card>
      </FadeIn>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      {unreadCount > 0 && (
        <SlideInContainer direction="right" delay={0.2}>
          <div className="flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={markAllAsRead}
              disabled={isMarkingAllRead}
              className="text-violet-600 border-violet-200 hover:bg-violet-50 dark:text-violet-400 dark:border-violet-700 dark:hover:bg-violet-900/20 transition-all duration-200 hover:scale-105 active:scale-95 disabled:scale-100"
            >
              {isMarkingAllRead ? (
                <>
                  <LoadingDots className="mr-2" />
                  <span>Menandai...</span>
                </>
              ) : (
                <>
                  <CheckCheck className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:scale-110" />
                  Tandai Semua Dibaca
                </>
              )}
            </Button>
          </div>
        </SlideInContainer>
      )}

      {/* Notifications List */}
      <div className="space-y-3">
        {notifications.map((notification, index) => (
          <SlideInContainer key={notification.id} direction="up" delay={index * 0.1}>
            <Card
              className={cn(
                "transition-all duration-300 hover:shadow-lg hover:shadow-violet-100 dark:hover:shadow-violet-900/20 cursor-pointer hover:scale-[1.02] active:scale-[0.98]",
                !notification.isRead && "ring-2 ring-violet-200 dark:ring-violet-800 shadow-md",
                getNotificationColor(notification.type)
              )}
              onClick={() => {
                if (!notification.isRead) {
                  markAsRead(notification.id);
                }
              }}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className="text-2xl flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className={cn(
                        "font-semibold text-slate-900 dark:text-slate-100",
                        !notification.isRead && "text-violet-900 dark:text-violet-100"
                      )}>
                        {notification.title}
                      </h3>
                      <div className="flex items-center gap-2">
                        {!notification.isRead && (
                          <Badge variant="secondary" className="bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300 animate-pulse">
                            Baru
                          </Badge>
                        )}
                        <span className="text-xs text-slate-500 dark:text-slate-400">
                          {formatTimeAgo(new Date(notification.createdAt))}
                        </span>
                      </div>
                    </div>
                    <p className="text-slate-700 dark:text-slate-300 text-sm leading-relaxed">
                      {notification.message}
                    </p>
                    {!notification.isRead && (
                      <div className="flex items-center gap-2 mt-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            markAsRead(notification.id);
                          }}
                          className="text-violet-600 hover:text-violet-700 hover:bg-violet-100 dark:text-violet-400 dark:hover:bg-violet-900/30 transition-all duration-200 hover:scale-105 active:scale-95"
                        >
                          <Check className="w-4 h-4 mr-1 transition-transform duration-200 group-hover:scale-110" />
                          Tandai Dibaca
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </SlideInContainer>
        ))}
      </div>
    </div>
  );
}
