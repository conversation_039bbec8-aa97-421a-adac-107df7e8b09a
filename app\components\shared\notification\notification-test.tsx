"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { useNotificationManager } from "@/lib/hooks/use-notification-sync";
import { triggerNotificationUpdate } from "./notification-sync";
import { Bell, RefreshCw, CheckCircle, AlertCircle } from "lucide-react";

export function NotificationTest() {
  const [isCreating, setIsCreating] = useState(false);
  const { counts, refresh, hasUnread, unreadCount } = useNotificationManager();

  const createTestNotification = async () => {
    setIsCreating(true);
    try {
      const response = await fetch('/api/notifications/create-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'Test Notification',
          message: `Test notification created at ${new Date().toLocaleTimeString()}`,
          type: 'NEW_RENTAL'
        }),
      });

      if (response.ok) {
        // Trigger update untuk semua komponen
        triggerNotificationUpdate();
        // Manual refresh juga
        setTimeout(() => {
          refresh();
        }, 500);
      }
    } catch (error) {
      console.error('Error creating test notification:', error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          Notification Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Display */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {unreadCount}
            </div>
            <div className="text-xs text-slate-500">Belum Dibaca</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {counts.total}
            </div>
            <div className="text-xs text-slate-500">Total</div>
          </div>
        </div>

        {/* Status Badges */}
        <div className="flex flex-wrap gap-2">
          <Badge variant={hasUnread ? "destructive" : "secondary"}>
            {hasUnread ? (
              <>
                <AlertCircle className="w-3 h-3 mr-1" />
                Ada Notifikasi Baru
              </>
            ) : (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Semua Terbaca
              </>
            )}
          </Badge>
        </div>

        {/* Breakdown by Type */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Breakdown:</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Rental:</span>
              <Badge variant="outline" size="sm">{counts.counts.rental}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Payment:</span>
              <Badge variant="outline" size="sm">{counts.counts.payment}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Stock:</span>
              <Badge variant="outline" size="sm">{counts.counts.stock}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Other:</span>
              <Badge variant="outline" size="sm">{counts.counts.other}</Badge>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Button
            onClick={createTestNotification}
            disabled={isCreating}
            className="w-full"
            variant="outline"
          >
            {isCreating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Bell className="w-4 h-4 mr-2" />
                Create Test Notification
              </>
            )}
          </Button>

          <Button
            onClick={refresh}
            variant="ghost"
            size="sm"
            className="w-full"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Manual Refresh
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-xs text-slate-500 space-y-1">
          <div>• Buka di multiple tabs untuk test sync</div>
          <div>• Create notification untuk test real-time update</div>
          <div>• Check navbar/sidebar untuk melihat perubahan</div>
        </div>
      </CardContent>
    </Card>
  );
}
