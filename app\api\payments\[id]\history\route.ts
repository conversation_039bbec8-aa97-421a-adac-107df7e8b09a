import { NextResponse } from "next/server";
import { prisma } from "@/lib/config/prisma";

export const runtime = 'nodejs';

export async function GET(_request: Request, { params }: { params: Promise<{ id: string }> }) {
    const { id } = await params;

    const paymentHistory = await prisma.payment.findMany({
        where: { rentalId: id },
        include: { rental: true },
    });

    return NextResponse.json(paymentHistory);
}