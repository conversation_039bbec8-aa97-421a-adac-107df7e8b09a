/**
 * Utilitas untuk optimasi caching
 * Membantu menyimpan dan mengambil data secara efisien
 */

// Tipe data untuk cache item dengan waktu kadaluarsa
type CacheItem<T> = {
  value: T;
  expiry: number | null; // null berarti tidak ada kadaluarsa
};

// Implementasi cache dengan LRU (Least Recently Used)
export class Cache<T> {
  private cache: Map<string, CacheItem<T>>;
  private maxSize: number;
  private defaultTTL: number | null;

  constructor(maxSize: number = 100, defaultTTL: number | null = 5 * 60 * 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL; // Default 5 menit dalam milidetik, null berarti tidak ada kadaluarsa
  }

  /**
   * Mengambil item dari cache
   * @param key Kunci cache
   * @returns Item dari cache atau undefined jika tidak ditemukan/kadaluarsa
   */
  get(key: string): T | undefined {
    const item = this.cache.get(key);
    
    // Item tidak ditemukan
    if (!item) return undefined;
    
    // Cek kadaluarsa
    if (item.expiry && Date.now() > item.expiry) {
      this.cache.delete(key);
      return undefined;
    }
    
    // Update posisi dalam cache (LRU)
    this.cache.delete(key);
    this.cache.set(key, item);
    
    return item.value;
  }

  /**
   * Menyimpan item ke cache
   * @param key Kunci cache
   * @param value Nilai yang akan disimpan
   * @param ttl Time-to-live dalam milidetik, null berarti gunakan default
   */
  set(key: string, value: T, ttl: number | null = null): void {
    // Jika cache sudah penuh, hapus item tertua (first item in Map)
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey);
      }
    }
    
    // Hitung waktu kadaluarsa
    const expiry = ttl !== null 
      ? Date.now() + ttl 
      : this.defaultTTL !== null 
        ? Date.now() + this.defaultTTL 
        : null;
    
    // Simpan ke cache
    this.cache.set(key, { value, expiry });
  }

  /**
   * Menghapus item dari cache
   * @param key Kunci cache
   * @returns true jika berhasil dihapus, false jika tidak
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Membersihkan semua item yang kadaluarsa
   * @returns Jumlah item yang dihapus
   */
  prune(): number {
    const now = Date.now();
    let count = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry && now > item.expiry) {
        this.cache.delete(key);
        count++;
      }
    }
    
    return count;
  }

  /**
   * Mengosongkan cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Mendapatkan jumlah item dalam cache
   */
  get size(): number {
    return this.cache.size;
  }
}

// Helper untuk menyimpan data di localStorage dengan expiry
export const localStorageCache = {
  /**
   * Menyimpan data ke localStorage dengan expiry
   * @param key Kunci localStorage
   * @param value Nilai yang akan disimpan
   * @param ttl Time-to-live dalam milidetik, default 24 jam
   */
  set<T>(key: string, value: T, ttl: number = 24 * 60 * 60 * 1000): void {
    const item: CacheItem<T> = {
      value,
      expiry: ttl ? Date.now() + ttl : null
    };
    
    try {
      localStorage.setItem(key, JSON.stringify(item));
    } catch (e) {
      console.error('Error saving to localStorage:', e);
    }
  },
  
  /**
   * Mengambil data dari localStorage
   * @param key Kunci localStorage
   * @returns Data atau null jika tidak ditemukan/kadaluarsa
   */
  get<T>(key: string): T | null {
    try {
      const itemStr = localStorage.getItem(key);
      if (!itemStr) return null;
      
      const item: CacheItem<T> = JSON.parse(itemStr);
      
      // Cek kadaluarsa
      if (item.expiry && Date.now() > item.expiry) {
        localStorage.removeItem(key);
        return null;
      }
      
      return item.value;
    } catch (e) {
      console.error('Error reading from localStorage:', e);
      return null;
    }
  },
  
  /**
   * Menghapus item dari localStorage
   * @param key Kunci localStorage
   */
  remove(key: string): void {
    localStorage.removeItem(key);
  },
  
  /**
   * Membersihkan semua item di localStorage yang kadaluarsa
   */
  prune(): void {
    const now = Date.now();
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (!key) continue;
      
      try {
        const itemStr = localStorage.getItem(key);
        if (!itemStr) continue;
        
        const item = JSON.parse(itemStr);
        if (item.expiry && now > item.expiry) {
          localStorage.removeItem(key);
        }
      } catch (e) {
        console.error('Error pruning localStorage:', e);
        // Abaikan item yang bukan CacheItem
        continue;
      }
    }
  }
}; 
