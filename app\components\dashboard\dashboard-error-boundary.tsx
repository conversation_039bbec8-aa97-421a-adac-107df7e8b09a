'use client';

import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { Button } from '@/app/components/ui/button';

function ErrorFallback({ error, resetErrorBoundary }: FallbackProps) {
  console.error('Dashboard error:', error);
  
  return (
    <div className="p-6 bg-red-50 border border-red-200 rounded-lg my-4">
      <h2 className="text-lg font-bold text-red-700 mb-2"><PERSON><PERSON><PERSON><PERSON></h2>
      <p className="text-red-600 mb-4">
        Gagal memuat data dashboard. Data yang ditampilkan mungkin tidak akurat.
      </p>
      <Button 
        onClick={resetErrorBoundary} 
        className="bg-red-600 hover:bg-red-700"
      >
        Coba <PERSON>gi
      </Button>
    </div>
  );
}

interface DashboardErrorBoundaryProps {
  children: React.ReactNode;
}

export default function DashboardErrorBoundary({ children }: DashboardErrorBoundaryProps) {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      {children}
    </ErrorBoundary>
  );
} 
