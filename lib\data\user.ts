import { prisma } from "@/lib/config/prisma";
import { User } from "@/lib/types/user";
import { Role } from "@/lib/types/auth";

export async function getUsers(): Promise<User[]> {
    try {
        return (await prisma.user.findMany({
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                createdAt: true,
                updatedAt: true,
            },
        })).map(user => ({ ...user, role: user.role as Role }));
    } catch (error) {
        console.error("Error fetching users:", error);
        throw new Error("Gagal mengambil data pengguna");
    }
}

export async function searchUsers(query: string, role?: string | null): Promise<User[]> {
    try {
        return (await prisma.user.findMany({
            where: {
                AND: [
                    {
                        OR: [
                            { name: { contains: query, mode: "insensitive" } },
                            { email: { contains: query, mode: "insensitive" } },
                            { phone: { contains: query, mode: "insensitive" } },
                        ],
                    },
                    role && role !== 'all' ? { role: role as Role } : {},
                ],
            },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                image: true,
                createdAt: true,
                updatedAt: true,
            },
        })).map(user => ({ ...user, role: user.role as Role }));
    } catch (error) {
        console.error("Error searching users:", error);
        throw new Error("Gagal mencari pengguna");
    }
}

export async function getUserById(id: string): Promise<User> {
    try {
        const user = await prisma.user.findUnique({
            where: { id },
            select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                role: true,
                createdAt: true,
                updatedAt: true,
            },
        });

        if (!user) {
            throw new Error("Pengguna tidak ditemukan");
        }

        return { ...user, role: user.role as Role };
    } catch (error) {
        console.error("Error fetching user:", error);
        throw new Error("Gagal mengambil data pengguna");
    }
}

export async function checkUserKnownStatus(userId: string): Promise<boolean> {
    try {
        // Cek jumlah rental yang sudah selesai oleh user
        const completedRentals = await prisma.rental.count({
            where: {
                userId: userId,
                status: "COMPLETED",
            },
        });

        // User dianggap dikenal jika memiliki lebih dari 2 rental yang selesai
        // Atau logika lain sesuai kebutuhan bisnis Anda
        return completedRentals >= 2;
    } catch (error) {
        console.error("Error checking user known status:", error);
        return false;
    }
}
