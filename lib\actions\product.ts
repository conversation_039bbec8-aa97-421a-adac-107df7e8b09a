"use server";

import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { revalidatePath } from "next/cache";
import type { UpdateProductResult } from "@/lib/types/product";
import { ProductSchema } from "@/lib/validations/product/schema";
import { notifyLowStock } from "@/lib/notifications";

export async function createProduct(
    prevState: unknown,
    formData: FormData
): Promise<UpdateProductResult> {
    console.log('createProduct dipanggil dengan formData:', Object.fromEntries(formData));
    try {
        // 1. Validasi session dan user
        const session = await getSession();
        console.log('Session di server action:', session);

        if (!session?.user) {
            console.log('Tidak ada user dalam session');
            return {
                success: false,
                error: "Unauthorized: Silakan login terlebih dahulu"
            };
        }

        console.log('User email dari session:', session.user.email);
        console.log('User ID dari session:', session.user.id);

        // 2. Coba gunakan ID dari session langsung jika tersedia
        let userId = session.user.id;

        // Jika tidak ada ID di session, cari di database
        if (!userId) {
            // Pastikan user ada di database
            const user = await prisma.user.findUnique({
                where: {
                    email: session.user.email as string
                },
                select: { id: true }
            });

            if (!user) {
                console.log('User dengan email', session.user.email, 'tidak ditemukan di database');
                return {
                    success: false,
                    error: "User tidak ditemukan"
                };
            }

            userId = user.id;
        }

        // Log untuk debugging
        console.log('User ID yang akan digunakan:', userId);

        // 3. Persiapkan data dari form
        const rawData = {
            name: String(formData.get('name') || ''),
            capacity: Number(formData.get('capacity')) || 0,
            price: Number(formData.get('price')) || 0,
            stock: Number(formData.get('stock')) || 0,
            description: formData.get('description') ? String(formData.get('description')) : null,
            imageUrl: formData.get('imageUrl') ? String(formData.get('imageUrl')) : null,
            category: formData.get('category') ? String(formData.get('category')) : null,
            overtimeRate: Number(formData.get('overtimeRate')) || 0,
            status: "AVAILABLE" as const
        };

        // Log untuk debugging
        console.log('Raw form data:', rawData);

        // 4. Validasi data
        const validatedData = ProductSchema.safeParse(rawData);
        if (!validatedData.success) {
            return {
                success: false,
                error: validatedData.error.errors[0].message
            };
        }

        // 5. Simpan ke database dengan user.id yang valid
        const payload = {
            ...validatedData.data,
            userId,
            category: validatedData.data.category || '',
        };

        // Log the payload to ensure it's not null
        console.log('Payload for product creation:', payload);

        // Pastikan payload dan userId ada dan valid
        if (!payload || !userId) {
            console.error('Payload atau userId tidak valid', { payload, userId });
            throw new Error("Data produk tidak lengkap");
        }

        // Buat objek data secara eksplisit untuk memastikan semua field ada
        const productData = {
            name: payload.name,
            capacity: payload.capacity,
            price: payload.price,
            stock: payload.stock || 0,
            description: payload.description || null,
            imageUrl: payload.imageUrl || null,
            category: payload.category || '',
            overtimeRate: payload.overtimeRate || 0,
            status: payload.status || "AVAILABLE",
            userId
        };

        console.log('Final product data for creation:', productData);

        const product = await prisma.product.create({
            data: productData,
            include: {
                user: true,
            },
        });

        console.log('Product created successfully:', product);

        // Revalidate all pages that display products
        revalidatePath("/");
        revalidatePath("/admin/products");
        revalidatePath("/user/catalog");
        revalidatePath("/(dashboard)/admin/products");
        revalidatePath("/(dashboard)/user/catalog");

        return {
            success: true,
            data: product
        };

    } catch (error) {
        console.error('Error creating product:', error);
        // Log full error details
        if (error instanceof Error) {
            console.error('Error name:', error.name);
            console.error('Error message:', error.message);
            console.error('Error stack:', error.stack);
        }

        return {
            success: false,
            error: error instanceof Error && error.message ? error.message : 'Terjadi kesalahan saat membuat produk',
        };
    }
}

export async function updateProduct(
    id: string,
    formData: FormData
): Promise<UpdateProductResult> {
    try {
        const data = {
            name: formData.get('name') as string,
            capacity: Number(formData.get('capacity')),
            price: Number(formData.get('price')),
            stock: Number(formData.get('stock')),
            description: formData.get('description') as string || null,
            imageUrl: formData.get('imageUrl') as string || null,
            category: formData.get('category') as string || null,
            overtimeRate: Number(formData.get('overtimeRate')) || 0,
            status: formData.get('status') as "AVAILABLE" | "NOT_AVAILABLE" | "MAINTENANCE",
        };

        const validatedData = ProductSchema.safeParse(data);
        if (!validatedData.success) {
            return {
                success: false,
                error: validatedData.error.errors[0].message
            };
        }

        const session = await getSession();
        if (!session?.user?.id) {
            return {
                success: false,
                error: "Unauthorized"
            };
        }

        const product = await prisma.product.update({
            where: { id },
            data: {
                ...validatedData.data,
                status: validatedData.data.status as "AVAILABLE" | "NOT_AVAILABLE" | "MAINTENANCE",
                category: validatedData.data.category || '',
            },
            include: {
                user: true,
            },
        });

        revalidatePath("/");
        revalidatePath("/admin/products");
        revalidatePath("/user/catalog");
        revalidatePath("/(dashboard)/admin/products");
        revalidatePath("/(dashboard)/user/catalog");

        return {
            success: true,
            data: product
        };
    } catch (error) {
        console.error('Error updating product:', error);
        return {
            success: false,
            error: 'Gagal mengupdate produk'
        };
    }
}

export async function deleteProduct(id: string): Promise<UpdateProductResult> {
    try {
        const session = await getSession();
        if (!session?.user) {
            return {
                success: false,
                error: "Unauthorized"
            };
        }

        await prisma.product.delete({
            where: { id }
        });

        revalidatePath("/");
        revalidatePath("/admin/products");
        revalidatePath("/user/catalog");
        revalidatePath("/(dashboard)/admin/products");
        revalidatePath("/(dashboard)/user/catalog");

        return { success: true };
    } catch (error) {
        console.error('Error deleting product:', error);
        return {
            success: false,
            error: 'Gagal menghapus produk'
        };
    }
}

export async function updateProductStock(
    productId: string,
    quantityChange: number
): Promise<UpdateProductResult> {
    try {
        if (!productId) {
            console.error('[STOCK UPDATE] ❌ Product ID tidak valid');
            return {
                success: false,
                error: "Product ID tidak valid"
            };
        }

        if (typeof quantityChange !== 'number' || isNaN(quantityChange)) {
            console.error('[STOCK UPDATE] ❌ Perubahan kuantitas tidak valid:', quantityChange);
            return {
                success: false,
                error: "Perubahan kuantitas harus berupa angka"
            };
        }

        console.log(`[STOCK UPDATE] Memperbarui stok produk ${productId} dengan perubahan: ${quantityChange}`);

        // Ambil data produk dulu untuk mengetahui stok saat ini
        const product = await prisma.product.findUnique({
            where: { id: productId },
            select: { stock: true, name: true }
        });

        if (!product) {
            console.error(`[STOCK UPDATE] Produk dengan ID ${productId} tidak ditemukan`);
            return {
                success: false,
                error: "Produk tidak ditemukan"
            };
        }

        console.log(`[STOCK UPDATE] Stok sebelum perubahan: ${product.stock}`);
        const newStock = product.stock + quantityChange;
        console.log(`[STOCK UPDATE] Stok setelah perubahan akan menjadi: ${newStock}`);

        // Validasi stok tidak boleh negatif
        if (newStock < 0) {
            console.error(`[STOCK UPDATE] ⚠️ Operasi dibatalkan! Stok tidak cukup. Stok saat ini: ${product.stock}, Perubahan: ${quantityChange}`);
            return {
                success: false,
                error: `Stok tidak cukup. Tersedia: ${product.stock}, Dibutuhkan: ${Math.abs(quantityChange)}`
            };
        }

        // Update stok produk
        const updatedProduct = await prisma.product.update({
            where: { id: productId },
            data: { stock: newStock }
        });

        console.log(`[STOCK UPDATE] ✅ Stok berhasil diperbarui untuk produk ${updatedProduct.name} (${productId}): ${updatedProduct.stock}`);

        // Kirim notifikasi jika stok hampir habis (kurang dari atau sama dengan 3)
        if (newStock <= 3) {
            try {
                await notifyLowStock(productId, newStock);
                console.log(`[STOCK UPDATE] 🔔 Notifikasi stok hampir habis dikirim untuk produk ${updatedProduct.name}`);
            } catch (notifyError) {
                console.error(`[STOCK UPDATE] ⚠️ Gagal mengirim notifikasi stok hampir habis: ${notifyError instanceof Error ? notifyError.message : 'Unknown error'}`);
                // Lanjutkan proses meskipun notifikasi gagal
            }
        }

        // Revalidasi path terkait
        revalidatePath("/admin/products");
        revalidatePath("/user/catalog");
        revalidatePath("/");

        return {
            success: true,
            data: updatedProduct
        };
    } catch (error) {
        console.error('[STOCK UPDATE] ❌ Error updating product stock:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Gagal memperbarui stok produk"
        };
    }
}
// ... kode actions product
