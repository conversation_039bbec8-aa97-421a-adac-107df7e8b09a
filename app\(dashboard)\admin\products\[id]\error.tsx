'use client';

import { useEffect } from "react";
import Link from "next/link";

export default function ProductError({
    error,
    reset,
}: {
    error: Error & { digest?: string };
    reset: () => void;
}) {
    useEffect(() => {
        console.error(error);
    }, [error]);

    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                    <PERSON><PERSON><PERSON><PERSON>
                </h1>
                <p className="text-lg text-gray-600 mb-8">
                    {error.message || "Ter<PERSON>di kesalahan saat memuat data produk"}
                </p>
                <div className="space-x-4">
                    <button
                        onClick={reset}
                        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                        Coba <PERSON>gi
                    </button>
                    <Link
                        href="/admin/products"
                        className="text-blue-600 hover:text-blue-800 font-medium"
                    >
                        Kembali ke Dashboard
                    </Link>
                </div>
            </div>
        </div>
    );
} 