import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy API untuk Photon Geocoding
 * Mengatasi masalah CORS saat mengakses API Photon dari browser
 */
export async function GET(request: NextRequest) {
  try {
    // Ambil parameter dari query string
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('q');
    const lat = searchParams.get('lat');
    const lon = searchParams.get('lon');
    // Photon hanya mendukung bahasa: default, en, de, fr
    // Untuk Indonesia, gunakan 'default' yang akan memberikan nama lokal
    const requestedLang = searchParams.get('lang') || 'default';
    const supportedLangs = ['default', 'en', 'de', 'fr'];
    const lang = supportedLangs.includes(requestedLang) ? requestedLang : 'default';
    const limit = searchParams.get('limit') || '5';
    
    console.log(`[Photon API] Parameter permintaan:`, { query, lat, lon, lang, limit });
    
    // Tentukan apakah ini permintaan reverse geocoding atau pencarian normal
    let url = '';
    if (lat && lon) {
      // Ini adalah permintaan reverse geocoding
      url = `https://photon.komoot.io/reverse?lat=${lat}&lon=${lon}&lang=${lang}`;
    } else if (query) {
      // Ini adalah permintaan pencarian biasa
      url = `https://photon.komoot.io/api/?q=${encodeURIComponent(query)}+NTB&limit=${limit}&lang=${lang}`;
    } else {
      // Tidak ada parameter yang diperlukan
      console.error('[Photon API] Parameter yang diperlukan tidak ada');
      return NextResponse.json(
        { error: 'Parameter query (q) atau koordinat (lat & lon) diperlukan' },
        { status: 400 }
      );
    }
    
    console.log(`[Photon API] Mengirim permintaan ke: ${url}`);
    
    // Kirim permintaan ke API Photon dengan timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 detik timeout
    
    try {
      // Kirim permintaan ke API Photon
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'RentalGansetApp/1.0'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Jika API merespons dengan error
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[Photon API] Error dari API Photon: ${response.status} ${response.statusText}`, errorText);
        return NextResponse.json(
          { error: `Error dari API Photon: ${response.status} ${response.statusText}` },
          { status: response.status }
        );
      }
      
      // Ambil data dari respons API
      const data = await response.json();
      console.log(`[Photon API] Berhasil mendapatkan respon: ${data.features?.length || 0} hasil`);
      
      // Kirim respons
      return NextResponse.json(data);
    } catch (fetchError: unknown) {
      clearTimeout(timeoutId);
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('[Photon API] Permintaan ke API Photon timeout');
        return NextResponse.json(
          { error: 'API Photon timeout' },
          { status: 504 }
        );
      }
      
      console.error('[Photon API] Gagal fetch API Photon:', fetchError);
      return NextResponse.json(
        { error: `Gagal mengakses API Photon: ${fetchError instanceof Error ? fetchError.message : 'Kesalahan tidak diketahui'}` },
        { status: 502 }
      );
    }
  } catch (error) {
    console.error('[Photon API] Error saat mengakses API Photon:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
} 
