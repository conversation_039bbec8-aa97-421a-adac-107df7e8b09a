'use client';

import { useState } from 'react';
import Image from 'next/image';
import { uploadToBlob } from '@/lib/utils/upload';

interface UploadFormProps {
    onImageUploaded: (url: string) => void;
    currentImageUrl?: string | null;
}

export default function UploadForm({ onImageUploaded, currentImageUrl }: UploadFormProps) {
    const [isUploading, setIsUploading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [preview, setPreview] = useState<string | null>(currentImageUrl || null);

    const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files?.[0]) return;

        setIsUploading(true);
        setError(null);

        try {
            const file = e.target.files[0];

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                throw new Error('File terlalu besar (maksimal 5MB)');
            }

            // Create preview
            const objectUrl = URL.createObjectURL(file);
            setPreview(objectUrl);

            const result = await uploadToBlob(file);

            if (result.success && result.url) {
                onImageUploaded(result.url);
            } else {
                throw new Error(result.error || 'Gagal mengupload gambar');
            }
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Terjadi kesalahan');
            setPreview(null);
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <div className="space-y-4">
            {error && (
                <div className="text-red-500 dark:text-red-400 text-sm">{error}</div>
            )}
            {preview && (
                <div className="relative w-32 h-32 dark:border dark:border-gray-700 dark:rounded-lg">
                    <Image
                        src={preview}
                        alt="Preview"
                        fill
                        className="object-cover rounded-lg"
                    />
                </div>
            )}
            <div className="flex items-center gap-4">
                <input
                    type="file"
                    accept="image/*"
                    onChange={handleUpload}
                    disabled={isUploading}
                    className="hidden"
                    id="image-upload"
                />
                <label
                    htmlFor="image-upload"
                    className={`cursor-pointer px-4 py-2 rounded-md ${isUploading
                            ? 'bg-gray-400 dark:bg-gray-600'
                            : 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800'
                        } text-white`}
                >
                    {isUploading ? 'Mengupload...' : 'Upload Gambar'}
                </label>
            </div>
        </div>
    );
} 
