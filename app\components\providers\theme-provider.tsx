"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";

interface ThemeChangeEvent extends Event {
  detail?: {
    prevTheme?: string;
    theme?: string;
  };
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [mounted, setMounted] = React.useState(false);

  // Memastikan tidak ada masalah hydration
  React.useEffect(() => {
    setMounted(true);
  }, []);

  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Tambahkan event listener untuk theme change
      const handleThemeChange = (e: ThemeChangeEvent) => {
        console.log('Theme changed:', e.detail?.prevTheme, 'to', e.detail?.theme);
        
        // Force refresh classes pada elemen html
        const theme = e.detail?.theme || 'light';
        const htmlElement = document.documentElement;
        
        // Hapus kelas tema lama jika ada
        htmlElement.classList.remove('light', 'dark');
        
        // Tambahkan kelas tema baru (untuk darkMode: "class")
        if (theme === 'dark') {
          htmlElement.classList.add('dark');
        } else {
          htmlElement.classList.add('light');
        }
        
        // Setel atribut data-theme
        htmlElement.setAttribute('data-theme', theme);
        
        // Log status untuk debugging
        console.log('HTML element attributes:', {
          'data-theme': htmlElement.getAttribute('data-theme'),
          'classes': htmlElement.className
        });
      };
      
      document.addEventListener('themeChange', handleThemeChange);
      
      return () => {
        document.removeEventListener('themeChange', handleThemeChange);
      };
    }
    
    return undefined;
  }, []);

  // Bagia properties untuk NextThemesProvider dengan tipe yang benar
  const themeProviderProps = {
    ...props,
    attribute: "data-theme" as const, // Cast ke const untuk mencocokkan tipe Attribute
    defaultTheme: "light",
    enableSystem: true,
    disableTransitionOnChange: false,
    enableColorScheme: true,
    storageKey: "rental-genset-theme",
    forcedTheme: !mounted ? "light" : undefined,
  };

  return (
    <NextThemesProvider {...themeProviderProps}>
      {children}
    </NextThemesProvider>
  );
}
