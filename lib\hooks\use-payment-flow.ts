"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { usePaymentAnimationContext } from "@/app/components/providers/payment-animation-provider";
import { useToast } from "@/lib/hooks/use-toast";
import { formatCurrency } from "@/lib/utils/format";

interface PaymentFlowOptions {
  rentalId: string;
  type: "deposit" | "remaining" | "full";
  amount: number;
  onSuccess?: (data: PaymentResponse) => void;
  onError?: (error: Error) => void;
}

interface PaymentResponse {
  success: boolean;
  message: string;
  token?: string;
  paymentId?: string;
  redirectUrl?: string;
}

export function usePaymentFlow() {
  const router = useRouter();
  const { showSuccess: showSuccessAnimation, showError: showErrorAnimation, showProcessing, hideAnimation } = usePaymentAnimationContext();
  const { showSuccess, showError } = useToast();

  const processPayment = useCallback(async ({ 
    rentalId, 
    type, 
    amount, 
    onSuccess, 
    onError 
  }: PaymentFlowOptions) => {
    try {
      // Check if Snap is available
      if (typeof window === 'undefined' || !window.snap) {
        throw new Error('Sistem pembayaran Midtrans belum siap. Pastikan file .env.local berisi NEXT_PUBLIC_MIDTRANS_CLIENT_KEY.');
      }
      
      // Show processing animation
      showProcessing(
        "Memproses Pembayaran",
        "Mohon tunggu, kami sedang memproses pembayaran Anda...",
        formatCurrency(amount)
      );

      // Tentukan endpoint API berdasarkan tipe pembayaran
      const endpoint = type === "deposit"
        ? `/api/payments/deposit/${rentalId}`
        : type === "remaining"
          ? `/api/payments/remaining/${rentalId}`
          : `/api/payments/full/${rentalId}`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ amount }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Terjadi kesalahan dalam proses pembayaran");
      }

      const data = await response.json() as PaymentResponse;

      if (!data.token) {
        throw new Error("Token pembayaran tidak ditemukan");
      }

      return new Promise<void>((resolve, reject) => {
        try {
          // Buka Midtrans Snap popup
          window.snap?.pay(data.token, {
            onSuccess: (result) => {
              console.log('Payment success:', result);
              hideAnimation(); // Hide processing animation

              const successTitle = type === "deposit"
                ? "Deposit Berhasil Dibayar!"
                : type === "remaining"
                  ? "Pembayaran Lunas!"
                  : "Pembayaran Berhasil!";

              const successMessage = type === "deposit"
                ? "Deposit telah berhasil dibayar. Pesanan Anda sedang diproses."
                : type === "remaining"
                  ? "Pelunasan telah berhasil dibayar. Terima kasih atas kepercayaan Anda."
                  : "Pembayaran telah berhasil dilakukan.";

              // Show success animation with callback
              showSuccessAnimation(successTitle, successMessage, formatCurrency(amount));
              showSuccess(successMessage);

              // Don't auto-redirect, let user click button in animation
              if (onSuccess) {
                setTimeout(() => {
                  onSuccess({ ...data, success: true });
                  resolve();
                }, 2000);
              } else {
                setTimeout(() => {
                  resolve();
                }, 2000);
              }

              // Animation will stay visible until user clicks button
            },
            onPending: (result) => {
              console.log('Payment pending:', result);
              hideAnimation();
              showSuccess('Pembayaran sedang diproses. Silakan tunggu konfirmasi.');
              resolve();
            },
            onError: (result) => {
              console.error('Payment error:', result);

              showErrorAnimation(
                "Pembayaran Gagal",
                "Terjadi kesalahan dalam proses pembayaran. Silakan coba lagi.",
                formatCurrency(amount)
              );

              // Don't auto-redirect, let user click button in animation
              const paymentError = new Error('Pembayaran gagal: ' + (result.message || 'Terjadi kesalahan'));
              
              if (onError) {
                setTimeout(() => {
                  onError(paymentError);
                  reject(paymentError);
                }, 2000);
              } else {
                setTimeout(() => {
                  reject(paymentError);
                }, 2000);
              }

              // Animation will stay visible until user clicks button
            },
            onClose: () => {
              console.log('Payment popup closed');
              hideAnimation();
              showError('Pembayaran dibatalkan. Anda dapat mencoba lagi nanti.');
            }
          });
        } catch (snapError) {
          console.error('Error executing Snap pay:', snapError);
          hideAnimation();
          const formattedError = new Error('Kesalahan eksekusi Snap: ' + (snapError instanceof Error ? snapError.message : 'Unknown error'));
          showError('Terjadi kesalahan saat menjalankan pembayaran. Silakan refresh halaman dan coba lagi.');
          
          if (onError) onError(formattedError);
          reject(formattedError);
        }
      });

    } catch (error: unknown) {
      console.error("Error processing payment:", error);
      hideAnimation();
      
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan dalam proses pembayaran";

      showErrorAnimation(
        "Pembayaran Gagal",
        errorMessage,
        formatCurrency(amount)
      );
      
      showError(errorMessage);

      // Don't auto-redirect, let user click button in animation
      if (onError) {
        setTimeout(() => {
          onError(error instanceof Error ? error : new Error(errorMessage));
        }, 2000);
      }

      // Animation will stay visible until user clicks button

      throw error;
    }
  }, [router, showSuccessAnimation, showErrorAnimation, showProcessing, hideAnimation, showSuccess, showError]);

  return {
    processPayment
  };
}
