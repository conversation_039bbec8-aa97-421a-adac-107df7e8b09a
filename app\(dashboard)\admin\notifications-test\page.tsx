"use client";

import { useState } from "react";
import { NotificationTest } from "@/app/components/shared/notification/notification-test";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { Bell, Settings, CheckCircle, Sparkles, Play } from "lucide-react";
import {
  SlideInContainer,
  FadeIn,
  ScaleIn,
  LoadingDots
} from "@/app/components/shared/notification/notification-animations";
import { SmoothToast } from "@/app/components/shared/notification/smooth-toast";

export default function NotificationsTestPage() {
  const [showToast, setShowToast] = useState(false);
  const [toastType, setToastType] = useState<"success" | "error" | "warning" | "info">("success");

  const handleShowToast = (type: "success" | "error" | "warning" | "info") => {
    setToastType(type);
    setShowToast(true);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <FadeIn>
        <div className="flex items-center justify-between">
          <SlideInContainer direction="left">
            <div>
              <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
                Notification System Test
              </h1>
              <p className="text-slate-600 dark:text-slate-400 mt-2">
                Test dan monitor sistem notifikasi real-time dengan animasi GSAP
              </p>
            </div>
          </SlideInContainer>
          <SlideInContainer direction="right" delay={0.2}>
            <Badge variant="secondary" className="flex items-center gap-2">
              <Sparkles className="w-4 h-4" />
              GSAP Animations
            </Badge>
          </SlideInContainer>
        </div>
      </FadeIn>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Test Component */}
        <SlideInContainer direction="up" delay={0.3}>
          <NotificationTest />
        </SlideInContainer>

        {/* Animation Demo */}
        <SlideInContainer direction="up" delay={0.4}>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Play className="w-5 h-5 text-violet-600" />
                Animation Demo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold">Toast Notifications</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleShowToast("success")}
                    className="text-green-600 border-green-200 hover:bg-green-50"
                  >
                    Success
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleShowToast("error")}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Error
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleShowToast("warning")}
                    className="text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                  >
                    Warning
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleShowToast("info")}
                    className="text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    Info
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Loading Animation</h4>
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                  <LoadingDots />
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-semibold">Scale Animation</h4>
                <ScaleIn delay={0}>
                  <div className="p-4 bg-violet-50 dark:bg-violet-900/20 rounded-lg text-center">
                    <Sparkles className="w-8 h-8 mx-auto text-violet-600" />
                    <p className="text-sm mt-2">Animated Element</p>
                  </div>
                </ScaleIn>
              </div>
            </CardContent>
          </Card>
        </SlideInContainer>

        {/* Instructions */}
        <SlideInContainer direction="up" delay={0.5}>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Testing Instructions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                    1. Real-time Sync Test
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Buka aplikasi di multiple tabs atau browser windows, lalu create test notification.
                    Perubahan harus langsung terlihat di semua tabs.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-green-900 dark:text-green-100">
                    2. Cross-component Sync Test
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Mark notification sebagai read di navbar dropdown, lalu check sidebar.
                    Status harus sinkron tanpa reload.
                  </p>
                </div>

                <div className="border-l-4 border-purple-500 pl-4">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100">
                    3. Network Status Test
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Disconnect internet, lalu reconnect. Sistem harus auto-refresh
                    saat connection restored.
                  </p>
                </div>

                <div className="border-l-4 border-orange-500 pl-4">
                  <h4 className="font-semibold text-orange-900 dark:text-orange-100">
                    4. Focus/Blur Test
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    Switch ke tab/window lain, lalu kembali. Notifications harus
                    auto-refresh saat window focus.
                  </p>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Bell className="w-4 h-4" />
                  Expected Behavior
                </h4>
                <ul className="text-sm space-y-1 text-slate-600 dark:text-slate-400">
                  <li>• Notification badge updates instantly</li>
                  <li>• Cross-tab synchronization works</li>
                  <li>• Auto-refresh on focus/online</li>
                  <li>• Optimistic updates with rollback on error</li>
                  <li>• Debounced requests (max 1 per 2 seconds)</li>
                </ul>
              </div>

              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                  ⚠️ Troubleshooting
                </h4>
                <ul className="text-sm space-y-1 text-yellow-700 dark:text-yellow-300">
                  <li>• Check browser console for errors</li>
                  <li>• Verify API endpoints are responding</li>
                  <li>• Test with different user roles</li>
                  <li>• Clear localStorage if sync issues persist</li>
                </ul>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
                  🧪 Automated Testing
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                  Jalankan script testing otomatis di browser console:
                </p>
                <div className="bg-blue-100 dark:bg-blue-900/40 rounded p-2 font-mono text-xs">
                  <div>1. Buka Developer Tools (F12)</div>
                  <div>2. Paste script dari: /scripts/test-notifications.js</div>
                  <div>3. Jalankan: testNotifications()</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </SlideInContainer>
      </div>

      {/* Toast Component */}
      <SmoothToast
        type={toastType}
        title={`${toastType.charAt(0).toUpperCase() + toastType.slice(1)} Toast`}
        message={`Ini adalah contoh ${toastType} toast dengan animasi GSAP yang smooth!`}
        isVisible={showToast}
        onClose={() => setShowToast(false)}
        position="top-right"
        duration={4000}
      />
    </div>
  );
}
