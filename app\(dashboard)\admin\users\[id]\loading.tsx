export default function UserDetailLoading() {
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="h-10 w-24 bg-gray-200 rounded animate-pulse" /> {/* Back button */}
      
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <div className="h-8 w-48 bg-gray-200 rounded mb-4 animate-pulse" /> {/* Name */}
          <div className="grid grid-cols-2 gap-6">
            <div>
              <div className="h-6 w-40 bg-gray-200 rounded mb-4 animate-pulse" /> {/* Section title */}
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" /> {/* Label */}
                    <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" /> {/* Value */}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <div className="h-6 w-40 bg-gray-200 rounded mb-4 animate-pulse" /> {/* Section title */}
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 