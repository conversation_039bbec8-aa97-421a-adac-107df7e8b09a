import { auth } from "./config";
import { headers } from "next/headers";
import { cache } from "react";

export const getSession = cache(async () => {
  const headersList = await headers();
  return await auth.api.getSession({
    headers: headersList,
  });
});

export const getCurrentUser = cache(async () => {
  const session = await getSession();
  return session?.user ?? null;
});

export const requireAuth = async () => {
  const session = await getSession();
  if (!session?.user) {
    throw new Error("Authentication required");
  }
  return session;
};

export const requireAdmin = async () => {
  const session = await requireAuth();
  if (session.user.role !== "ADMIN") {
    throw new Error("Admin access required");
  }
  return session;
};

export type { Session, User } from "./config";
