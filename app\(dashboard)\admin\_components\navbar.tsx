'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { signOut } from '@/lib/auth/client';
import { enhancedLogout } from '@/lib/utils/logout';
import {
    LuLayoutDashboard,
    LuBox,
    LuShoppingCart,
    LuCreditCard,
    LuUsers,
    LuLogOut
} from 'react-icons/lu';

const menuItems = [
    {
        href: '/admin/dashboard',
        title: 'Dashboard',
        icon: LuLayoutDashboard
    },
    {
        href: '/admin/products',
        title: 'Produk',
        icon: LuBox
    },
    {
        href: '/admin/rental',
        title: 'Rental',
        icon: LuShoppingCart
    },
    {
        href: '/admin/payments',
        title: 'Pembayaran',
        icon: LuCreditCard
    },
    {
        href: '/admin/users',
        title: 'Pengguna',
        icon: LuUsers
    }
];

export function Navbar() {
    const pathname = usePathname();

    return (
        <nav className="flex items-center space-x-4">
            {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                return (
                    <Link
                        key={item.href}
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${isActive
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-600 hover:bg-gray-50'
                            }`}
                    >
                        <Icon className="w-4 h-4 mr-2" />
                        {item.title}
                    </Link>
                );
            })}
            <button
                onClick={() => enhancedLogout(signOut)}
                className="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700"
            >
                <LuLogOut className="w-4 h-4 mr-2" />
                Keluar
            </button>
        </nav>
    );
} 
