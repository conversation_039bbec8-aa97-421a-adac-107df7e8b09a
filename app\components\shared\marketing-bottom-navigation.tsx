'use client';

import {
    LuLayoutDashboard as <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ackage
} from 'react-icons/lu';
import { BottomNavigation } from './bottom-navigation';

const menuItems = [
    {
        href: '/',
        title: '<PERSON><PERSON>a',
        icon: LuHome
    },
    {
        href: '/user/catalog',
        title: 'Katalog',
        icon: LuPackage
    },
    {
        href: '/login',
        title: '<PERSON><PERSON><PERSON>',
        icon: LuU<PERSON>
    }
];

export function MarketingBottomNavigation() {
    return <BottomNavigation menuItems={menuItems} variant="marketing" />;
}
