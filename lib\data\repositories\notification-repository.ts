import { Notification } from '@prisma/client';
import { db } from '../db';

// Enum values sebagai string literals
export const NotificationTypes = {
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  NEW_RENTAL: 'NEW_RENTAL',
  RENTAL_CONFIRMED: 'RENTAL_CONFIRMED',
  OPERATION_STARTED: 'OPERATION_STARTED',
  OPERATION_COMPLETED: 'OPERATION_COMPLETED',
  LOW_STOCK: 'LOW_STOCK',
  OVERTIME_DETECTED: 'OVERTIME_DETECTED',
  NEW_PAYMENT: 'NEW_PAYMENT',
  NEW_INVOICE: 'NEW_INVOICE'
} as const;

export type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];

export class NotificationRepository {
  async findById(id: string): Promise<Notification | null> {
    return db.notification.findUnique({
      where: { id }
    });
  }

  async findByUserId(userId: string, options?: {
    limit?: number,
    offset?: number,
    onlyUnread?: boolean
  }): Promise<Notification[]> {
    return db.notification.findMany({
      where: {
        userId,
        ...(options?.onlyUnread ? { isRead: false } : {})
      },
      take: options?.limit || 20,
      skip: options?.offset || 0,
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async create(data: {
    userId: string,
    title: string,
    message: string,
    type: NotificationType
  }): Promise<Notification> {
    return db.notification.create({
      data: {
        ...data,
        isRead: false
      }
    });
  }

  async markAsRead(id: string): Promise<Notification> {
    return db.notification.update({
      where: { id },
      data: { isRead: true }
    });
  }

  async markAllAsRead(userId: string): Promise<{ count: number }> {
    const result = await db.notification.updateMany({
      where: {
        userId,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    return { count: result.count };
  }

  async delete(id: string): Promise<Notification> {
    return db.notification.delete({
      where: { id }
    });
  }

  async deleteAll(userId: string): Promise<{ count: number }> {
    const result = await db.notification.deleteMany({
      where: { userId }
    });

    return { count: result.count };
  }

  async getUnreadCount(userId: string): Promise<number> {
    return db.notification.count({
      where: {
        userId,
        isRead: false
      }
    });
  }
} 
