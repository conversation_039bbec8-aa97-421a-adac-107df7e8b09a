import { z } from "zod";

// Definisikan status pembayaran yang valid
export const PAYMENT_STATUS = ["DEPOSIT_PENDING", "DEPOSIT_PAID", "FULLY_PAID", "FAILED", "INVOICE_ISSUED"] as const;

// Schema validasi untuk upload file
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

export const ImageSchema = z.object({
  image: z.instanceof(File, { message: "File harus diunggah" })
    .refine((file) => file.size <= MAX_FILE_SIZE, {
      message: `Ukuran file maksimal 5MB`,
    })
    .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
      message: "Format file harus JPG, JPEG, PNG, atau WEBP",
    })
    .optional(),
});

/**
 * Schema untuk validasi pembayaran
 */
export const PaymentSchema = z.object({
  rentalId: z.string({
    required_error: "ID rental harus diisi",
  }).min(1, { message: "ID rental harus diisi" }),
  
  amount: z.number({
    required_error: "Jumlah pembayaran harus diisi",
    invalid_type_error: "Jumlah harus berupa angka"
  })
    .min(1, { message: "Jumlah pembayaran harus lebih dari 0" })
    .max(200_000_000, { message: "Jumlah pembayaran maksimal 200 juta rupiah" }),
  
  status: z.enum(PAYMENT_STATUS, {
    required_error: "Status pembayaran harus diisi",
    invalid_type_error: "Status pembayaran tidak valid",
    message: "Status pembayaran tidak valid"
  }).default("DEPOSIT_PENDING"),
  
  paymentMethod: z.enum([
    "credit_card",
    "bank_transfer",
    "gopay",
    "shopeepay"
  ], {
    message: "Metode pembayaran tidak valid"
  }).optional(),
  
  paymentProof: ImageSchema.shape.image.optional(),
});

/**
 * Schema untuk validasi transaksi dari payment gateway
 */
export const TransactionSchema = z.object({
  orderId: z.string().min(1, { message: "Order ID harus diisi" }),
  transactionId: z.string().min(1, { message: "Transaction ID harus diisi" }),
  paymentType: z.string().min(1, { message: "Tipe pembayaran harus diisi" }),
  grossAmount: z.number().min(1, { message: "Jumlah pembayaran harus lebih dari 0" }),
  currency: z.enum(["IDR"], { message: "Mata uang tidak valid" }).default("IDR"),
  transactionTime: z.string().datetime({ message: "Format waktu transaksi tidak valid" }),
  transactionStatus: z.enum(PAYMENT_STATUS, { message: "Status transaksi tidak valid" }),
});

// Tipe-tipe yang diekspor
export type PaymentInput = z.infer<typeof PaymentSchema>;
export type TransactionInput = z.infer<typeof TransactionSchema>;

// Membuat objek schemas untuk ekspor
const paymentSchemas = {
  PaymentSchema,
  TransactionSchema,
  ImageSchema,
  PAYMENT_STATUS
};

export default paymentSchemas; 
