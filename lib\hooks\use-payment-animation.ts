"use client";

import { useState, useCallback } from "react";

interface PaymentAnimationState {
  isVisible: boolean;
  type: "success" | "error" | "processing";
  title: string;
  message: string;
  amount?: string;
}

export function usePaymentAnimation() {
  const [animationState, setAnimationState] = useState<PaymentAnimationState>({
    isVisible: false,
    type: "processing",
    title: "",
    message: "",
    amount: undefined
  });

  const showSuccess = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "success",
      title,
      message,
      amount
    });
  }, []);

  const showError = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "error",
      title,
      message,
      amount
    });
  }, []);

  const showProcessing = useCallback((title: string, message: string, amount?: string) => {
    setAnimationState({
      isVisible: true,
      type: "processing",
      title,
      message,
      amount
    });
  }, []);

  const hideAnimation = useCallback(() => {
    setAnimationState(prev => ({
      ...prev,
      isVisible: false
    }));
  }, []);

  return {
    animationState,
    showSuccess,
    showError,
    showProcessing,
    hideAnimation
  };
}
