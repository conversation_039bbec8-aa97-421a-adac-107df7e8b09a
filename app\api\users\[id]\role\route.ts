import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { id } = await params;
    const { role } = await request.json();

    // Cegah admin mengubah role diri sendiri
    if (id === session.user.id) {
      return new NextResponse(
        JSON.stringify({ error: 'Tidak dapat mengubah role diri sendiri' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Cek apakah target user adalah ADMIN
    const targetUser = await prisma.user.findUnique({
      where: { id },
      select: { role: true, email: true }
    });

    if (!targetUser) {
      return new NextResponse(
        JSON.stringify({ error: 'User tidak ditemukan' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Cegah admin mengubah role admin lain
    if (targetUser.role === 'ADMIN') {
      return new NextResponse(
        JSON.stringify({ error: 'Tidak dapat mengubah role admin lain' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: { role },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user role:', error);
    return NextResponse.json(
      { error: 'Failed to update user role' },
      { status: 500 }
    );
  }
}