"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { toast } from "sonner";
import { User } from "@/lib/types/user";
import { UserAvatar } from "./user-avatar";
import { Card } from "@/app/components/ui/card";
import { Mail, Phone, User as UserIcon } from "lucide-react";

interface EditUserModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export function EditUserModal({ user, isOpen, onClose, onUpdate }: EditUserModalProps) {
  const [formData, setFormData] = useState({
    name: user.name || "",
    email: user.email,
    phone: user.phone || "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`/api/users/${user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Gagal mengupdate pengguna");
      }

      toast.success("Pengguna berhasil diupdate");
      onUpdate();
      onClose();
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Gagal mengupdate pengguna");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] w-[95vw] max-h-[90vh] overflow-y-auto bg-background border-border">
        <DialogHeader className="space-y-6">
          <div className="flex flex-col items-center space-y-4">
            <UserAvatar
              imageUrl={user.image}
              name={user.name || ''}
              size="lg"
            />
            <div className="text-center space-y-2">
              <DialogTitle className="text-2xl font-bold text-foreground">
                Edit Profil Pengguna
              </DialogTitle>
              <DialogDescription className="text-sm text-muted-foreground">
                ID: {user.id}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Card className="border-border bg-card shadow-sm">
          <form onSubmit={handleSubmit} className="space-y-6 p-6">
            <div className="space-y-4">
              <div className="relative">
                <Label htmlFor="name" className="text-sm font-medium text-foreground">
                  Nama Lengkap
                </Label>
                <div className="relative mt-2">
                  <UserIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Masukkan nama lengkap"
                    className="pl-9 h-11 bg-background border-border text-foreground placeholder:text-muted-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    required
                  />
                </div>
              </div>

              <div className="relative">
                <Label htmlFor="email" className="text-sm font-medium text-foreground">
                  Email
                </Label>
                <div className="relative mt-2">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    placeholder="Masukkan email"
                    className="pl-9 h-11 bg-background border-border text-foreground placeholder:text-muted-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
                    required
                  />
                </div>
              </div>

              <div className="relative">
                <Label htmlFor="phone" className="text-sm font-medium text-foreground">
                  Nomor WhatsApp
                </Label>
                <div className="relative mt-2">
                  <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    placeholder="Masukkan nomor WhatsApp (08xxxxxxxxxx)"
                    className="pl-9 h-11 bg-background border-border text-foreground placeholder:text-muted-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  />
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-border">
              <Button
                variant="outline"
                onClick={onClose}
                type="button"
                disabled={isLoading}
                className="flex-1 h-11 border-border bg-background hover:bg-accent hover:text-accent-foreground"
              >
                Batal
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="flex-1 h-11 bg-primary hover:bg-primary/90 text-primary-foreground disabled:opacity-50"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    <span>Menyimpan...</span>
                  </div>
                ) : (
                  "Simpan Perubahan"
                )}
              </Button>
            </div>
          </form>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
