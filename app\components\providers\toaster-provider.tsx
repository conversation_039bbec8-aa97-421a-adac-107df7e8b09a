"use client";

import { useState, useEffect } from "react";
import { Toaster } from "sonner";

// Komponen Toaster yang di-isolasi dan hanya di-render di client-side
export default function ToasterProvider() {
  // Pendekatan hydration-safe
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Cleanup saat unmount
    return () => setIsMounted(false);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <Toaster
      position="top-center"
      richColors
      closeButton
      expand={false}
      visibleToasts={3}
      toastOptions={{
        style: {
          fontSize: "14px",
        },
        className: "overflow-hidden",
      }}
    />
  );
} 
