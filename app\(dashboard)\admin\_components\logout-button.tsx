"use client";

import { But<PERSON> } from "@/app/components/ui/button";
import { signOut } from "@/lib/auth/client";
import { IoPower } from "react-icons/io5";
import { enhancedLogout } from "@/lib/utils/logout";

export function LogoutButton() {
  const handleLogout = async () => {
    await enhancedLogout(signOut);
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleLogout}
      className="flex items-center gap-2"
    >
      <IoPower className="h-4 w-4" />
      Logout
    </Button>
  );
}