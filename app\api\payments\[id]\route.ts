import { getSession } from "@/lib/auth/server";
import { prisma } from "@/lib/config/prisma";
import { NextResponse } from "next/server";

export const runtime = 'nodejs';

// GET /api/payments/[id]
export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Destructuring params untuk memastikan diawait
    const { id } = await params;
    console.log(`GET /api/payments/${id} - Request received`);

    const session = await getSession();

    if (!session?.user) {
      console.log("Unauthorized request (no user session)");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`Fetching payment with ID: ${id} for user: ${session.user.id}`);

    // Coba cari payment berdasarkan ID payment
    let payment = await prisma.payment.findUnique({
      where: { id },
      include: {
        rental: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
            product: {
              select: {
                id: true,
                name: true,
                imageUrl: true,
                image: true,
                price: true,
                capacity: true,
              },
            },
          },
        },
      },
    });

    // Jika tidak ditemukan, coba mencari payment berdasarkan rentalId
    // (jika ID yang diberikan adalah ID rental)
    if (!payment) {
      console.log(`Payment with ID ${id} not found, trying to find by rentalId`);
      payment = await prisma.payment.findUnique({
        where: { rentalId: id },
        include: {
          rental: {
            include: {
              user: {
                select: {
                  name: true,
                  email: true,
                },
              },
              product: {
                select: {
                  id: true,
                  name: true,
                  imageUrl: true,
                  image: true,
                  price: true,
                  capacity: true,
                },
              },
            },
          },
        },
      });
    }

    if (!payment) {
      console.log(`Payment with ID or rentalId ${id} not found`);
      return NextResponse.json(
        {
          error: "Payment not found",
          details: `No payment found with ID ${id} or rental ID ${id}`
        },
        { status: 404 }
      );
    }

    // Pastikan user hanya bisa mengakses pembayaran mereka sendiri
    // Atau admin dapat mengakses semua
    if (payment.rental?.userId !== session.user.id && session.user.role !== "ADMIN") {
      console.log(`Access denied: User ${session.user.id} is not the owner of payment ${id}`);
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    // Verify payment data structure
    if (!payment.rental || !payment.rental.product) {
      console.error(`Invalid payment data structure for payment ${id}`);
      return NextResponse.json(
        {
          error: "Invalid payment data structure",
          details: "Payment data is missing required rental or product information"
        },
        { status: 500 }
      );
    }

    console.log(`Payment ${payment.id} retrieved successfully`);
    return NextResponse.json(payment);
  } catch (error) {
    console.error("Error fetching payment:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch payment",
        details: error instanceof Error ? error.message : "Unknown error",
        stack: process.env.NODE_ENV === 'development' ? error instanceof Error ? error.stack : undefined : undefined
      },
      { status: 500 }
    );
  }
}

// PUT /api/payments/[id]
export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Destructuring params untuk memastikan diawait
    const { id } = await params;
    const body = await request.json();
    const { status, transactionId } = body;

    const payment = await prisma.payment.update({
      where: { id },
      data: {
        status,
        transactionId,
        updatedAt: new Date()
      },
      include: {
        rental: true
      }
    });

    return NextResponse.json(payment);
  } catch (error) {
    console.error("[PAYMENT_UPDATE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// DELETE /api/payments/[id]
export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();
    if (!session?.user || session.user.role !== 'ADMIN') {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Destructuring params untuk memastikan diawait
    const { id } = await params;
    await prisma.payment.delete({
      where: { id }
    });

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("[PAYMENT_DELETE]", error);
    return new NextResponse("Internal error", { status: 500 });
  }
}

// Untuk update status pembayaran (misalnya konfirmasi manual)
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Hanya admin yang bisa update status pembayaran
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    // Destructuring params untuk memastikan diawait
    const { id } = await params;
    const body = await req.json();
    const { status } = body;

    if (!status) {
      return NextResponse.json(
        { error: "Status is required" },
        { status: 400 }
      );
    }

    const payment = await prisma.payment.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json(payment);
  } catch (error) {
    console.error("[PAYMENT_STATUS_UPDATE]", error);
    return NextResponse.json(
      { error: "Failed to update payment status" },
      { status: 500 }
    );
  }
}