import { NextRequest, NextResponse } from 'next/server';

/**
 * Proxy API untuk OpenStreetMap Geocoding
 * Ini adalah alternatif terakhir ketika Photon dan Nominatim gagal
 * Menggunakan endpoint OSM dasar dengan parameter tambahan yang dioptimalkan
 */
export async function GET(request: NextRequest) {
  try {
    // Ambil parameter dari query string
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('q');
    
    // Parameter tambahan
    const format = searchParams.get('format') || 'json';
    const limit = searchParams.get('limit') || '10'; // Lebih banyak hasil untuk meningkatkan kemungkinan menemukan
    const addressdetails = searchParams.get('addressdetails') || '1';
    const countrycodes = searchParams.get('countrycodes') || 'id';
    const lang = searchParams.get('accept-language') || 'id';
    
    console.log(`[OSM API] Parameter permintaan:`, { query, format, limit, addressdetails, countrycodes, lang });
    
    if (!query) {
      console.error('[OSM API] Parameter query (q) tidak ada');
      return NextResponse.json(
        { error: 'Parameter query (q) diperlukan' },
        { status: 400 }
      );
    }
    
    // Tambahkan parameter tambahan untuk pencarian di Indonesia jika belum disertakan
    let searchQuery = query;
    if (!query.toLowerCase().includes('indonesia') && !query.toLowerCase().includes('nusa tenggara')) {
      searchQuery = `${query} Nusa Tenggara Barat Indonesia`;
    }
    
    // Siapkan URL untuk pencarian OSM
    const url = `https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(searchQuery)}&format=${format}&addressdetails=${addressdetails}&countrycodes=${countrycodes}&limit=${limit}&accept-language=${lang}&polygon_geojson=0&dedupe=1`;
    
    console.log(`[OSM API] Mengirim permintaan ke: ${url}`);
    
    // Kirim permintaan ke API OSM dengan timeout yang lebih panjang
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 detik timeout
    
    try {
      // Kirim permintaan ke API OSM
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'RentalGansetApp/2.0', // Gunakan User-Agent yang berbeda
          'Referer': 'https://rentalganset.id'
        },
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      // Jika API merespons dengan error
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[OSM API] Error dari API OSM: ${response.status} ${response.statusText}`, errorText);
        return NextResponse.json(
          { error: `Error dari API OSM: ${response.status} ${response.statusText}` },
          { status: response.status }
        );
      }
      
      // Ambil data dari respons API
      const data = await response.json();
      
      // Filter hasil untuk lokasi yang lebih relevan di NTB jika memungkinkan
      let filteredData = data;
      if (Array.isArray(data) && data.length > 0) {
        // Coba filter hasil untuk NTB terlebih dahulu
        const ntbResults = data.filter(item => 
          (item.display_name && item.display_name.toLowerCase().includes('nusa tenggara barat')) ||
          (item.address && item.address.state && 
           item.address.state.toLowerCase().includes('nusa tenggara barat'))
        );
        
        // Gunakan hasil NTB jika ada, jika tidak gunakan semua data
        if (ntbResults.length > 0) {
          filteredData = ntbResults;
        }
      }
      
      console.log(`[OSM API] Berhasil mendapatkan respon: ${Array.isArray(filteredData) ? filteredData.length : 'objek'}`);
      
      // Kirim respons
      return NextResponse.json(filteredData);
      
    } catch (fetchError: unknown) {
      clearTimeout(timeoutId);
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('[OSM API] Permintaan ke API OSM timeout');
        return NextResponse.json(
          { error: 'API OSM timeout' },
          { status: 504 }
        );
      }
      
      console.error('[OSM API] Gagal fetch API OSM:', fetchError);
      return NextResponse.json(
        { error: `Gagal mengakses API OSM: ${fetchError instanceof Error ? fetchError.message : 'Kesalahan tidak diketahui'}` },
        { status: 502 }
      );
    }
    
  } catch (error) {
    console.error('[OSM API] Error saat mengakses API OSM:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
} 
