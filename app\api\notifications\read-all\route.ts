import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    const session = await getSession();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Update semua notifikasi yang belum dibaca menjadi sudah dibaca
    const result = await prisma.notification.updateMany({
      where: {
        userId: session.user.id,
        isRead: false
      },
      data: {
        isRead: true
      }
    });

    return NextResponse.json({ 
      success: true,
      count: result.count
    });
  } catch (error) {
    console.error("[MARK_ALL_NOTIFICATIONS_READ_ERROR]", error);
    return NextResponse.json(
      { error: "Gagal menandai semua notifikasi sebagai dibaca" },
      { status: 500 }
    );
  }
} 
