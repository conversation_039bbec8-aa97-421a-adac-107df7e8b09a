/**
 * Utility functions untuk validasi dan formatting nomor WhatsApp Indonesia
 */

// Regex untuk validasi nomor WhatsApp Indonesia
export const whatsappRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;

/**
 * Validasi format nomor WhatsApp Indonesia
 * @param phoneNumber Nomor WhatsApp yang akan divalidasi
 * @returns boolean - true jika valid, false jika tidak
 */
export function validateWhatsAppNumber(phoneNumber: string): boolean {
  if (!phoneNumber) return false;
  // Hapus spasi dan karakter non-digit kecuali +
  const cleanNumber = phoneNumber.replace(/[\s-()]/g, '');
  return whatsappRegex.test(cleanNumber);
}

/**
 * Normalisasi nomor WhatsApp ke format +62
 * @param phoneNumber Nomor WhatsApp input
 * @returns string - nomor dalam format +62xxxxxxxxxx
 */
export function normalizeWhatsAppNumber(phoneNumber: string): string {
  if (!phoneNumber) return '';
  
  // Hapus semua karakter non-digit kecuali +
  let cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // Jika dimulai dengan 0, ganti dengan 62
  if (cleanNumber.startsWith('0')) {
    cleanNumber = '62' + cleanNumber.slice(1);
  }
  
  // Jika dimulai dengan 62, tambahkan +
  if (cleanNumber.startsWith('62')) {
    cleanNumber = '+' + cleanNumber;
  }
  
  // Jika tidak dimulai dengan +62, tambahkan +62
  if (!cleanNumber.startsWith('+62')) {
    cleanNumber = '+62' + cleanNumber;
  }
  
  return cleanNumber;
}

/**
 * Format nomor WhatsApp untuk display
 * @param phoneNumber Nomor WhatsApp
 * @returns string - nomor yang diformat untuk display
 */
export function formatWhatsAppNumber(phoneNumber: string): string {
  const normalized = normalizeWhatsAppNumber(phoneNumber);
  
  // Format: +62 8xx-xxxx-xxxx
  if (normalized.length >= 13) {
    return normalized.replace(/(\+62)(\d{3})(\d{4})(\d{4})/, '$1 $2-$3-$4');
  }
  
  return normalized;
}

/**
 * Dapatkan nomor untuk URL wa.me (tanpa + dan spasi)
 * @param phoneNumber Nomor WhatsApp
 * @returns string - nomor untuk URL wa.me
 */
export function getWhatsAppUrlNumber(phoneNumber: string): string {
  const normalized = normalizeWhatsAppNumber(phoneNumber);
  return normalized.replace(/[^\d]/g, '');
}

/**
 * Validasi dengan pesan error yang detail
 * @param phoneNumber Nomor WhatsApp
 * @returns object dengan status validasi dan pesan error
 */
export function validateWhatsAppWithMessage(phoneNumber: string): {
  isValid: boolean;
  message?: string;
  normalized?: string;
} {
  if (!phoneNumber || phoneNumber.trim() === '') {
    return {
      isValid: false,
      message: 'Nomor WhatsApp harus diisi'
    };
  }
  
  const cleanNumber = phoneNumber.replace(/[\s-()]/g, '');
  
  if (!whatsappRegex.test(cleanNumber)) {
    return {
      isValid: false,
      message: 'Format nomor WhatsApp tidak valid (contoh: 08XXXXXXXXXX)'
    };
  }
  
  return {
    isValid: true,
    normalized: normalizeWhatsAppNumber(phoneNumber)
  };
}

/**
 * Generate WhatsApp URL untuk chat
 * @param phoneNumber Nomor WhatsApp
 * @param message Pesan yang akan dikirim (opsional)
 * @returns string - URL WhatsApp
 */
export function generateWhatsAppURL(phoneNumber: string, message?: string): string {
  const urlNumber = getWhatsAppUrlNumber(phoneNumber);
  const baseUrl = `https://wa.me/${urlNumber}`;
  
  if (message) {
    const encodedMessage = encodeURIComponent(message);
    return `${baseUrl}?text=${encodedMessage}`;
  }
  
  return baseUrl;
}
