import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Menggabungkan class names dengan clsx dan tailwind-merge
 * Berguna untuk membuat conditional classes di Tailwind CSS
 * 
 * Optimasi:
 * - Menggunakan inline cache pada level aplikasi
 * - Mengh<PERSON>ri penggunaan fungsi ini dalam render loops
 * - Untuk performa maksimal, gunakan dengan useMemo di komponen React
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
} 
