"use client";

import { useState, useEffect } from "react";
import { formatDate } from "@/lib/utils/format";

// Definisi tipe yang sesuai dengan data yang digunakan
interface RentalWithRelations {
  id: string;
  status: string;
  startDate: Date | string;
  endDate?: Date | string;
  product?: {
    name: string;
    capacity?: number;
  };
}

interface UserRentalsProps {
  userId: string;
}

export function UserRentals({ userId }: UserRentalsProps) {
  const [rentals, setRentals] = useState<RentalWithRelations[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchRentals() {
      try {
        const response = await fetch(`/api/users/${userId}/rentals`);
        if (!response.ok) throw new Error('Failed to fetch rentals');
        const data = await response.json();
        setRentals(data);
      } catch (error) {
        console.error('Error fetching rentals:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchRentals();
  }, [userId]);

  if (isLoading) {
    return <div className="dark:text-gray-300">Loading...</div>;
  }

  if (!rentals.length) {
    return <div className="dark:text-gray-300">Belum ada riwayat penyewaan</div>;
  }

  return (
    <div>
      <ul className="divide-y divide-gray-200 dark:divide-gray-700">
        {rentals.map((rental) => (
          <li key={rental.id} className="py-4">
            <div className="font-medium dark:text-white">{rental.product?.name || 'Produk tidak tersedia'}</div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              {rental.startDate && formatDate(new Date(rental.startDate))}
              {rental.endDate && ` - ${formatDate(new Date(rental.endDate))}`}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Status: {rental.status}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
