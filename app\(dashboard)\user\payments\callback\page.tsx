import { redirect } from "next/navigation";
import { Suspense } from "react";
import { prisma } from "@/lib/config/prisma";
import { getSession } from "@/lib/auth/server";
import { PaymentStatus } from "@prisma/client";
import { WhatsAppService } from "@/lib/services/whatsapp";

interface CallbackPageProps {
  searchParams: Promise<{
    order_id?: string;
    status?: string;
    transaction_status?: string;
  }>;
}

async function CallbackHandler({ searchParams }: CallbackPageProps) {
  const { order_id: orderId, status, transaction_status } = await searchParams;

  console.log('[CALLBACK] Received callback:', { orderId, status, transaction_status });

  if (!orderId) {
    // Redirect to payments page if no order ID
    redirect('/user/payments');
  }

  // Parse the order ID to extract rental ID and payment type
  // Format: {rentalId}_{paymentType}_{timestamp}
  const orderParts = orderId.split('_');
  if (orderParts.length < 2) {
    // Invalid order ID format, redirect to payments page
    redirect('/user/payments');
  }

  const rentalId = orderParts[0];
  const paymentType = orderParts[1]; // 'deposit', 'remaining', or 'full'

  console.log('[CALLBACK] Parsed data:', { rentalId, paymentType });

  // Update payment status if payment is successful
  if (status === 'success' && transaction_status === 'settlement') {
    console.log('[CALLBACK] Payment successful, updating status...');
    try {
      const session = await getSession();
      if (session?.user) {
        // Update payment status based on payment type
        let newStatus: PaymentStatus = PaymentStatus.DEPOSIT_PENDING;
        if (paymentType === 'deposit') {
          newStatus = PaymentStatus.DEPOSIT_PAID;
        } else if (paymentType === 'remaining') {
          newStatus = PaymentStatus.FULLY_PAID;
        } else if (paymentType === 'full') {
          newStatus = PaymentStatus.FULLY_PAID;
        }

        // Update payment status with rental and user data
        const payment = await prisma.payment.update({
          where: { rentalId },
          data: {
            status: newStatus,
            transactionId: orderId
          },
          include: {
            rental: {
              include: {
                user: {
                  select: {
                    name: true,
                    phone: true,
                    email: true
                  }
                },
                product: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        });

        // Update rental status if deposit is paid
        if (newStatus === PaymentStatus.DEPOSIT_PAID) {
          await prisma.rental.update({
            where: { id: rentalId },
            data: {
              status: "ACTIVE",
              paymentStatus: "DEPOSIT_PAID"
            }
          });

          // Send WhatsApp notification for deposit payment
          console.log(`[CALLBACK] Sending WhatsApp notification for deposit payment: ${rentalId}`);

          try {
            await WhatsAppService.sendAdminDepositNotification(
              rentalId,
              payment.rental.user.name || 'Unknown Customer',
              payment.rental.user.phone || 'No phone',
              payment.rental.user.email || 'No email',
              payment.rental.product.name,
              payment.deposit || 0,
              new Date()
            );

            console.log(`✅ WhatsApp admin notification sent for deposit payment: ${rentalId}`);
          } catch (whatsappError) {
            console.error(`❌ Failed to send WhatsApp notification for deposit payment ${rentalId}:`, whatsappError);
          }

        } else if (newStatus === PaymentStatus.FULLY_PAID) {
          await prisma.rental.update({
            where: { id: rentalId },
            data: {
              status: "COMPLETED",
              paymentStatus: "FULLY_PAID"
            }
          });
        }

        console.log(`[CALLBACK] Payment status updated: ${rentalId} -> ${newStatus}`);
      }
    } catch (error) {
      console.error('[CALLBACK] Error updating payment status:', error);
    }
  }

  // Only redirect for pending payments
  if (status === 'pending') {
    redirect(`/user/payments/pending/${rentalId}`);
  }

  // For success and error, redirect to a page that can show GSAP animation
  // We need to redirect to trigger the animation system properly
  if (status === 'success') {
    redirect(`/user/payments?payment_success=${rentalId}&type=${paymentType}`);
  } else {
    redirect(`/user/payments?payment_error=${rentalId}&type=${paymentType}`);
  }
}

export default function CallbackPage({ searchParams }: CallbackPageProps) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memproses pembayaran...</p>
        </div>
      </div>
    }>
      {/* @ts-expect-error Async Server Component */}
      <CallbackHandler searchParams={searchParams} />
    </Suspense>
  );
}

