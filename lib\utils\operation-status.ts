/**
 * Unified operation status utilities for consistent status handling across the application
 */

export type OperationStatus = "pending" | "running" | "completed" | "cancelled";

export interface OperationStatusInfo {
  status: OperationStatus;
  label: string;
  description: string;
  priority: number; // For sorting: 1 = highest priority (top), 3 = lowest priority (bottom)
  color: {
    bg: string;
    text: string;
    border: string;
  };
}

/**
 * Determines the operation status based on rental data
 */
export function getOperationStatus(rental: {
  operationalStart?: Date | string | null;
  operationalEnd?: Date | string | null;
  status?: string;
}): OperationStatus {
  // If rental is cancelled
  if (rental.status === "CANCELLED") {
    return "cancelled";
  }
  
  // If operation has ended
  if (rental.operationalEnd) {
    return "completed";
  }
  
  // If operation has started but not ended
  if (rental.operationalStart) {
    return "running";
  }
  
  // If operation hasn't started yet
  return "pending";
}

/**
 * Gets comprehensive status information for display
 */
export function getOperationStatusInfo(status: OperationStatus): OperationStatusInfo {
  const statusMap: Record<OperationStatus, OperationStatusInfo> = {
    pending: {
      status: "pending",
      label: "Menunggu Operasi",
      description: "Menunggu tim teknisi memulai operasi",
      priority: 1, // Highest priority - show at top
      color: {
        bg: "bg-yellow-500",
        text: "text-yellow-800 dark:text-yellow-300",
        border: "border-yellow-500"
      }
    },
    running: {
      status: "running",
      label: "Sedang Beroperasi",
      description: "Operasi sedang berjalan",
      priority: 2, // Middle priority
      color: {
        bg: "bg-green-500",
        text: "text-green-800 dark:text-green-300",
        border: "border-green-500"
      }
    },
    completed: {
      status: "completed",
      label: "Operasi Selesai",
      description: "Operasi telah selesai",
      priority: 3, // Lowest priority - show at bottom
      color: {
        bg: "bg-blue-500",
        text: "text-blue-800 dark:text-blue-300",
        border: "border-blue-500"
      }
    },
    cancelled: {
      status: "cancelled",
      label: "Dibatalkan",
      description: "Operasi dibatalkan",
      priority: 3, // Same as completed
      color: {
        bg: "bg-red-500",
        text: "text-red-800 dark:text-red-300",
        border: "border-red-500"
      }
    }
  };

  return statusMap[status];
}

/**
 * Sorts operations by status priority (pending first, completed last)
 */
export function sortOperationsByStatus<T extends { operationalStart?: Date | string | null; operationalEnd?: Date | string | null; status?: string }>(
  operations: T[]
): T[] {
  return operations.sort((a, b) => {
    const statusA = getOperationStatus(a);
    const statusB = getOperationStatus(b);
    const infoA = getOperationStatusInfo(statusA);
    const infoB = getOperationStatusInfo(statusB);
    
    // Sort by priority (1 = top, 3 = bottom)
    if (infoA.priority !== infoB.priority) {
      return infoA.priority - infoB.priority;
    }
    
    // If same priority, sort by operational start date (newest first)
    const dateA = a.operationalStart ? new Date(a.operationalStart).getTime() : 0;
    const dateB = b.operationalStart ? new Date(b.operationalStart).getTime() : 0;
    
    return dateB - dateA;
  });
}

/**
 * Filters operations by status
 */
export function filterOperationsByStatus<T extends { operationalStart?: Date | string | null; operationalEnd?: Date | string | null; status?: string }>(
  operations: T[],
  targetStatus: OperationStatus
): T[] {
  return operations.filter(operation => getOperationStatus(operation) === targetStatus);
}

/**
 * Gets operation counts by status
 */
export function getOperationStatusCounts<T extends { operationalStart?: Date | string | null; operationalEnd?: Date | string | null; status?: string }>(
  operations: T[]
): Record<OperationStatus, number> {
  const counts: Record<OperationStatus, number> = {
    pending: 0,
    running: 0,
    completed: 0,
    cancelled: 0
  };
  
  operations.forEach(operation => {
    const status = getOperationStatus(operation);
    counts[status]++;
  });
  
  return counts;
}
